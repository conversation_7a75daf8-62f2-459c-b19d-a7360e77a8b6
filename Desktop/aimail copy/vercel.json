{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase-url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "AWS_ACCESS_KEY_ID": "@aws-access-key-id", "AWS_SECRET_ACCESS_KEY": "@aws-secret-access-key", "AWS_REGION": "@aws-region", "SES_FROM_EMAIL": "@ses-from-email", "PERPLEXITY_API_KEY": "@perplexity-api-key", "NEXTAUTH_SECRET": "@nextauth-secret", "NEXTAUTH_URL": "@nextauth-url"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}