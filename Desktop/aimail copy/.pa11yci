{"defaults": {"timeout": 30000, "wait": 2000, "chromeLaunchConfig": {"args": ["--no-sandbox", "--disable-setuid-sandbox"]}, "standard": "WCAG2AA", "includeNotices": false, "includeWarnings": true, "ignore": ["color-contrast"]}, "urls": [{"url": "http://localhost:3000/login", "screenCapture": "screenshots/login-a11y.png"}, {"url": "http://localhost:3000/dashboard", "screenCapture": "screenshots/dashboard-a11y.png", "actions": ["set field input[type=email] to <EMAIL>", "set field input[type=password] to testpassword", "click element button[type=submit]", "wait for element h1 to be visible"]}, {"url": "http://localhost:3000/audience", "screenCapture": "screenshots/audience-a11y.png", "actions": ["set field input[type=email] to <EMAIL>", "set field input[type=password] to testpassword", "click element button[type=submit]", "wait for element h1 to be visible"]}, {"url": "http://localhost:3000/campaigns", "screenCapture": "screenshots/campaigns-a11y.png", "actions": ["set field input[type=email] to <EMAIL>", "set field input[type=password] to testpassword", "click element button[type=submit]", "wait for element h1 to be visible"]}, {"url": "http://localhost:3000/analytics", "screenCapture": "screenshots/analytics-a11y.png", "actions": ["set field input[type=email] to <EMAIL>", "set field input[type=password] to testpassword", "click element button[type=submit]", "wait for element h1 to be visible"]}, {"url": "http://localhost:3000/settings", "screenCapture": "screenshots/settings-a11y.png", "actions": ["set field input[type=email] to <EMAIL>", "set field input[type=password] to testpassword", "click element button[type=submit]", "wait for element h1 to be visible"]}]}