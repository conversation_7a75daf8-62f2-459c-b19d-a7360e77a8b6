#!/usr/bin/env node

/**
 * Demo Data Creation Script
 * 
 * This script creates sample campaigns and contacts for testing the email marketing platform.
 * Run this after setting up your database and authentication.
 * 
 * Usage:
 *   node scripts/create-demo-data.js
 * 
 * Prerequisites:
 *   - Database is set up and running
 *   - User is authenticated (or you have a user ID)
 *   - Environment variables are configured
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const demoContacts = [
  { email: '<EMAIL>', name: '<PERSON>', status: 'active' },
  { email: '<EMAIL>', name: '<PERSON>', status: 'active' },
  { email: '<EMAIL>', name: '<PERSON>', status: 'active' },
  { email: '<EMAIL>', name: '<PERSON>', status: 'active' },
  { email: 'charlie.w<PERSON><PERSON>@example.com', name: '<PERSON>', status: 'active' },
  { email: '<EMAIL>', name: '<PERSON> <PERSON>', status: 'unsubscribed' },
  { email: '<EMAIL>', name: '<PERSON> <PERSON>', status: 'active' },
  { email: '<EMAIL>', name: '<PERSON> <PERSON>', status: 'active' },
  { email: '<EMAIL>', name: 'George <PERSON>', status: 'bounced' },
  { email: '<EMAIL>', name: 'Helen Anderson', status: 'active' },
  { email: '<EMAIL>', name: 'Ivan Rodriguez', status: 'active' },
  { email: '<EMAIL>', name: 'Julia Thompson', status: 'active' },
  { email: '<EMAIL>', name: 'Kevin White', status: 'active' },
  { email: '<EMAIL>', name: 'Laura Harris', status: 'active' },
  { email: '<EMAIL>', name: 'Mike Clark', status: 'active' }
];

const demoCampaigns = [
  {
    name: 'Welcome Series - Email 1',
    subject: 'Welcome to our platform! 🎉',
    html_body: `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Email</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #3b82f6; margin-bottom: 10px; font-size: 32px;">Welcome to EmailFlow!</h1>
            <p style="color: #64748b; font-size: 18px; margin: 0;">We're excited to have you on board</p>
        </div>
        
        <div style="background: #f0f9ff; padding: 25px; border-radius: 8px; margin-bottom: 25px; border-left: 4px solid #3b82f6;">
            <h2 style="color: #1e293b; margin-top: 0; font-size: 24px;">Getting Started</h2>
            <p style="color: #475569; line-height: 1.6; margin-bottom: 15px;">
                Thank you for joining our email marketing platform. Here are a few things you can do to get started:
            </p>
            <ul style="color: #475569; line-height: 1.8; padding-left: 20px;">
                <li>Import your contact lists via CSV</li>
                <li>Create your first email campaign</li>
                <li>Customize your branding and colors</li>
                <li>Set up your sending domain with Amazon SES</li>
                <li>Explore our AI-powered content generation</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://yourplatform.com/dashboard" style="background: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
                Go to Dashboard
            </a>
        </div>
        
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #92400e; margin-top: 0;">💡 Pro Tip</h3>
            <p style="color: #78350f; line-height: 1.6; margin: 0;">
                Start with a small test campaign to familiarize yourself with the platform before sending to your entire list.
            </p>
        </div>
        
        <div style="border-top: 1px solid #e2e8f0; padding-top: 25px; margin-top: 30px; text-align: center;">
            <p style="color: #94a3b8; font-size: 14px; margin-bottom: 15px;">
                If you have any questions, feel free to <a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;">contact our support team</a>.
            </p>
            <p style="color: #94a3b8; font-size: 12px; margin: 0;">
                You received this email because you signed up for our platform. 
                <a href="#" style="color: #3b82f6; text-decoration: none;">Unsubscribe</a> | 
                <a href="#" style="color: #3b82f6; text-decoration: none;">Update preferences</a>
            </p>
        </div>
    </div>
</body>
</html>`,
    text_body: `Welcome to EmailFlow!

We're excited to have you on board.

Getting Started
===============

Thank you for joining our email marketing platform. Here are a few things you can do to get started:

• Import your contact lists via CSV
• Create your first email campaign  
• Customize your branding and colors
• Set up your sending domain with Amazon SES
• Explore our AI-powered content generation

Go to Dashboard: https://yourplatform.com/dashboard

💡 Pro Tip: Start with a small test campaign to familiarize yourself with the platform before sending to your entire list.

If you have any questions, feel free to contact our support <NAME_EMAIL>.

You received this email because you signed up for our platform. 
Unsubscribe: [unsubscribe_link] | Update preferences: [preferences_link]`,
    status: 'draft'
  },
  {
    name: 'Monthly Newsletter - March 2024',
    subject: 'Your Monthly Update: New Features & Tips 📧',
    html_body: `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Newsletter</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #3b82f6; margin-bottom: 10px; font-size: 28px;">Monthly Newsletter</h1>
            <p style="color: #64748b; font-size: 16px; margin: 0;">March 2024 Edition</p>
        </div>
        
        <div style="margin-bottom: 30px;">
            <h2 style="color: #1e293b; font-size: 22px;">🚀 New Features This Month</h2>
            <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6;">
                <h3 style="color: #1e293b; margin-top: 0; font-size: 18px;">AI-Powered Email Generation</h3>
                <p style="color: #475569; line-height: 1.6; margin: 0;">
                    Generate compelling email content with our new AI assistant. Simply describe your campaign goals and let AI create subject lines and email content for you.
                </p>
            </div>
        </div>
        
        <div style="margin-bottom: 30px;">
            <h2 style="color: #1e293b; font-size: 22px;">📊 Platform Statistics</h2>
            <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                <div style="flex: 1; background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 28px; font-weight: bold; color: #3b82f6; margin-bottom: 5px;">2.5M+</div>
                    <div style="color: #64748b; font-size: 14px;">Emails Sent</div>
                </div>
                <div style="flex: 1; background: #f8fafc; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e2e8f0;">
                    <div style="font-size: 28px; font-weight: bold; color: #10b981; margin-bottom: 5px;">98.5%</div>
                    <div style="color: #64748b; font-size: 14px;">Delivery Rate</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://yourplatform.com/campaigns/new" style="background: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">
                Create New Campaign
            </a>
        </div>
    </div>
</body>
</html>`,
    text_body: `Monthly Newsletter - March 2024 Edition

🚀 New Features This Month

AI-Powered Email Generation
Generate compelling email content with our new AI assistant.

📊 Platform Statistics
• 2.5M+ Emails Sent
• 98.5% Delivery Rate

Create New Campaign: https://yourplatform.com/campaigns/new`,
    status: 'sent'
  }
];

async function createDemoData(userId) {
  console.log('Creating demo data for user:', userId);
  
  try {
    // Create demo contacts
    console.log('Creating demo contacts...');
    const contactsToInsert = demoContacts.map(contact => ({
      ...contact,
      user_id: userId
    }));
    
    const { data: contacts, error: contactsError } = await supabase
      .from('contacts')
      .insert(contactsToInsert)
      .select();
    
    if (contactsError) {
      console.error('Error creating contacts:', contactsError);
      return;
    }
    
    console.log(`✅ Created ${contacts.length} demo contacts`);
    
    // Create demo campaigns
    console.log('Creating demo campaigns...');
    const campaignsToInsert = demoCampaigns.map(campaign => ({
      ...campaign,
      user_id: userId
    }));
    
    const { data: campaigns, error: campaignsError } = await supabase
      .from('campaigns')
      .insert(campaignsToInsert)
      .select();
    
    if (campaignsError) {
      console.error('Error creating campaigns:', campaignsError);
      return;
    }
    
    console.log(`✅ Created ${campaigns.length} demo campaigns`);
    
    // Create some sample suppression entries
    console.log('Creating sample suppression entries...');
    const suppressionEntries = [
      { user_id: userId, email: '<EMAIL>', reason: 'bounce' },
      { user_id: userId, email: '<EMAIL>', reason: 'complaint' },
      { user_id: userId, email: '<EMAIL>', reason: 'unsubscribe' }
    ];
    
    const { error: suppressionError } = await supabase
      .from('suppression')
      .insert(suppressionEntries);
    
    if (suppressionError) {
      console.error('Error creating suppression entries:', suppressionError);
    } else {
      console.log('✅ Created sample suppression entries');
    }
    
    console.log('\n🎉 Demo data creation completed successfully!');
    console.log('\nYou can now:');
    console.log('- View contacts in the Audience section');
    console.log('- Edit and send the demo campaigns');
    console.log('- Test the email sending functionality');
    console.log('- Explore the analytics dashboard');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

async function main() {
  // Get user ID from command line argument or prompt
  const userId = process.argv[2];
  
  if (!userId) {
    console.error('❌ Please provide a user ID as an argument');
    console.log('Usage: node scripts/create-demo-data.js <user-id>');
    console.log('\nTo get your user ID:');
    console.log('1. Log into your application');
    console.log('2. Open browser dev tools');
    console.log('3. Go to Application > Local Storage');
    console.log('4. Look for the Supabase auth token and decode it');
    console.log('5. Or check your Supabase Auth dashboard');
    process.exit(1);
  }
  
  // Validate environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Missing required environment variables');
    console.log('Please ensure you have set:');
    console.log('- NEXT_PUBLIC_SUPABASE_URL');
    console.log('- SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }
  
  await createDemoData(userId);
}

if (require.main === module) {
  main();
}

module.exports = { createDemoData, demoContacts, demoCampaigns };