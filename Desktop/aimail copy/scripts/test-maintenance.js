#!/usr/bin/env node

/**
 * Test Maintenance Script
 * 
 * This script helps maintain the test suite by:
 * - Cleaning up test artifacts
 * - Generating test reports
 * - Updating test data
 * - Checking test health
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const COMMANDS = {
  cleanup: cleanupTestArtifacts,
  report: generateTestReport,
  health: checkTestHealth,
  data: updateTestData,
  coverage: analyzeCoverage,
  performance: checkPerformanceRegression
};

function main() {
  const command = process.argv[2];
  
  if (!command || !COMMANDS[command]) {
    console.log('Usage: node scripts/test-maintenance.js <command>');
    console.log('Commands:');
    console.log('  cleanup    - Clean up test artifacts');
    console.log('  report     - Generate test reports');
    console.log('  health     - Check test suite health');
    console.log('  data       - Update test data');
    console.log('  coverage   - Analyze test coverage');
    console.log('  performance - Check for performance regressions');
    process.exit(1);
  }
  
  try {
    COMMANDS[command]();
  } catch (error) {
    console.error(`Error executing ${command}:`, error.message);
    process.exit(1);
  }
}

function cleanupTestArtifacts() {
  console.log('🧹 Cleaning up test artifacts...');
  
  const artifactDirs = [
    'coverage',
    'test-results',
    'playwright-report',
    'screenshots',
    '.nyc_output'
  ];
  
  artifactDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`  ✓ Removed ${dir}`);
    }
  });
  
  // Clean up temporary test files
  const tempFiles = [
    'test-contacts.csv',
    'load-test-results.json',
    'accessibility-report.json'
  ];
  
  tempFiles.forEach(file => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`  ✓ Removed ${file}`);
    }
  });
  
  console.log('✅ Cleanup completed');
}

function generateTestReport() {
  console.log('📊 Generating test reports...');
  
  try {
    // Generate coverage report
    console.log('  📈 Generating coverage report...');
    execSync('npm run test:coverage -- --silent', { stdio: 'inherit' });
    
    // Generate E2E test report
    console.log('  🎭 Generating E2E test report...');
    execSync('npx playwright test --reporter=html', { stdio: 'inherit' });
    
    // Generate accessibility report
    console.log('  ♿ Generating accessibility report...');
    try {
      execSync('npm run test:accessibility -- --reporter json > accessibility-report.json', { stdio: 'inherit' });
    } catch (error) {
      console.log('    ⚠️  Accessibility tests require running application');
    }
    
    console.log('✅ Test reports generated');
    console.log('  📁 Coverage: coverage/lcov-report/index.html');
    console.log('  📁 E2E: playwright-report/index.html');
    console.log('  📁 Accessibility: accessibility-report.json');
    
  } catch (error) {
    console.error('❌ Error generating reports:', error.message);
  }
}

function checkTestHealth() {
  console.log('🏥 Checking test suite health...');
  
  const healthChecks = [
    checkTestCoverage,
    checkTestPerformance,
    checkTestStability,
    checkTestDependencies
  ];
  
  const results = healthChecks.map(check => {
    try {
      return check();
    } catch (error) {
      return { status: 'error', message: error.message };
    }
  });
  
  const passed = results.filter(r => r.status === 'pass').length;
  const failed = results.filter(r => r.status === 'fail').length;
  const errors = results.filter(r => r.status === 'error').length;
  
  console.log(`\n📋 Health Check Summary:`);
  console.log(`  ✅ Passed: ${passed}`);
  console.log(`  ❌ Failed: ${failed}`);
  console.log(`  🚨 Errors: ${errors}`);
  
  if (failed > 0 || errors > 0) {
    console.log('\n🔍 Issues found:');
    results.forEach((result, index) => {
      if (result.status !== 'pass') {
        console.log(`  ${index + 1}. ${result.message}`);
      }
    });
  }
}

function checkTestCoverage() {
  try {
    const coverageFile = path.join('coverage', 'coverage-summary.json');
    if (!fs.existsSync(coverageFile)) {
      return { status: 'fail', message: 'Coverage report not found. Run npm run test:coverage first.' };
    }
    
    const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    const totalCoverage = coverage.total.lines.pct;
    
    if (totalCoverage < 80) {
      return { status: 'fail', message: `Test coverage is ${totalCoverage}%, below 80% threshold` };
    }
    
    return { status: 'pass', message: `Test coverage is ${totalCoverage}%` };
  } catch (error) {
    return { status: 'error', message: `Error checking coverage: ${error.message}` };
  }
}

function checkTestPerformance() {
  try {
    // Check if performance tests exist and run quickly
    const performanceTestDir = path.join('tests', 'performance');
    if (!fs.existsSync(performanceTestDir)) {
      return { status: 'fail', message: 'Performance tests directory not found' };
    }
    
    const testFiles = fs.readdirSync(performanceTestDir).filter(f => f.endsWith('.test.ts'));
    if (testFiles.length === 0) {
      return { status: 'fail', message: 'No performance test files found' };
    }
    
    return { status: 'pass', message: `Found ${testFiles.length} performance test files` };
  } catch (error) {
    return { status: 'error', message: `Error checking performance tests: ${error.message}` };
  }
}

function checkTestStability() {
  try {
    // Check for common flaky test patterns
    const testFiles = getAllTestFiles();
    let flakyPatterns = 0;
    
    const patterns = [
      /setTimeout\(/g,
      /setInterval\(/g,
      /Math\.random\(/g,
      /new Date\(\)/g
    ];
    
    testFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      patterns.forEach(pattern => {
        if (pattern.test(content)) {
          flakyPatterns++;
        }
      });
    });
    
    if (flakyPatterns > 5) {
      return { status: 'fail', message: `Found ${flakyPatterns} potential flaky test patterns` };
    }
    
    return { status: 'pass', message: 'No major flaky test patterns detected' };
  } catch (error) {
    return { status: 'error', message: `Error checking test stability: ${error.message}` };
  }
}

function checkTestDependencies() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const testDeps = [
      '@testing-library/react',
      '@testing-library/jest-dom',
      '@playwright/test',
      'jest'
    ];
    
    const missing = testDeps.filter(dep => 
      !packageJson.devDependencies[dep] && !packageJson.dependencies[dep]
    );
    
    if (missing.length > 0) {
      return { status: 'fail', message: `Missing test dependencies: ${missing.join(', ')}` };
    }
    
    return { status: 'pass', message: 'All required test dependencies are installed' };
  } catch (error) {
    return { status: 'error', message: `Error checking dependencies: ${error.message}` };
  }
}

function updateTestData() {
  console.log('🔄 Updating test data...');
  
  try {
    // Generate fresh test contacts CSV
    const testContacts = generateTestContacts(1000);
    const csvContent = 'email,name\n' + testContacts.map(c => `${c.email},${c.name}`).join('\n');
    
    const fixturesDir = path.join('tests', 'fixtures');
    if (!fs.existsSync(fixturesDir)) {
      fs.mkdirSync(fixturesDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(fixturesDir, 'test-contacts.csv'), csvContent);
    console.log('  ✓ Generated test-contacts.csv with 1000 contacts');
    
    // Generate test campaign data
    const testCampaigns = generateTestCampaigns(50);
    fs.writeFileSync(
      path.join(fixturesDir, 'test-campaigns.json'),
      JSON.stringify(testCampaigns, null, 2)
    );
    console.log('  ✓ Generated test-campaigns.json with 50 campaigns');
    
    // Generate test analytics data
    const testAnalytics = generateTestAnalytics();
    fs.writeFileSync(
      path.join(fixturesDir, 'test-analytics.json'),
      JSON.stringify(testAnalytics, null, 2)
    );
    console.log('  ✓ Generated test-analytics.json');
    
    console.log('✅ Test data updated');
    
  } catch (error) {
    console.error('❌ Error updating test data:', error.message);
  }
}

function analyzeCoverage() {
  console.log('📊 Analyzing test coverage...');
  
  try {
    const coverageFile = path.join('coverage', 'coverage-summary.json');
    if (!fs.existsSync(coverageFile)) {
      console.log('❌ Coverage report not found. Run npm run test:coverage first.');
      return;
    }
    
    const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    
    console.log('\n📈 Coverage Summary:');
    console.log(`  Lines: ${coverage.total.lines.pct}%`);
    console.log(`  Functions: ${coverage.total.functions.pct}%`);
    console.log(`  Branches: ${coverage.total.branches.pct}%`);
    console.log(`  Statements: ${coverage.total.statements.pct}%`);
    
    // Find files with low coverage
    const lowCoverageFiles = [];
    Object.entries(coverage).forEach(([file, data]) => {
      if (file !== 'total' && data.lines.pct < 80) {
        lowCoverageFiles.push({ file, coverage: data.lines.pct });
      }
    });
    
    if (lowCoverageFiles.length > 0) {
      console.log('\n⚠️  Files with low coverage (<80%):');
      lowCoverageFiles
        .sort((a, b) => a.coverage - b.coverage)
        .forEach(({ file, coverage }) => {
          console.log(`  ${file}: ${coverage}%`);
        });
    }
    
  } catch (error) {
    console.error('❌ Error analyzing coverage:', error.message);
  }
}

function checkPerformanceRegression() {
  console.log('⚡ Checking for performance regressions...');
  
  try {
    // This would typically compare against baseline metrics
    // For now, we'll just run performance tests and check execution time
    
    const startTime = Date.now();
    execSync('npm run test:performance', { stdio: 'pipe' });
    const endTime = Date.now();
    
    const executionTime = endTime - startTime;
    const baselineTime = 30000; // 30 seconds baseline
    
    if (executionTime > baselineTime * 1.2) {
      console.log(`⚠️  Performance tests took ${executionTime}ms, which is 20% slower than baseline`);
    } else {
      console.log(`✅ Performance tests completed in ${executionTime}ms (within acceptable range)`);
    }
    
  } catch (error) {
    console.error('❌ Error checking performance:', error.message);
  }
}

// Helper functions
function getAllTestFiles() {
  const testFiles = [];
  
  function findTestFiles(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        findTestFiles(filePath);
      } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx') || file.endsWith('.spec.ts')) {
        testFiles.push(filePath);
      }
    });
  }
  
  findTestFiles('src');
  findTestFiles('tests');
  
  return testFiles;
}

function generateTestContacts(count) {
  const contacts = [];
  for (let i = 0; i < count; i++) {
    contacts.push({
      email: `test${i}@example.com`,
      name: `Test User ${i}`
    });
  }
  return contacts;
}

function generateTestCampaigns(count) {
  const campaigns = [];
  for (let i = 0; i < count; i++) {
    campaigns.push({
      id: `campaign-${i}`,
      name: `Test Campaign ${i}`,
      subject: `Test Subject ${i}`,
      html_body: `<h1>Test Campaign ${i}</h1><p>This is test content.</p>`,
      text_body: `Test Campaign ${i}\n\nThis is test content.`,
      status: ['draft', 'sent', 'scheduled'][i % 3]
    });
  }
  return campaigns;
}

function generateTestAnalytics() {
  return {
    totalContacts: 1000,
    totalCampaigns: 50,
    totalSends: 25000,
    averageOpenRate: 0.25,
    averageClickRate: 0.05,
    averageBounceRate: 0.02
  };
}

if (require.main === module) {
  main();
}

module.exports = {
  cleanupTestArtifacts,
  generateTestReport,
  checkTestHealth,
  updateTestData,
  analyzeCoverage,
  checkPerformanceRegression
};