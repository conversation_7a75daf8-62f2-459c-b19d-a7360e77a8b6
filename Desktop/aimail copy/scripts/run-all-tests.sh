#!/bin/bash

# Comprehensive Test Runner Script
# This script runs all types of tests in the correct order

set -e  # Exit on any error

echo "🚀 Starting comprehensive test suite..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if required dependencies are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v npx &> /dev/null; then
        print_error "npx is not installed"
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Install dependencies if needed
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed"
}

# Run linting
run_linting() {
    print_status "Running linting..."
    if npm run lint; then
        print_success "Linting passed"
    else
        print_error "Linting failed"
        exit 1
    fi
}

# Run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    if npm run test:coverage; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        exit 1
    fi
}

# Run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    if npm run test:performance; then
        print_success "Performance tests passed"
    else
        print_warning "Performance tests failed (non-blocking)"
    fi
}

# Build application
build_application() {
    print_status "Building application..."
    if npm run build; then
        print_success "Build successful"
    else
        print_error "Build failed"
        exit 1
    fi
}

# Start application for E2E tests
start_application() {
    print_status "Starting application for E2E tests..."
    npm run start &
    APP_PID=$!
    
    # Wait for application to be ready
    print_status "Waiting for application to be ready..."
    for i in {1..30}; do
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            print_success "Application is ready"
            return 0
        fi
        sleep 2
    done
    
    print_error "Application failed to start"
    kill $APP_PID 2>/dev/null || true
    exit 1
}

# Stop application
stop_application() {
    if [ ! -z "$APP_PID" ]; then
        print_status "Stopping application..."
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
        print_success "Application stopped"
    fi
}

# Run E2E tests
run_e2e_tests() {
    print_status "Installing Playwright browsers..."
    npx playwright install --with-deps
    
    print_status "Running E2E tests..."
    if npm run test:e2e; then
        print_success "E2E tests passed"
    else
        print_error "E2E tests failed"
        return 1
    fi
}

# Run accessibility tests
run_accessibility_tests() {
    print_status "Running accessibility tests..."
    if npm run test:accessibility; then
        print_success "Accessibility tests passed"
    else
        print_warning "Accessibility tests failed (non-blocking)"
    fi
}

# Run load tests (optional)
run_load_tests() {
    if [ "$RUN_LOAD_TESTS" = "true" ]; then
        print_status "Running load tests..."
        if npm run test:load; then
            print_success "Load tests passed"
        else
            print_warning "Load tests failed (non-blocking)"
        fi
    else
        print_status "Skipping load tests (set RUN_LOAD_TESTS=true to enable)"
    fi
}

# Generate test reports
generate_reports() {
    print_status "Generating test reports..."
    if node scripts/test-maintenance.js report; then
        print_success "Test reports generated"
    else
        print_warning "Failed to generate some reports"
    fi
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    stop_application
    
    # Clean up test artifacts
    node scripts/test-maintenance.js cleanup 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Set up trap to ensure cleanup on exit
trap cleanup EXIT

# Main execution
main() {
    print_status "Starting comprehensive test suite"
    
    # Parse command line arguments
    SKIP_DEPS=false
    SKIP_BUILD=false
    SKIP_E2E=false
    RUN_LOAD_TESTS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-e2e)
                SKIP_E2E=true
                shift
                ;;
            --load-tests)
                RUN_LOAD_TESTS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --skip-deps    Skip dependency installation"
                echo "  --skip-build   Skip application build"
                echo "  --skip-e2e     Skip E2E tests"
                echo "  --load-tests   Run load tests"
                echo "  --help         Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run test phases
    check_dependencies
    
    if [ "$SKIP_DEPS" = false ]; then
        install_dependencies
    fi
    
    run_linting
    run_unit_tests
    run_performance_tests
    
    if [ "$SKIP_BUILD" = false ]; then
        build_application
    fi
    
    if [ "$SKIP_E2E" = false ]; then
        start_application
        run_e2e_tests
        run_accessibility_tests
        stop_application
    fi
    
    run_load_tests
    generate_reports
    
    print_success "All tests completed successfully! 🎉"
    
    # Print summary
    echo ""
    echo "📊 Test Summary:"
    echo "  ✅ Linting: Passed"
    echo "  ✅ Unit Tests: Passed"
    echo "  ✅ Performance Tests: Completed"
    echo "  ✅ Build: Successful"
    if [ "$SKIP_E2E" = false ]; then
        echo "  ✅ E2E Tests: Passed"
        echo "  ✅ Accessibility Tests: Completed"
    fi
    if [ "$RUN_LOAD_TESTS" = "true" ]; then
        echo "  ✅ Load Tests: Completed"
    fi
    echo "  ✅ Reports: Generated"
    echo ""
    echo "📁 Test artifacts:"
    echo "  - Coverage report: coverage/lcov-report/index.html"
    echo "  - E2E report: playwright-report/index.html"
    echo "  - Accessibility screenshots: screenshots/"
    echo ""
}

# Run main function
main "$@"