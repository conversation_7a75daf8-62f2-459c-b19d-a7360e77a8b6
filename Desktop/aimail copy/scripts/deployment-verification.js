#!/usr/bin/env node

/**
 * Deployment Verification Script
 * 
 * This script verifies that a deployment is working correctly by running
 * a series of tests against the deployed application.
 * 
 * Usage:
 *   node scripts/deployment-verification.js <base-url>
 * 
 * Example:
 *   node scripts/deployment-verification.js https://your-app.vercel.app
 */

const https = require('https');
const http = require('http');

class DeploymentVerifier {
  constructor(baseUrl) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(name, testFn) {
    console.log(`🧪 Running test: ${name}`);
    
    try {
      const result = await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASS', message: result });
      console.log(`✅ PASS: ${name}`);
      return true;
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ 
        name, 
        status: 'FAIL', 
        message: error.message,
        error: error.stack 
      });
      console.log(`❌ FAIL: ${name} - ${error.message}`);
      return false;
    }
  }

  async makeRequest(path, options = {}) {
    return new Promise((resolve, reject) => {
      const url = `${this.baseUrl}${path}`;
      const client = url.startsWith('https:') ? https : http;
      
      const req = client.request(url, {
        method: options.method || 'GET',
        headers: {
          'User-Agent': 'Deployment-Verifier/1.0',
          ...options.headers
        },
        timeout: 10000
      }, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }

  async testHealthEndpoint() {
    const response = await this.makeRequest('/api/health');
    
    if (response.statusCode !== 200) {
      throw new Error(`Health check failed with status ${response.statusCode}`);
    }
    
    const health = JSON.parse(response.body);
    
    if (health.status !== 'healthy') {
      throw new Error(`Application is unhealthy: ${JSON.stringify(health.checks)}`);
    }
    
    return 'Health check passed - application is healthy';
  }

  async testHomePage() {
    const response = await this.makeRequest('/');
    
    if (response.statusCode !== 200) {
      throw new Error(`Home page returned status ${response.statusCode}`);
    }
    
    if (!response.body.includes('<!DOCTYPE html>')) {
      throw new Error('Home page does not contain valid HTML');
    }
    
    return 'Home page loads successfully';
  }

  async testLoginPage() {
    const response = await this.makeRequest('/login');
    
    if (response.statusCode !== 200) {
      throw new Error(`Login page returned status ${response.statusCode}`);
    }
    
    if (!response.body.includes('login') && !response.body.includes('sign in')) {
      throw new Error('Login page does not contain expected content');
    }
    
    return 'Login page loads successfully';
  }

  async testApiRoutes() {
    const routes = [
      '/api/health',
      '/api/campaigns',
      '/api/contacts',
      '/api/settings'
    ];
    
    const results = [];
    
    for (const route of routes) {
      try {
        const response = await this.makeRequest(route);
        
        // Most API routes should return 401 (unauthorized) when not authenticated
        // or 200 for public endpoints like health
        if (response.statusCode === 200 || response.statusCode === 401) {
          results.push(`${route}: OK`);
        } else {
          results.push(`${route}: Unexpected status ${response.statusCode}`);
        }
      } catch (error) {
        results.push(`${route}: Error - ${error.message}`);
      }
    }
    
    return `API routes tested: ${results.join(', ')}`;
  }

  async testStaticAssets() {
    const assets = [
      '/favicon.ico',
      '/_next/static/css', // This might not exist, but we'll check
    ];
    
    let assetsFound = 0;
    
    for (const asset of assets) {
      try {
        const response = await this.makeRequest(asset);
        if (response.statusCode === 200) {
          assetsFound++;
        }
      } catch (error) {
        // Static assets might not be available, this is not critical
      }
    }
    
    return `Static assets check completed (${assetsFound} assets found)`;
  }

  async testSecurityHeaders() {
    const response = await this.makeRequest('/');
    
    const securityHeaders = [
      'x-frame-options',
      'x-content-type-options'
    ];
    
    const missingHeaders = securityHeaders.filter(header => 
      !response.headers[header] && !response.headers[header.toLowerCase()]
    );
    
    if (missingHeaders.length > 0) {
      console.warn(`⚠️  Missing security headers: ${missingHeaders.join(', ')}`);
    }
    
    return `Security headers check completed (${securityHeaders.length - missingHeaders.length}/${securityHeaders.length} present)`;
  }

  async testDatabaseConnectivity() {
    // This is tested through the health endpoint
    const response = await this.makeRequest('/api/health');
    const health = JSON.parse(response.body);
    
    if (health.checks.database.status !== 'healthy') {
      throw new Error(`Database connectivity failed: ${health.checks.database.message}`);
    }
    
    return 'Database connectivity verified through health check';
  }

  async runAllTests() {
    console.log(`🚀 Starting deployment verification for: ${this.baseUrl}\n`);
    
    const tests = [
      ['Health Endpoint', () => this.testHealthEndpoint()],
      ['Home Page', () => this.testHomePage()],
      ['Login Page', () => this.testLoginPage()],
      ['API Routes', () => this.testApiRoutes()],
      ['Static Assets', () => this.testStaticAssets()],
      ['Security Headers', () => this.testSecurityHeaders()],
      ['Database Connectivity', () => this.testDatabaseConnectivity()]
    ];
    
    for (const [name, testFn] of tests) {
      await this.runTest(name, testFn);
      console.log(''); // Add spacing between tests
    }
    
    this.printSummary();
    
    return this.results.failed === 0;
  }

  printSummary() {
    console.log('📊 DEPLOYMENT VERIFICATION SUMMARY');
    console.log('=====================================');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📊 Total:  ${this.results.passed + this.results.failed}`);
    
    if (this.results.failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.tests
        .filter(test => test.status === 'FAIL')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.message}`);
        });
    }
    
    console.log('\n' + (this.results.failed === 0 ? 
      '🎉 All tests passed! Deployment is ready.' : 
      '⚠️  Some tests failed. Please review and fix issues.'));
  }

  generateReport() {
    return {
      timestamp: new Date().toISOString(),
      baseUrl: this.baseUrl,
      summary: {
        passed: this.results.passed,
        failed: this.results.failed,
        total: this.results.passed + this.results.failed,
        success: this.results.failed === 0
      },
      tests: this.results.tests
    };
  }
}

async function main() {
  const baseUrl = process.argv[2];
  
  if (!baseUrl) {
    console.error('❌ Please provide a base URL');
    console.log('Usage: node scripts/deployment-verification.js <base-url>');
    console.log('Example: node scripts/deployment-verification.js https://your-app.vercel.app');
    process.exit(1);
  }
  
  // Validate URL format
  try {
    new URL(baseUrl);
  } catch (error) {
    console.error('❌ Invalid URL format');
    process.exit(1);
  }
  
  const verifier = new DeploymentVerifier(baseUrl);
  const success = await verifier.runAllTests();
  
  // Generate JSON report if requested
  if (process.argv.includes('--json')) {
    const report = verifier.generateReport();
    console.log('\n📄 JSON REPORT:');
    console.log(JSON.stringify(report, null, 2));
  }
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { DeploymentVerifier };