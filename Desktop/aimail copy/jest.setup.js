import '@testing-library/jest-dom'

// Polyfill for Web APIs used by Next.js
import { TextEncoder, TextDecoder } from 'util'
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = jest.fn()
}

// Mock Web APIs for Next.js
const { Blob, File } = require('buffer')
const { ReadableStream, WritableStream, TransformStream } = require('stream/web')

Object.assign(global, {
  Blob,
  File,
  ReadableStream,
  WritableStream,
  TransformStream,
})

// Mock Headers
if (!global.Headers) {
  global.Headers = class Headers {
    constructor(init) {
      this.map = new Map()
      if (init) {
        if (init instanceof Headers) {
          init.forEach((value, key) => this.map.set(key, value))
        } else if (Array.isArray(init)) {
          init.forEach(([key, value]) => this.map.set(key, value))
        } else {
          Object.entries(init).forEach(([key, value]) => this.map.set(key, value))
        }
      }
    }
    
    get(name) {
      return this.map.get(name.toLowerCase()) || null
    }
    
    set(name, value) {
      this.map.set(name.toLowerCase(), value)
    }
    
    has(name) {
      return this.map.has(name.toLowerCase())
    }
    
    delete(name) {
      this.map.delete(name.toLowerCase())
    }
    
    forEach(callback) {
      this.map.forEach(callback)
    }
    
    entries() {
      return this.map.entries()
    }
    
    keys() {
      return this.map.keys()
    }
    
    values() {
      return this.map.values()
    }
  }
}

// Mock Request
if (!global.Request) {
  global.Request = class Request {
    constructor(input, init = {}) {
      this._url = typeof input === 'string' ? input : input.url
      this.method = init.method || 'GET'
      this.headers = new Headers(init.headers)
      this.body = init.body
    }
    
    get url() {
      return this._url
    }
    
    json() {
      return Promise.resolve(JSON.parse(this.body || '{}'))
    }
  }
}

// Mock Response
if (!global.Response) {
  global.Response = class Response {
    constructor(body, init = {}) {
      this.body = body
      this.status = init.status || 200
      this.statusText = init.statusText || 'OK'
      this.headers = new Headers(init.headers)
    }
    
    json() {
      return Promise.resolve(JSON.parse(this.body || '{}'))
    }
    
    static json(data, init = {}) {
      return new Response(JSON.stringify(data), {
        ...init,
        headers: {
          'content-type': 'application/json',
          ...init.headers,
        },
      })
    }
    
    static redirect(url, status = 302) {
      return new Response(null, {
        status,
        headers: {
          location: url,
        },
      })
    }
  }
}

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock Supabase
jest.mock('./src/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithPassword: jest.fn(),
      signUp: jest.fn(),
      signOut: jest.fn(),
      getSession: jest.fn(),
      getUser: jest.fn(),
      onAuthStateChange: jest.fn(() => ({
        data: { subscription: { unsubscribe: jest.fn() } }
      })),
    },
  },
}))