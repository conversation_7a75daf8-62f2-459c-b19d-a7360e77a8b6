{"name": "email-marketing-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:performance": "jest --testPathPattern=performance", "test:accessibility": "pa11y-ci", "test:load": "artillery run tests/load/load-test.yml", "test:all": "npm run test && npm run test:e2e && npm run test:accessibility", "test:ci": "npm run test:coverage && npm run test:e2e && npm run test:accessibility", "test:maintenance": "node scripts/test-maintenance.js", "test:comprehensive": "./scripts/run-all-tests.sh", "deploy:verify": "node scripts/deployment-verification.js", "demo:create": "node scripts/create-demo-data.js", "health:check": "curl -f http://localhost:3000/api/health || exit 1", "postbuild": "echo 'Build completed successfully'"}, "dependencies": {"@aws-sdk/client-ses": "^3.886.0", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isomorphic-dompurify": "^2.26.0", "lucide-react": "^0.544.0", "next": "14.2.15", "react": "^18", "react-dom": "^18", "tailwind-merge": "^3.3.1", "zod": "^4.1.8"}, "devDependencies": {"@axe-core/playwright": "^4.10.0", "@faker-js/faker": "^9.3.0", "@lhci/cli": "^0.12.0", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "artillery": "^2.0.20", "eslint": "^8", "eslint-config-next": "14.2.15", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "lighthouse": "^12.2.1", "pa11y": "^8.0.0", "pa11y-ci": "^3.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}