/**
 * Test Data Fixtures
 * 
 * Centralized test data for consistent testing across all test suites
 */

export const mockUsers = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

export const mockContacts = [
  {
    id: 'contact-1',
    user_id: 'user-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'contact-2',
    user_id: 'user-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'contact-3',
    user_id: 'user-1',
    email: '<EMAIL>',
    name: 'Bounced User',
    status: 'bounced' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

export const mockCampaigns = [
  {
    id: 'campaign-1',
    user_id: 'user-1',
    name: 'Welcome Campaign',
    subject: 'Welcome to our platform!',
    html_body: '<h1>Welcome!</h1><p>Thank you for joining us.</p>',
    text_body: 'Welcome!\n\nThank you for joining us.',
    status: 'draft' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'campaign-2',
    user_id: 'user-1',
    name: 'Newsletter Campaign',
    subject: 'Monthly Newsletter',
    html_body: '<h1>Newsletter</h1><p>Here are this month\'s updates.</p>',
    text_body: 'Newsletter\n\nHere are this month\'s updates.',
    status: 'sent' as const,
    sent_at: '2024-01-15T10:00:00Z',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  }
];

export const mockEmailSends = [
  {
    id: 'send-1',
    campaign_id: 'campaign-2',
    contact_id: 'contact-1',
    status: 'sent' as const,
    ses_message_id: 'ses-msg-1',
    sent_at: '2024-01-15T10:00:00Z',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: 'send-2',
    campaign_id: 'campaign-2',
    contact_id: 'contact-2',
    status: 'sent' as const,
    ses_message_id: 'ses-msg-2',
    sent_at: '2024-01-15T10:00:00Z',
    created_at: '2024-01-15T10:00:00Z'
  }
];

export const mockTrackingEvents = [
  {
    id: 'event-1',
    send_id: 'send-1',
    type: 'open' as const,
    metadata: { user_agent: 'Mozilla/5.0...', ip: '***********' },
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'event-2',
    send_id: 'send-1',
    type: 'click' as const,
    metadata: { url: 'https://example.com', user_agent: 'Mozilla/5.0...' },
    created_at: '2024-01-15T10:45:00Z'
  }
];

export const mockSuppressionList = [
  {
    id: 'suppression-1',
    user_id: 'user-1',
    email: '<EMAIL>',
    reason: 'bounce' as const,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'suppression-2',
    user_id: 'user-1',
    email: '<EMAIL>',
    reason: 'unsubscribe' as const,
    created_at: '2024-01-01T00:00:00Z'
  }
];

export const mockAnalytics = {
  totalContacts: 1000,
  totalCampaigns: 25,
  totalSends: 15000,
  averageOpenRate: 0.24,
  averageClickRate: 0.05,
  averageBounceRate: 0.02,
  recentCampaigns: mockCampaigns,
  topCampaigns: [
    {
      id: 'campaign-2',
      name: 'Newsletter Campaign',
      openRate: 0.35,
      clickRate: 0.08,
      sends: 500
    }
  ],
  timeSeries: [
    { date: '2024-01-01', opens: 120, clicks: 25, sends: 500 },
    { date: '2024-01-02', opens: 135, clicks: 30, sends: 600 },
    { date: '2024-01-03', opens: 110, clicks: 20, sends: 450 }
  ]
};

export const mockSESResponses = {
  sendEmail: {
    MessageId: 'test-message-id-123'
  },
  bounceNotification: {
    Type: 'Notification',
    Message: JSON.stringify({
      notificationType: 'Bounce',
      bounce: {
        bounceType: 'Permanent',
        bounceSubType: 'General',
        bouncedRecipients: [
          {
            emailAddress: '<EMAIL>',
            action: 'failed',
            status: '5.1.1',
            diagnosticCode: 'smtp; 550 5.1.1 User unknown'
          }
        ]
      },
      mail: {
        messageId: 'ses-msg-1',
        timestamp: '2024-01-15T10:00:00.000Z',
        source: '<EMAIL>',
        destination: ['<EMAIL>']
      }
    })
  },
  complaintNotification: {
    Type: 'Notification',
    Message: JSON.stringify({
      notificationType: 'Complaint',
      complaint: {
        complainedRecipients: [
          {
            emailAddress: '<EMAIL>'
          }
        ],
        timestamp: '2024-01-15T10:00:00.000Z',
        feedbackId: 'complaint-feedback-id'
      },
      mail: {
        messageId: 'ses-msg-2',
        timestamp: '2024-01-15T10:00:00.000Z',
        source: '<EMAIL>',
        destination: ['<EMAIL>']
      }
    })
  }
};

export const mockPerplexityResponse = {
  choices: [
    {
      message: {
        content: JSON.stringify({
          subjects: [
            'Exciting News: Our New Product is Here!',
            'Introducing Our Latest Innovation',
            'You\'ve Been Waiting for This...'
          ],
          bodies: [
            {
              html: '<h1>Exciting News!</h1><p>We\'re thrilled to announce our latest product...</p>',
              text: 'Exciting News!\n\nWe\'re thrilled to announce our latest product...'
            },
            {
              html: '<h1>Innovation at Its Best</h1><p>Discover what makes our new product special...</p>',
              text: 'Innovation at Its Best\n\nDiscover what makes our new product special...'
            }
          ]
        })
      }
    }
  ]
};

export const mockCSVData = {
  valid: `email,name
<EMAIL>,John Doe
<EMAIL>,Jane Smith
<EMAIL>,Bob Johnson`,
  
  invalid: `email,name
invalid-email,John Doe
<EMAIL>,Jane Smith
,Bob Johnson`,
  
  large: Array.from({ length: 1000 }, (_, i) => 
    `test${i}@example.com,Test User ${i}`
  ).join('\n')
};

// Helper functions for generating test data
export function generateMockContacts(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: `contact-${i}`,
    user_id: 'user-1',
    email: `test${i}@example.com`,
    name: `Test User ${i}`,
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }));
}

export function generateMockCampaigns(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: `campaign-${i}`,
    user_id: 'user-1',
    name: `Test Campaign ${i}`,
    subject: `Test Subject ${i}`,
    html_body: `<h1>Test Campaign ${i}</h1><p>This is test content.</p>`,
    text_body: `Test Campaign ${i}\n\nThis is test content.`,
    status: (['draft', 'sent', 'scheduled'] as const)[i % 3],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }));
}

export function generateMockEmailSends(campaignId: string, contactIds: string[]) {
  return contactIds.map((contactId, i) => ({
    id: `send-${campaignId}-${i}`,
    campaign_id: campaignId,
    contact_id: contactId,
    status: 'sent' as const,
    ses_message_id: `ses-msg-${campaignId}-${i}`,
    sent_at: '2024-01-15T10:00:00Z',
    created_at: '2024-01-15T10:00:00Z'
  }));
}

export function generateMockTrackingEvents(sendIds: string[]) {
  const events = [];
  sendIds.forEach((sendId, i) => {
    // Generate open event
    events.push({
      id: `event-open-${i}`,
      send_id: sendId,
      type: 'open' as const,
      metadata: { user_agent: 'Mozilla/5.0...', ip: '***********' },
      created_at: '2024-01-15T10:30:00Z'
    });
    
    // Generate click event (50% chance)
    if (i % 2 === 0) {
      events.push({
        id: `event-click-${i}`,
        send_id: sendId,
        type: 'click' as const,
        metadata: { url: 'https://example.com', user_agent: 'Mozilla/5.0...' },
        created_at: '2024-01-15T10:45:00Z'
      });
    }
  });
  return events;
}