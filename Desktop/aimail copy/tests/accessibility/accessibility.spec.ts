import { test, expect } from '@playwright/test';
import { injectAxe, checkA11y, getViolations } from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await injectAxe(page);
  });

  test('login page should be accessible', async ({ page }) => {
    await page.goto('/login');
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('dashboard should be accessible after login', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('audience page should be accessible', async ({ page }) => {
    // Login and navigate
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/audience');
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('campaigns page should be accessible', async ({ page }) => {
    // Login and navigate
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/campaigns');
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('analytics page should be accessible', async ({ page }) => {
    // Login and navigate
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/analytics');
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('settings page should be accessible', async ({ page }) => {
    // Login and navigate
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/settings');
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('forms should have proper labels and ARIA attributes', async ({ page }) => {
    await page.goto('/login');
    
    // Check form accessibility
    const emailInput = page.locator('input[type="email"]');
    const passwordInput = page.locator('input[type="password"]');
    const submitButton = page.locator('button[type="submit"]');
    
    // Inputs should have labels
    await expect(emailInput).toHaveAttribute('aria-label');
    await expect(passwordInput).toHaveAttribute('aria-label');
    
    // Button should have accessible text
    await expect(submitButton).toHaveText(/sign in|login/i);
    
    // Check for ARIA attributes
    await checkA11y(page, 'form', {
      rules: {
        'label': { enabled: true },
        'aria-input-field-name': { enabled: true }
      }
    });
  });

  test('navigation should be keyboard accessible', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
    
    // Should navigate to first menu item
    await expect(page).toHaveURL(/\/(dashboard|audience|campaigns)/);
    
    // Check navigation accessibility
    await checkA11y(page, 'nav', {
      rules: {
        'keyboard': { enabled: true },
        'focus-order-semantics': { enabled: true }
      }
    });
  });

  test('tables should have proper headers and ARIA labels', async ({ page }) => {
    // Login and go to audience page
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/audience');
    
    // Check if table exists and is accessible
    const table = page.locator('table');
    if (await table.count() > 0) {
      await expect(table).toHaveAttribute('role', 'table');
      
      // Check for table headers
      const headers = page.locator('th');
      const headerCount = await headers.count();
      expect(headerCount).toBeGreaterThan(0);
      
      // Check table accessibility
      await checkA11y(page, 'table', {
        rules: {
          'table-header': { enabled: true },
          'th-has-data-cells': { enabled: true }
        }
      });
    }
  });

  test('modals and dialogs should be accessible', async ({ page }) => {
    // Login and navigate to audience
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.goto('/audience');
    
    // Open add contact modal
    const addButton = page.locator('[data-testid="add-contact-button"]');
    if (await addButton.count() > 0) {
      await addButton.click();
      
      // Check modal accessibility
      const modal = page.locator('[role="dialog"]');
      await expect(modal).toBeVisible();
      await expect(modal).toHaveAttribute('aria-labelledby');
      
      await checkA11y(page, '[role="dialog"]', {
        rules: {
          'dialog-name': { enabled: true },
          'focus-trap': { enabled: true }
        }
      });
    }
  });

  test('color contrast should meet WCAG standards', async ({ page }) => {
    await page.goto('/login');
    
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true }
      }
    });
  });

  test('images should have alt text', async ({ page }) => {
    await page.goto('/');
    
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      const ariaLabel = await img.getAttribute('aria-label');
      const role = await img.getAttribute('role');
      
      // Image should have alt text, aria-label, or be decorative
      expect(alt !== null || ariaLabel !== null || role === 'presentation').toBe(true);
    }
    
    await checkA11y(page, null, {
      rules: {
        'image-alt': { enabled: true }
      }
    });
  });

  test('should handle screen reader announcements', async ({ page }) => {
    // Login and perform an action that should announce changes
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Check for ARIA live regions
    const liveRegion = page.locator('[aria-live]');
    if (await liveRegion.count() > 0) {
      await expect(liveRegion).toBeVisible();
      await expect(liveRegion).toHaveAttribute('aria-live', /polite|assertive/);
    }
    
    await checkA11y(page, null, {
      rules: {
        'aria-live-region': { enabled: true }
      }
    });
  });
});