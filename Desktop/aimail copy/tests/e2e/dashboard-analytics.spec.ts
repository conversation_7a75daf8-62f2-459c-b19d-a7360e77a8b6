import { test, expect } from '@playwright/test';

test.describe('Dashboard and Analytics', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should display dashboard with key metrics', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard');
    
    // Should show key metrics widgets
    await expect(page.locator('[data-testid="total-contacts"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-campaigns"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-sends"]')).toBeVisible();
    await expect(page.locator('[data-testid="average-open-rate"]')).toBeVisible();
    
    // Should show recent activity
    await expect(page.locator('[data-testid="recent-campaigns"]')).toBeVisible();
    await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
  });

  test('should navigate to analytics page and display detailed metrics', async ({ page }) => {
    await page.click('[href="/analytics"]');
    await expect(page).toHaveURL('/analytics');
    await expect(page.locator('h1')).toContainText('Analytics');
    
    // Should show time series chart
    await expect(page.locator('[data-testid="time-series-chart"]')).toBeVisible();
    
    // Should show top performing campaigns
    await expect(page.locator('[data-testid="top-campaigns"]')).toBeVisible();
    
    // Should show performance metrics
    await expect(page.locator('[data-testid="performance-metrics"]')).toBeVisible();
  });

  test('should filter analytics by date range', async ({ page }) => {
    await page.goto('/analytics');
    
    // Open date range picker
    await page.click('[data-testid="date-range-picker"]');
    
    // Select last 30 days
    await page.click('[data-testid="last-30-days"]');
    
    // Should update charts and metrics
    await expect(page.locator('[data-testid="time-series-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="date-range-display"]')).toContainText('Last 30 days');
  });

  test('should display real-time updates', async ({ page }) => {
    // Get initial metrics
    const initialContacts = await page.locator('[data-testid="total-contacts"] .metric-value').textContent();
    
    // Navigate to audience and add a contact
    await page.goto('/audience');
    await page.click('[data-testid="add-contact-button"]');
    await page.fill('input[name="email"]', `realtime-test-${Date.now()}@example.com`);
    await page.fill('input[name="name"]', 'Real-time Test Contact');
    await page.click('button[type="submit"]');
    
    // Go back to dashboard
    await page.goto('/dashboard');
    
    // Should show updated contact count (with some tolerance for timing)
    await expect(async () => {
      const newContacts = await page.locator('[data-testid="total-contacts"] .metric-value').textContent();
      expect(parseInt(newContacts || '0')).toBeGreaterThan(parseInt(initialContacts || '0'));
    }).toPass({ timeout: 10000 });
  });

  test('should show responsive design on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/dashboard');
    
    // Should show mobile navigation
    await expect(page.locator('[data-testid="mobile-nav-toggle"]')).toBeVisible();
    
    // Metrics should stack vertically
    await expect(page.locator('[data-testid="metrics-grid"]')).toHaveCSS('flex-direction', 'column');
    
    // Open mobile menu
    await page.click('[data-testid="mobile-nav-toggle"]');
    await expect(page.locator('[data-testid="mobile-nav-menu"]')).toBeVisible();
    
    // Should be able to navigate
    await page.click('[data-testid="mobile-nav-campaigns"]');
    await expect(page).toHaveURL('/campaigns');
  });

  test('should handle empty states gracefully', async ({ page }) => {
    // This test assumes a fresh installation with no data
    // In a real test environment, you might want to clear data first
    
    await page.goto('/dashboard');
    
    // Should show empty states or zero values
    await expect(page.locator('[data-testid="empty-campaigns"], [data-testid="total-campaigns"]')).toBeVisible();
    await expect(page.locator('[data-testid="empty-contacts"], [data-testid="total-contacts"]')).toBeVisible();
    
    // Should show helpful messages
    await expect(page.locator('[data-testid="getting-started"]')).toBeVisible();
  });

  test('should export analytics data', async ({ page }) => {
    await page.goto('/analytics');
    
    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-analytics"]');
    const download = await downloadPromise;
    
    // Verify download
    expect(download.suggestedFilename()).toMatch(/analytics-export-\d{4}-\d{2}-\d{2}\.csv/);
    
    // Save and verify file content
    const path = await download.path();
    expect(path).toBeTruthy();
  });
});