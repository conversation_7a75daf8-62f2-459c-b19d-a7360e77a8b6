import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('Contact Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should navigate to audience page and display contacts', async ({ page }) => {
    await page.click('[href="/audience"]');
    await expect(page).toHaveURL('/audience');
    await expect(page.locator('h1')).toContainText('Audience');
    
    // Should show contacts table or empty state
    await expect(page.locator('[data-testid="contacts-table"], [data-testid="empty-contacts"]')).toBeVisible();
  });

  test('should create a new contact manually', async ({ page }) => {
    await page.goto('/audience');
    
    await page.click('[data-testid="add-contact-button"]');
    await expect(page.locator('[data-testid="contact-form"]')).toBeVisible();
    
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="name"]', 'New Contact');
    await page.click('button[type="submit"]');
    
    // Should show success message and contact in list
    await expect(page.locator('[role="alert"]')).toContainText('Contact created successfully');
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should import contacts via CSV upload', async ({ page }) => {
    await page.goto('/audience');
    
    // Create a test CSV file
    const csvContent = 'email,name\<EMAIL>,Test User 1\<EMAIL>,Test User 2';
    const csvPath = path.join(__dirname, '../fixtures/test-contacts.csv');
    
    await page.click('[data-testid="import-contacts-button"]');
    await expect(page.locator('[data-testid="csv-import-dialog"]')).toBeVisible();
    
    // Upload CSV file
    await page.setInputFiles('input[type="file"]', {
      name: 'test-contacts.csv',
      mimeType: 'text/csv',
      buffer: Buffer.from(csvContent)
    });
    
    await page.click('[data-testid="upload-csv-button"]');
    
    // Should show import progress and success
    await expect(page.locator('[data-testid="import-progress"]')).toBeVisible();
    await expect(page.locator('text=Import completed successfully')).toBeVisible({ timeout: 10000 });
    
    // Should show imported contacts
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });

  test('should edit an existing contact', async ({ page }) => {
    await page.goto('/audience');
    
    // Assuming there's at least one contact, click edit on first row
    await page.click('[data-testid="contact-row"]:first-child [data-testid="edit-contact"]');
    await expect(page.locator('[data-testid="contact-form"]')).toBeVisible();
    
    await page.fill('input[name="name"]', 'Updated Contact Name');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Contact updated successfully');
    await expect(page.locator('text=Updated Contact Name')).toBeVisible();
  });

  test('should delete a contact', async ({ page }) => {
    await page.goto('/audience');
    
    // Get the first contact email for verification
    const firstContactEmail = await page.locator('[data-testid="contact-row"]:first-child [data-testid="contact-email"]').textContent();
    
    await page.click('[data-testid="contact-row"]:first-child [data-testid="delete-contact"]');
    await expect(page.locator('[data-testid="delete-confirmation-dialog"]')).toBeVisible();
    
    await page.click('[data-testid="confirm-delete"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Contact deleted successfully');
    
    // Contact should no longer be visible
    if (firstContactEmail) {
      await expect(page.locator(`text=${firstContactEmail}`)).not.toBeVisible();
    }
  });

  test('should filter and search contacts', async ({ page }) => {
    await page.goto('/audience');
    
    // Test search functionality
    await page.fill('[data-testid="contact-search"]', '<EMAIL>');
    await page.keyboard.press('Enter');
    
    // Should filter results
    await expect(page.locator('[data-testid="contact-row"]')).toHaveCount(1);
    
    // Clear search
    await page.fill('[data-testid="contact-search"]', '');
    await page.keyboard.press('Enter');
    
    // Test status filter
    await page.selectOption('[data-testid="status-filter"]', 'active');
    await expect(page.locator('[data-testid="contact-row"]')).toHaveCount(1, { timeout: 5000 });
  });
});