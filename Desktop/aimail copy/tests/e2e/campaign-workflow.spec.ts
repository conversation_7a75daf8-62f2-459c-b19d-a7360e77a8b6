import { test, expect } from '@playwright/test';

test.describe('Campaign Management Workflow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', process.env.TEST_USER_EMAIL || '<EMAIL>');
    await page.fill('input[type="password"]', process.env.TEST_USER_PASSWORD || 'testpassword');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should navigate to campaigns page and display campaigns', async ({ page }) => {
    await page.click('[href="/campaigns"]');
    await expect(page).toHaveURL('/campaigns');
    await expect(page.locator('h1')).toContainText('Campaigns');
    
    // Should show campaigns table or empty state
    await expect(page.locator('[data-testid="campaigns-table"], [data-testid="empty-campaigns"]')).toBeVisible();
  });

  test('should create a new campaign with manual content', async ({ page }) => {
    await page.goto('/campaigns');
    
    await page.click('[data-testid="create-campaign-button"]');
    await expect(page.locator('[data-testid="campaign-form"]')).toBeVisible();
    
    // Fill campaign details
    await page.fill('input[name="name"]', 'Test Campaign');
    await page.fill('input[name="subject"]', 'Test Subject Line');
    await page.fill('textarea[name="htmlBody"]', '<h1>Hello World</h1><p>This is a test email.</p>');
    await page.fill('textarea[name="textBody"]', 'Hello World\n\nThis is a test email.');
    
    await page.click('button[type="submit"]');
    
    // Should show success message and redirect to campaigns list
    await expect(page.locator('[role="alert"]')).toContainText('Campaign created successfully');
    await expect(page).toHaveURL('/campaigns');
    await expect(page.locator('text=Test Campaign')).toBeVisible();
  });

  test('should create a campaign with AI-generated content', async ({ page }) => {
    await page.goto('/campaigns');
    
    await page.click('[data-testid="create-campaign-button"]');
    await expect(page.locator('[data-testid="campaign-form"]')).toBeVisible();
    
    // Fill basic details
    await page.fill('input[name="name"]', 'AI Generated Campaign');
    
    // Use AI generation
    await page.click('[data-testid="ai-generate-button"]');
    await expect(page.locator('[data-testid="ai-prompt-dialog"]')).toBeVisible();
    
    await page.fill('textarea[name="prompt"]', 'Create an email about a new product launch for a tech startup');
    await page.click('[data-testid="generate-content"]');
    
    // Wait for AI generation (mock response in test environment)
    await expect(page.locator('[data-testid="ai-suggestions"]')).toBeVisible({ timeout: 10000 });
    
    // Select generated content
    await page.click('[data-testid="select-subject"]:first-child');
    await page.click('[data-testid="select-body"]:first-child');
    await page.click('[data-testid="use-ai-content"]');
    
    // Verify content was populated
    await expect(page.locator('input[name="subject"]')).not.toHaveValue('');
    await expect(page.locator('textarea[name="htmlBody"]')).not.toHaveValue('');
    
    await page.click('button[type="submit"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Campaign created successfully');
    await expect(page.locator('text=AI Generated Campaign')).toBeVisible();
  });

  test('should preview campaign before sending', async ({ page }) => {
    await page.goto('/campaigns');
    
    // Click on first campaign to edit
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="edit-campaign"]');
    
    // Click preview button
    await page.click('[data-testid="preview-campaign"]');
    await expect(page.locator('[data-testid="campaign-preview"]')).toBeVisible();
    
    // Should show both HTML and text previews
    await expect(page.locator('[data-testid="html-preview"]')).toBeVisible();
    await expect(page.locator('[data-testid="text-preview"]')).toBeVisible();
    
    // Test preview tabs
    await page.click('[data-testid="text-preview-tab"]');
    await expect(page.locator('[data-testid="text-preview-content"]')).toBeVisible();
    
    await page.click('[data-testid="html-preview-tab"]');
    await expect(page.locator('[data-testid="html-preview-content"]')).toBeVisible();
  });

  test('should send a test email', async ({ page }) => {
    await page.goto('/campaigns');
    
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="edit-campaign"]');
    
    // Send test email
    await page.click('[data-testid="send-test-email"]');
    await expect(page.locator('[data-testid="test-email-dialog"]')).toBeVisible();
    
    await page.fill('input[name="testEmail"]', '<EMAIL>');
    await page.click('[data-testid="send-test"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Test email sent successfully');
  });

  test('should schedule a campaign for future sending', async ({ page }) => {
    await page.goto('/campaigns');
    
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="edit-campaign"]');
    
    // Schedule campaign
    await page.click('[data-testid="schedule-campaign"]');
    await expect(page.locator('[data-testid="schedule-dialog"]')).toBeVisible();
    
    // Set future date and time
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    
    await page.fill('input[type="datetime-local"]', futureDate.toISOString().slice(0, 16));
    await page.click('[data-testid="confirm-schedule"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Campaign scheduled successfully');
    
    // Should show scheduled status
    await page.goto('/campaigns');
    await expect(page.locator('[data-testid="campaign-status"]:first-child')).toContainText('Scheduled');
  });

  test('should send campaign immediately', async ({ page }) => {
    await page.goto('/campaigns');
    
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="edit-campaign"]');
    
    // Send immediately
    await page.click('[data-testid="send-now"]');
    await expect(page.locator('[data-testid="send-confirmation-dialog"]')).toBeVisible();
    
    await page.click('[data-testid="confirm-send"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Campaign sent successfully');
    
    // Should show sending/sent status
    await page.goto('/campaigns');
    await expect(page.locator('[data-testid="campaign-status"]:first-child')).toContainText(/Sending|Sent/);
  });

  test('should view campaign analytics', async ({ page }) => {
    await page.goto('/campaigns');
    
    // Click on a sent campaign's analytics
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="view-analytics"]');
    
    // Should show analytics page with metrics
    await expect(page.locator('[data-testid="campaign-analytics"]')).toBeVisible();
    await expect(page.locator('[data-testid="open-rate"]')).toBeVisible();
    await expect(page.locator('[data-testid="click-rate"]')).toBeVisible();
    await expect(page.locator('[data-testid="bounce-rate"]')).toBeVisible();
    
    // Should show charts
    await expect(page.locator('[data-testid="analytics-chart"]')).toBeVisible();
  });

  test('should duplicate an existing campaign', async ({ page }) => {
    await page.goto('/campaigns');
    
    const originalCampaignName = await page.locator('[data-testid="campaign-row"]:first-child [data-testid="campaign-name"]').textContent();
    
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="duplicate-campaign"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Campaign duplicated successfully');
    
    // Should show duplicated campaign with "Copy" suffix
    await expect(page.locator(`text=${originalCampaignName} (Copy)`)).toBeVisible();
  });

  test('should delete a campaign', async ({ page }) => {
    await page.goto('/campaigns');
    
    const campaignName = await page.locator('[data-testid="campaign-row"]:first-child [data-testid="campaign-name"]').textContent();
    
    await page.click('[data-testid="campaign-row"]:first-child [data-testid="delete-campaign"]');
    await expect(page.locator('[data-testid="delete-confirmation-dialog"]')).toBeVisible();
    
    await page.click('[data-testid="confirm-delete"]');
    
    await expect(page.locator('[role="alert"]')).toContainText('Campaign deleted successfully');
    
    // Campaign should no longer be visible
    if (campaignName) {
      await expect(page.locator(`text=${campaignName}`)).not.toBeVisible();
    }
  });
});