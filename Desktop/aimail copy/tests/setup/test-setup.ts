/**
 * Test Setup and Utilities
 * 
 * Common setup functions and utilities for all test types
 */

import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';

// Global test configuration
export const TEST_CONFIG = {
  timeout: 30000,
  retries: 2,
  baseUrl: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
  testUser: {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || 'testpassword'
  }
};

// Database setup for integration tests
export async function setupTestDatabase() {
  // This would typically:
  // 1. Create test database
  // 2. Run migrations
  // 3. Seed with test data
  console.log('Setting up test database...');
}

export async function cleanupTestDatabase() {
  // This would typically:
  // 1. Clean up test data
  // 2. Close connections
  console.log('Cleaning up test database...');
}

// Mock external services
export function setupMocks() {
  // Mock AWS SES
  jest.mock('@aws-sdk/client-ses', () => ({
    SESClient: jest.fn().mockImplementation(() => ({
      send: jest.fn().mockResolvedValue({
        MessageId: 'test-message-id'
      })
    })),
    SendEmailCommand: jest.fn()
  }));

  // Mock Supabase
  jest.mock('@/lib/supabase', () => ({
    supabase: {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: {}, error: null }),
      then: jest.fn().mockResolvedValue({ data: [], error: null })
    }
  }));

  // Mock Perplexity API
  global.fetch = jest.fn().mockImplementation((url: string) => {
    if (url.includes('api.perplexity.ai')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                subjects: ['Test Subject 1', 'Test Subject 2'],
                bodies: [
                  { html: '<p>Test body 1</p>', text: 'Test body 1' },
                  { html: '<p>Test body 2</p>', text: 'Test body 2' }
                ]
              })
            }
          }]
        })
      });
    }
    return Promise.reject(new Error('Unmocked fetch call'));
  });
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private startTime: number = 0;
  private measurements: Map<string, number> = new Map();

  start(label: string = 'default') {
    this.startTime = performance.now();
    this.measurements.set(`${label}_start`, this.startTime);
  }

  end(label: string = 'default'): number {
    const endTime = performance.now();
    const startTime = this.measurements.get(`${label}_start`) || this.startTime;
    const duration = endTime - startTime;
    this.measurements.set(`${label}_duration`, duration);
    return duration;
  }

  getDuration(label: string = 'default'): number {
    return this.measurements.get(`${label}_duration`) || 0;
  }

  getAllMeasurements(): Record<string, number> {
    return Object.fromEntries(this.measurements);
  }

  reset() {
    this.measurements.clear();
    this.startTime = 0;
  }
}

// Memory monitoring utilities
export class MemoryMonitor {
  private initialMemory: NodeJS.MemoryUsage;

  constructor() {
    this.initialMemory = process.memoryUsage();
  }

  getCurrentUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }

  getMemoryIncrease(): {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  } {
    const current = process.memoryUsage();
    return {
      heapUsed: current.heapUsed - this.initialMemory.heapUsed,
      heapTotal: current.heapTotal - this.initialMemory.heapTotal,
      external: current.external - this.initialMemory.external,
      rss: current.rss - this.initialMemory.rss
    };
  }

  reset() {
    this.initialMemory = process.memoryUsage();
  }
}

// Test data generators
export function generateTestEmail(index: number = 0) {
  return `test${index}@example.com`;
}

export function generateTestUser(index: number = 0) {
  return {
    id: `user-${index}`,
    email: generateTestEmail(index),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

export function generateTestContact(index: number = 0, userId: string = 'user-1') {
  return {
    id: `contact-${index}`,
    user_id: userId,
    email: generateTestEmail(index),
    name: `Test User ${index}`,
    status: 'active' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

export function generateTestCampaign(index: number = 0, userId: string = 'user-1') {
  return {
    id: `campaign-${index}`,
    user_id: userId,
    name: `Test Campaign ${index}`,
    subject: `Test Subject ${index}`,
    html_body: `<h1>Test Campaign ${index}</h1><p>This is test content.</p>`,
    text_body: `Test Campaign ${index}\n\nThis is test content.`,
    status: 'draft' as const,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

// Async utilities
export function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function waitForCondition(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (await condition()) {
      return;
    }
    await waitFor(interval);
  }
  
  throw new Error(`Condition not met within ${timeout}ms`);
}

// Error simulation utilities
export function simulateNetworkError() {
  return new Error('Network error: Connection timeout');
}

export function simulateValidationError(field: string) {
  return new Error(`Validation error: ${field} is required`);
}

export function simulateRateLimitError() {
  return new Error('Rate limit exceeded. Please try again later.');
}

// Test environment utilities
export function isCI(): boolean {
  return process.env.CI === 'true';
}

export function isDebugMode(): boolean {
  return process.env.DEBUG === 'true';
}

export function getTestTimeout(): number {
  return isCI() ? TEST_CONFIG.timeout * 2 : TEST_CONFIG.timeout;
}

// Cleanup utilities
export function cleanupTestFiles(patterns: string[]) {
  const fs = require('fs');
  const glob = require('glob');
  
  patterns.forEach(pattern => {
    const files = glob.sync(pattern);
    files.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  });
}

// Global test setup
export function setupGlobalTestEnvironment() {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321';
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  
  // Setup global mocks
  setupMocks();
  
  // Global test hooks
  beforeAll(async () => {
    await setupTestDatabase();
  });
  
  afterAll(async () => {
    await cleanupTestDatabase();
  });
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  afterEach(() => {
    // Cleanup any test artifacts
    cleanupTestFiles([
      'test-*.csv',
      'test-*.json',
      'screenshot-*.png'
    ]);
  });
}

// Export commonly used test utilities
export const testUtils = {
  performanceMonitor: new PerformanceMonitor(),
  memoryMonitor: new MemoryMonitor(),
  generateTestEmail,
  generateTestUser,
  generateTestContact,
  generateTestCampaign,
  waitFor,
  waitForCondition,
  simulateNetworkError,
  simulateValidationError,
  simulateRateLimitError,
  isCI,
  isDebugMode,
  getTestTimeout,
  cleanupTestFiles
};