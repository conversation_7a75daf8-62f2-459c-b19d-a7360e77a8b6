const { faker } = require('@faker-js/faker');

module.exports = {
  generateBulkContacts,
  $randomEmail,
  $randomFullName,
  $randomInt,
  $randomUuid
};

function generateBulkContacts(count) {
  const contacts = [];
  for (let i = 0; i < count; i++) {
    contacts.push({
      email: faker.internet.email(),
      name: faker.person.fullName(),
      status: 'active'
    });
  }
  return contacts;
}

function $randomEmail() {
  return faker.internet.email();
}

function $randomFullName() {
  return faker.person.fullName();
}

function $randomInt(min, max) {
  return faker.number.int({ min, max });
}

function $randomUuid() {
  return faker.string.uuid();
}