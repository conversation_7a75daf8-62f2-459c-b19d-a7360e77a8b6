config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm up"
    - duration: 120
      arrivalRate: 10
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 20
      name: "Sustained load"
    - duration: 60
      arrivalRate: 5
      name: "Cool down"
  processor: "./load-test-processor.js"
  variables:
    testEmail: "<EMAIL>"
    testPassword: "loadtestpassword"

scenarios:
  - name: "Authentication Flow"
    weight: 30
    flow:
      - get:
          url: "/login"
          capture:
            - json: "$.csrfToken"
              as: "csrfToken"
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/dashboard"
          headers:
            Authorization: "Bearer {{ authToken }}"

  - name: "Contact Management"
    weight: 25
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/contacts"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - post:
          url: "/api/contacts"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            email: "{{ $randomEmail() }}"
            name: "{{ $randomFullName() }}"
            status: "active"

  - name: "Campaign Operations"
    weight: 25
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/campaigns"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - post:
          url: "/api/campaigns"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            name: "Load Test Campaign {{ $randomInt(1, 10000) }}"
            subject: "Test Subject {{ $randomInt(1, 1000) }}"
            html_body: "<h1>Load Test Email</h1><p>This is a load test email.</p>"
            text_body: "Load Test Email\n\nThis is a load test email."

  - name: "Analytics and Reporting"
    weight: 20
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - get:
          url: "/api/analytics/dashboard"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - get:
          url: "/api/analytics/timeseries"
          headers:
            Authorization: "Bearer {{ authToken }}"
          qs:
            period: "30d"
      - get:
          url: "/api/analytics/top-campaigns"
          headers:
            Authorization: "Bearer {{ authToken }}"

  - name: "Email Tracking"
    weight: 10
    flow:
      - get:
          url: "/api/track/open"
          qs:
            id: "{{ $randomUuid() }}"
      - get:
          url: "/api/track/click"
          qs:
            id: "{{ $randomUuid() }}"
            url: "https://example.com"

  - name: "Bulk Operations"
    weight: 5
    flow:
      - post:
          url: "/api/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.token"
              as: "authToken"
      - post:
          url: "/api/contacts/import"
          headers:
            Authorization: "Bearer {{ authToken }}"
          json:
            contacts: "{{ generateBulkContacts(100) }}"

expect:
  - statusCode: 200
  - contentType: json
  - hasProperty: data

ensure:
  - p95: 2000  # 95% of requests should complete within 2 seconds
  - p99: 5000  # 99% of requests should complete within 5 seconds
  - maxErrorRate: 1  # Error rate should be less than 1%