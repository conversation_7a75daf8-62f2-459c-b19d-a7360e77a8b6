# Email Marketing Platform

A modern, self-hosted email marketing platform built with Next.js, Supabase, and Amazon SES. This platform provides a complete solution for managing email campaigns, contacts, and analytics with AI-powered content generation.

## Features

- 🔐 **Secure Authentication** - Email/password authentication with Supabase Auth
- 📧 **Campaign Management** - Create, schedule, and send email campaigns
- 👥 **Contact Management** - Import contacts via CSV with validation
- 🤖 **AI Content Generation** - Generate email content using Perplexity API
- 📊 **Analytics & Tracking** - Track opens, clicks, and campaign performance
- 🚀 **Amazon SES Integration** - Reliable email delivery with bounce handling
- 🎨 **White-label Ready** - Customizable branding and theming
- 📱 **Responsive Design** - Works on desktop, tablet, and mobile

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- Supabase account
- AWS account with SES access
- Perplexity API key (optional, for AI features)

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd email-marketing-platform
npm install
```

### 2. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration values (see [Configuration Guide](#configuration-guide) below).

### 3. Database Setup

Set up your Supabase database:

```bash
# Install Supabase CLI
npm install -g supabase

# Initialize Supabase (if not already done)
supabase init

# Link to your Supabase project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push

# Seed with sample data (optional)
supabase db seed
```

### 4. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Deployment

### Deploy to Vercel (Recommended)

1. **One-Click Deploy**
   
   [![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/email-marketing-platform)

2. **Manual Deploy**

   ```bash
   # Install Vercel CLI
   npm install -g vercel
   
   # Deploy
   vercel
   
   # Set environment variables
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
   # ... add all other environment variables
   ```

### Deploy to Netlify

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables in Netlify dashboard

### Deploy to Other Platforms

The application can be deployed to any platform that supports Next.js:
- Railway
- Render
- DigitalOcean App Platform
- AWS Amplify

## Configuration Guide

### Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your keys:
   - `NEXT_PUBLIC_SUPABASE_URL`: Your project URL
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your anon/public key
   - `SUPABASE_SERVICE_ROLE_KEY`: Your service role key (keep secret)

### Amazon SES Setup

1. **AWS Account Setup**
   - Create an AWS account if you don't have one
   - Go to IAM and create a user with SES permissions
   - Generate access keys for the user

2. **SES Configuration**
   - Go to Amazon SES console
   - Verify your sending domain or email address
   - Request production access (starts in sandbox mode)
   - Set up SNS topics for bounce/complaint handling

3. **Environment Variables**
   ```bash
   AWS_ACCESS_KEY_ID=your_access_key
   AWS_SECRET_ACCESS_KEY=your_secret_key
   AWS_REGION=us-east-1  # or your preferred region
   SES_FROM_EMAIL=<EMAIL>
   ```

### Perplexity API Setup (Optional)

1. Sign up at [perplexity.ai](https://perplexity.ai)
2. Get your API key from the dashboard
3. Add to environment variables:
   ```bash
   PERPLEXITY_API_KEY=your_api_key
   ```

### Domain and DNS Configuration

For production deployment, configure your domain:

1. **SPF Record**: Add to your DNS
   ```
   v=spf1 include:amazonses.com ~all
   ```

2. **DKIM**: Enable in SES console and add the provided DNS records

3. **DMARC**: Add DMARC policy
   ```
   v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
   ```

## Customization

### Branding

Edit `src/config/branding.ts` to customize the appearance:

```typescript
export const brandingConfig = {
  appName: 'Your App Name',
  logo: '/your-logo.png',
  colors: {
    primary: '#your-primary-color',
    secondary: '#your-secondary-color',
    accent: '#your-accent-color'
  }
}
```

### Styling

The application uses Tailwind CSS with shadcn/ui components. Customize the theme in `tailwind.config.ts`.

## Development

### Project Structure

```
src/
├── app/                 # Next.js App Router pages and API routes
├── components/          # React components
├── lib/                # Utility functions and services
├── types/              # TypeScript type definitions
├── config/             # Configuration files
└── hooks/              # Custom React hooks
```

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
```

### Testing

Run the test suite:

```bash
npm run test
```

For specific test types:
```bash
npm run test:unit        # Unit tests only
npm run test:integration # Integration tests only
npm run test:e2e         # End-to-end tests
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify Supabase URL and keys are correct
   - Check if RLS policies are properly configured
   - Ensure database migrations have been run

2. **Email Sending Issues**
   - Verify AWS credentials and SES configuration
   - Check if your domain/email is verified in SES
   - Ensure you're not in SES sandbox mode for production

3. **Build Errors**
   - Clear `.next` folder and `node_modules`
   - Run `npm install` again
   - Check for TypeScript errors

### Getting Help

- Check the [Issues](https://github.com/your-username/email-marketing-platform/issues) page
- Review the [Documentation](https://github.com/your-username/email-marketing-platform/wiki)
- Contact <NAME_EMAIL>

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run tests: `npm run test`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Security

For security issues, <NAME_EMAIL> instead of using the issue tracker.