# Design Document

## Overview

The email marketing platform will be built as a modern, single-page application using Next.js 14 with the App Router for both frontend and backend functionality. The architecture follows a serverless approach suitable for deployment on Vercel or Netlify, with Supabase providing the database layer and authentication services. The system is designed as a single-tenant application where each deployment serves one organization.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Next.js Frontend] --> B[Next.js API Routes]
    B --> C[Supabase Database]
    B --> D[Amazon SES]
    B --> E[Perplexity API]
    F[SNS Webhooks] --> G[/api/webhooks/ses]
    G --> C
    H[Email Recipients] --> I[Tracking Pixels/Links]
    I --> J[/api/track]
    J --> C
```

### Technology Stack

- **Frontend**: Next.js 14 with App Router, React 18, TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Database**: Supabase (PostgreSQL) with real-time subscriptions
- **Authentication**: Supabase Auth
- **Email Delivery**: Amazon SES via AWS SDK v3
- **AI Integration**: Perplexity API for content generation
- **Deployment**: Vercel (primary), Netlify (alternative)
- **File Storage**: Supabase Storage for assets

## Components and Interfaces

### Frontend Components

#### Layout Components
- `RootLayout`: Main application layout with authentication wrapper
- `DashboardLayout`: Sidebar navigation and main content area
- `Sidebar`: Navigation menu with active state management

#### Page Components
- `LoginPage`: Authentication form with Supabase integration
- `DashboardPage`: Overview statistics and recent activity
- `AudiencePage`: Contact management and CSV import
- `CampaignsPage`: Campaign list and creation interface
- `CampaignEditor`: Email composition with AI assistance
- `SettingsPage`: Configuration for API keys and branding

#### Shared Components
- `ContactUpload`: CSV file processing and validation
- `EmailComposer`: Rich text editor with AI generation
- `CampaignStats`: Charts and metrics display
- `DataTable`: Reusable table with pagination and sorting

### API Routes Structure

```
/api/
├── auth/
│   ├── login/route.ts
│   └── logout/route.ts
├── contacts/
│   ├── route.ts (GET, POST)
│   ├── import/route.ts
│   └── [id]/route.ts
├── campaigns/
│   ├── route.ts (GET, POST)
│   ├── [id]/route.ts
│   └── [id]/send/route.ts
├── ai/
│   └── generate/route.ts
├── webhooks/
│   └── ses/route.ts
├── track/
│   ├── open/route.ts
│   └── click/route.ts
└── settings/
    └── route.ts
```

### Service Layer

#### Database Service (`lib/supabase.ts`)
```typescript
interface DatabaseService {
  getContacts(userId: string, filters?: ContactFilters): Promise<Contact[]>
  createCampaign(campaign: CreateCampaignData): Promise<Campaign>
  logEmailSend(sendData: EmailSendLog): Promise<void>
  trackEvent(eventData: TrackingEvent): Promise<void>
  updateSuppressionList(email: string, reason: string): Promise<void>
}
```

#### Email Service (`lib/email.ts`)
```typescript
interface EmailService {
  sendCampaign(campaignId: string): Promise<SendResult>
  sendSingleEmail(to: string, subject: string, body: string): Promise<string>
  handleBounce(bounceData: SESBounceNotification): Promise<void>
  handleComplaint(complaintData: SESComplaintNotification): Promise<void>
}
```

#### AI Service (`lib/ai.ts`)
```typescript
interface AIService {
  generateEmailContent(prompt: string): Promise<AIGeneratedContent>
}

interface AIGeneratedContent {
  subjects: string[]
  bodies: string[]
}
```

## Data Models

### Database Schema

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Contacts Table
```sql
CREATE TABLE contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, email)
);
```

#### Campaigns Table
```sql
CREATE TABLE campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  html_body TEXT NOT NULL,
  text_body TEXT,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'failed')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Sends Table
```sql
CREATE TABLE sends (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
  contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'bounced')),
  ses_message_id VARCHAR(255),
  error_message TEXT,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Events Table
```sql
CREATE TABLE events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  send_id UUID REFERENCES sends(id) ON DELETE CASCADE,
  type VARCHAR(20) NOT NULL CHECK (type IN ('open', 'click', 'bounce', 'complaint')),
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Suppression Table
```sql
CREATE TABLE suppression (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  reason VARCHAR(50) NOT NULL CHECK (reason IN ('bounce', 'complaint', 'unsubscribe')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, email)
);
```

### TypeScript Interfaces

```typescript
interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
}

interface Contact {
  id: string
  user_id: string
  email: string
  name?: string
  status: 'active' | 'unsubscribed' | 'bounced'
  created_at: string
  updated_at: string
}

interface Campaign {
  id: string
  user_id: string
  name: string
  subject: string
  html_body: string
  text_body?: string
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
  scheduled_at?: string
  sent_at?: string
  created_at: string
  updated_at: string
}

interface EmailSend {
  id: string
  campaign_id: string
  contact_id: string
  status: 'pending' | 'sent' | 'failed' | 'bounced'
  ses_message_id?: string
  error_message?: string
  sent_at?: string
  created_at: string
}

interface TrackingEvent {
  id: string
  send_id: string
  type: 'open' | 'click' | 'bounce' | 'complaint'
  metadata?: Record<string, any>
  created_at: string
}
```

## Error Handling

### API Error Responses
All API routes will return consistent error responses:

```typescript
interface APIError {
  error: string
  message: string
  code?: string
  details?: any
}
```

### Error Categories
1. **Authentication Errors** (401): Invalid credentials, expired sessions
2. **Authorization Errors** (403): Insufficient permissions
3. **Validation Errors** (400): Invalid input data, malformed requests
4. **Rate Limiting Errors** (429): SES quota exceeded, API rate limits
5. **External Service Errors** (502): SES failures, Perplexity API issues
6. **Server Errors** (500): Database connection issues, unexpected failures

### Error Handling Strategy
- **Frontend**: Toast notifications for user-facing errors, fallback UI states
- **API Routes**: Structured error responses with appropriate HTTP status codes
- **Email Sending**: Retry logic with exponential backoff for transient failures
- **Webhook Processing**: Dead letter queue for failed webhook processing

## Testing Strategy

### Unit Testing
- **Components**: React Testing Library for UI component testing
- **API Routes**: Jest with mock database and external services
- **Services**: Isolated testing of business logic with mocked dependencies
- **Utilities**: Pure function testing for data transformation and validation

### Integration Testing
- **Database Operations**: Test with Supabase local development setup
- **Email Sending**: Mock SES service for testing send flows
- **Authentication**: Test auth flows with Supabase test environment
- **Webhook Processing**: Test SNS notification handling

### End-to-End Testing
- **Campaign Flow**: Complete user journey from login to campaign creation and sending
- **Contact Management**: CSV import and contact lifecycle testing
- **Tracking**: Email open and click tracking verification

### Performance Testing
- **Email Sending**: Load testing for bulk email campaigns
- **Database Queries**: Performance testing for large contact lists
- **API Endpoints**: Response time and throughput testing

## Security Considerations

### Authentication & Authorization
- Supabase Row Level Security (RLS) policies for data isolation
- JWT token validation on all protected API routes
- Session management with secure cookie configuration

### Data Protection
- Environment variables for all sensitive configuration
- Input validation and sanitization on all user inputs
- SQL injection prevention through parameterized queries
- XSS protection through proper output encoding

### Email Security
- SPF, DKIM, and DMARC configuration documentation
- Bounce and complaint handling to maintain sender reputation
- Rate limiting to prevent abuse and comply with SES limits
- Suppression list management for compliance

### Infrastructure Security
- HTTPS enforcement for all communications
- Secure webhook endpoint validation
- API rate limiting and abuse prevention
- Regular security dependency updates

## Configuration Management

### Environment Variables
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# AWS SES Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=
SES_FROM_EMAIL=

# Perplexity API
PERPLEXITY_API_KEY=

# Application Configuration
NEXTAUTH_SECRET=
NEXTAUTH_URL=
```

### Branding Configuration (`config/branding.ts`)
```typescript
export const brandingConfig = {
  appName: 'EmailFlow',
  logo: '/logo.png',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b'
  },
  theme: {
    borderRadius: '0.5rem',
    fontFamily: 'Inter'
  }
}
```

This design provides a solid foundation for building a modern, scalable email marketing platform that meets all the specified requirements while maintaining good architectural practices and security standards.