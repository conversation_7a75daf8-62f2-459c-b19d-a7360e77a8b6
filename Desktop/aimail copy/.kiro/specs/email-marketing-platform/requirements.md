# Requirements Document

## Introduction

This document outlines the requirements for building a standalone, self-hosted email marketing platform similar to Listmonk but with modern architecture and lightweight deployment. The platform will be built using Next.js for both frontend and backend, Supabase for database management, and will support easy deployment to platforms like Vercel or Netlify. The system is designed as a single-tenant solution where each deployment serves one company, with white-labeling capabilities through configurable branding.

## Requirements

### Requirement 1: Authentication System

**User Story:** As a business owner, I want to securely access my email marketing platform with simple email/password authentication, so that I can manage my campaigns privately.

#### Acceptance Criteria

1. WHEN a user visits the platform THEN the system SHALL present a login form with email and password fields
2. WHEN a user provides valid credentials THEN the system SHALL authenticate them using Supabase Auth
3. WHEN a user provides invalid credentials THEN the system SHALL display an appropriate error message
4. WHEN a user is authenticated THEN the system SHALL redirect them to the dashboard
5. IF a user is not authenticated THEN the system SHALL restrict access to all platform features
6. WHEN a user logs out THEN the system SHALL clear their session and redirect to login

### Requirement 2: Contact Management

**User Story:** As a marketer, I want to upload and manage my contact lists through CSV import, so that I can organize my audience for email campaigns.

#### Acceptance Criteria

1. WHEN a user uploads a CSV file THEN the system SHALL parse and import contacts into the Supabase database
2. WHEN importing contacts THEN the system SHALL validate email addresses and reject invalid entries
3. WHEN a contact is imported THEN the system SHALL store id, email, name, and status fields
4. WHEN a contact status changes THEN the system SHALL update it to active, unsubscribed, or bounced
5. WHEN a contact unsubscribes or bounces THEN the system SHALL add them to the suppression list
6. WHEN viewing contacts THEN the system SHALL display a paginated list with search and filter capabilities

### Requirement 3: Campaign Management

**User Story:** As a marketer, I want to create, schedule, and track email campaigns with HTML and text content, so that I can communicate effectively with my audience.

#### Acceptance Criteria

1. WHEN creating a campaign THEN the system SHALL provide fields for subject line, HTML body, and text body
2. WHEN saving a campaign THEN the system SHALL store it in the campaigns table with draft status
3. WHEN scheduling a campaign THEN the system SHALL allow setting a future send time
4. WHEN sending immediately THEN the system SHALL process the campaign without delay
5. WHEN a campaign is processing THEN the system SHALL update status to sending, then sent upon completion
6. WHEN viewing campaigns THEN the system SHALL display a list with status, send date, and basic metrics

### Requirement 4: AI-Powered Email Composer

**User Story:** As a marketer, I want to generate email content using AI assistance, so that I can create compelling campaigns more efficiently.

#### Acceptance Criteria

1. WHEN composing an email THEN the system SHALL provide a "Generate with AI" button
2. WHEN the AI button is clicked THEN the system SHALL prompt for campaign details (product/offer/goal)
3. WHEN AI generation is requested THEN the system SHALL call the Perplexity API with the specified prompt template
4. WHEN AI responds THEN the system SHALL display 3 subject line options and 2 body draft options
5. WHEN a user selects AI-generated content THEN the system SHALL populate the email composer fields
6. IF the Perplexity API fails THEN the system SHALL display an error message and allow manual composition

### Requirement 5: Email Delivery System

**User Story:** As a platform user, I want my campaigns to be delivered reliably through Amazon SES, so that my emails reach my audience's inboxes.

#### Acceptance Criteria

1. WHEN a campaign is sent THEN the system SHALL fetch active contacts from the database
2. WHEN sending emails THEN the system SHALL use Amazon SES via the AWS SDK
3. WHEN each email is sent THEN the system SHALL log the send in the sends table with campaign_id, email, status, and message_id
4. WHEN sending campaigns THEN the system SHALL respect SES rate limits to avoid throttling
5. WHEN SES returns errors THEN the system SHALL log the failure and continue with remaining emails
6. WHEN a send batch completes THEN the system SHALL update the campaign status appropriately

### Requirement 6: Bounce and Complaint Handling

**User Story:** As a platform administrator, I want automatic handling of email bounces and complaints, so that I maintain good sender reputation and comply with email regulations.

#### Acceptance Criteria

1. WHEN SES sends bounce notifications THEN the system SHALL receive them via SNS webhook
2. WHEN a bounce notification is received THEN the system SHALL parse the bounce type and affected email
3. WHEN a hard bounce occurs THEN the system SHALL mark the contact as bounced and add to suppression list
4. WHEN a complaint is received THEN the system SHALL mark the contact as unsubscribed and add to suppression list
5. WHEN updating suppression status THEN the system SHALL prevent future emails to those addresses
6. WHEN webhook processing fails THEN the system SHALL log errors for debugging

### Requirement 7: Email Tracking and Analytics

**User Story:** As a marketer, I want to track email opens, clicks, and engagement metrics, so that I can measure campaign effectiveness.

#### Acceptance Criteria

1. WHEN an email is opened THEN the system SHALL record an open event in the events table
2. WHEN a link is clicked THEN the system SHALL record a click event with metadata
3. WHEN viewing campaign reports THEN the system SHALL display open rates, click rates, and bounce rates
4. WHEN viewing dashboard THEN the system SHALL show aggregate statistics across all campaigns
5. WHEN tracking events THEN the system SHALL associate them with the correct send_id for accurate attribution
6. WHEN displaying charts THEN the system SHALL provide visual representations of campaign performance

### Requirement 8: User Interface and Dashboard

**User Story:** As a platform user, I want an intuitive dashboard with clear navigation, so that I can efficiently manage all aspects of my email marketing.

#### Acceptance Criteria

1. WHEN accessing the platform THEN the system SHALL display a clean dashboard with sidebar navigation
2. WHEN viewing the dashboard THEN the system SHALL show overview statistics and recent activity
3. WHEN managing audience THEN the system SHALL provide contact import, viewing, and management features
4. WHEN working with campaigns THEN the system SHALL offer creation, editing, and reporting capabilities
5. WHEN accessing settings THEN the system SHALL allow configuration of SES keys, API keys, and branding
6. WHEN using on mobile devices THEN the system SHALL provide a responsive design that works across screen sizes

### Requirement 9: Configuration and White-labeling

**User Story:** As a business owner, I want to customize the platform's branding and appearance, so that it matches my company's identity.

#### Acceptance Criteria

1. WHEN deploying the platform THEN the system SHALL read branding configuration from config.ts
2. WHEN customizing branding THEN the system SHALL allow changes to logo, colors, and app name
3. WHEN branding is updated THEN the system SHALL reflect changes across all UI components
4. WHEN storing sensitive data THEN the system SHALL use environment variables for all API keys and secrets
5. WHEN deploying to Vercel THEN the system SHALL support one-click deployment with proper configuration
6. WHEN setting up THEN the system SHALL provide clear documentation for all configuration steps

### Requirement 10: Database Schema and Data Management

**User Story:** As a system administrator, I want a well-structured database schema that supports all platform features, so that data is organized efficiently and securely.

#### Acceptance Criteria

1. WHEN storing user data THEN the system SHALL use a users table with id, email, and password_hash
2. WHEN managing contacts THEN the system SHALL use a contacts table with id, email, name, and status
3. WHEN storing campaigns THEN the system SHALL use a campaigns table with id, user_id, subject, body, status, and scheduled_at
4. WHEN logging sends THEN the system SHALL use a sends table with id, campaign_id, contact_id, status, and ses_message_id
5. WHEN tracking events THEN the system SHALL use an events table with id, send_id, type, and metadata
6. WHEN managing suppression THEN the system SHALL use a suppression table with id, email, and reason