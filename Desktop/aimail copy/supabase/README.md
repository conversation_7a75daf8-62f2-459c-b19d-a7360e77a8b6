# Database Setup

This directory contains the database schema, migrations, and configuration for the email marketing platform.

## Structure

- `migrations/` - Database migration files
  - `001_initial_schema.sql` - Creates all tables and indexes
  - `002_rls_policies.sql` - Sets up Row Level Security policies
- `config.toml` - Supabase local development configuration
- `seed.sql` - Sample data for development and testing

## Local Development Setup

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Start local Supabase instance:
   ```bash
   supabase start
   ```

3. Apply migrations:
   ```bash
   supabase db reset
   ```

4. The local database will be available at:
   - Database URL: `postgresql://postgres:postgres@127.0.0.1:54322/postgres`
   - API URL: `http://127.0.0.1:54321`
   - Studio URL: `http://127.0.0.1:54323`

## Environment Variables

Add these to your `.env.local` file:

```bash
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

You can get the keys by running:
```bash
supabase status
```

## Database Schema

### Tables

- **users** - User accounts with authentication
- **contacts** - Email contacts for campaigns
- **campaigns** - Email campaigns with content and scheduling
- **sends** - Individual email sends with delivery status
- **events** - Tracking events (opens, clicks, bounces, complaints)
- **suppression** - Suppressed email addresses

### Row Level Security

All tables have RLS enabled with policies that ensure:
- Users can only access their own data
- Data isolation between different user accounts
- Proper authentication is required for all operations

## Production Setup

1. Create a new Supabase project at https://supabase.com
2. Run the migration files in order:
   - `001_initial_schema.sql`
   - `002_rls_policies.sql`
3. Update your environment variables with production values
4. Configure your domain and authentication settings in Supabase dashboard

## Backup and Maintenance

- Regular backups are handled automatically by Supabase
- Monitor database performance through Supabase dashboard
- Review and update RLS policies as needed
- Keep migration files in version control