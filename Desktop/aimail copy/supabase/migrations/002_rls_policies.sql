-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE sends ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppression ENABLE ROW LEVEL SECURITY;

-- Users table policies
-- Users can only see and update their own record
CREATE POLICY "Users can view own record" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own record" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Contacts table policies
-- Users can only access their own contacts
CREATE POLICY "Users can view own contacts" ON contacts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own contacts" ON contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own contacts" ON contacts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own contacts" ON contacts
    FOR DELETE USING (auth.uid() = user_id);

-- Campaigns table policies
-- Users can only access their own campaigns
CREATE POLICY "Users can view own campaigns" ON campaigns
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own campaigns" ON campaigns
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own campaigns" ON campaigns
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own campaigns" ON campaigns
    FOR DELETE USING (auth.uid() = user_id);

-- Sends table policies
-- Users can only access sends for their own campaigns
CREATE POLICY "Users can view own sends" ON sends
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM campaigns 
            WHERE campaigns.id = sends.campaign_id 
            AND campaigns.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own sends" ON sends
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM campaigns 
            WHERE campaigns.id = sends.campaign_id 
            AND campaigns.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own sends" ON sends
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM campaigns 
            WHERE campaigns.id = sends.campaign_id 
            AND campaigns.user_id = auth.uid()
        )
    );

-- Events table policies
-- Users can only access events for their own sends
CREATE POLICY "Users can view own events" ON events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM sends 
            JOIN campaigns ON campaigns.id = sends.campaign_id
            WHERE sends.id = events.send_id 
            AND campaigns.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own events" ON events
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM sends 
            JOIN campaigns ON campaigns.id = sends.campaign_id
            WHERE sends.id = events.send_id 
            AND campaigns.user_id = auth.uid()
        )
    );

-- Suppression table policies
-- Users can only access their own suppression list
CREATE POLICY "Users can view own suppression list" ON suppression
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own suppression entries" ON suppression
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own suppression entries" ON suppression
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own suppression entries" ON suppression
    FOR DELETE USING (auth.uid() = user_id);