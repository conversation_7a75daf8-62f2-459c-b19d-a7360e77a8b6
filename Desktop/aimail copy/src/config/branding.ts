export interface BrandingConfig {
  appName: string
  logo: string
  colors: {
    primary: string
    secondary: string
    accent: string
  }
  theme: {
    borderRadius: string
    fontFamily: string
  }
}

export const brandingConfig: BrandingConfig = {
  appName: 'EmailFlow',
  logo: '/logo.png',
  colors: {
    primary: '#3b82f6', // blue-500
    secondary: '#64748b', // slate-500
    accent: '#f59e0b', // amber-500
  },
  theme: {
    borderRadius: '0.5rem',
    fontFamily: 'Inter',
  },
}

// Helper function to get CSS custom properties for the theme
export function getThemeVariables(config: BrandingConfig = brandingConfig) {
  return {
    '--brand-primary': config.colors.primary,
    '--brand-secondary': config.colors.secondary,
    '--brand-accent': config.colors.accent,
    '--brand-radius': config.theme.borderRadius,
    '--brand-font': config.theme.fontFamily,
  }
}

// Helper function to apply branding to document
export function applyBranding(config: BrandingConfig = brandingConfig) {
  if (typeof document !== 'undefined') {
    const root = document.documentElement
    const variables = getThemeVariables(config)
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
    
    // Update document title
    document.title = config.appName
  }
}