// Database table interfaces matching the schema

export interface User {
  id: string
  email: string
  password_hash: string
  created_at: string
  updated_at: string
}

export interface Contact {
  id: string
  user_id: string
  email: string
  name?: string
  status: 'active' | 'unsubscribed' | 'bounced'
  created_at: string
  updated_at: string
}

export interface Campaign {
  id: string
  user_id: string
  name: string
  subject: string
  html_body: string
  text_body?: string
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
  scheduled_at?: string
  sent_at?: string
  created_at: string
  updated_at: string
}

export interface EmailSend {
  id: string
  campaign_id: string
  contact_id: string
  status: 'pending' | 'sent' | 'failed' | 'bounced'
  ses_message_id?: string
  error_message?: string
  sent_at?: string
  created_at: string
}

export interface TrackingEvent {
  id: string
  send_id: string
  type: 'open' | 'click' | 'bounce' | 'complaint'
  metadata?: Record<string, any>
  created_at: string
}

export interface SuppressionEntry {
  id: string
  user_id: string
  email: string
  reason: 'bounce' | 'complaint' | 'unsubscribe'
  created_at: string
}

// Input types for creating records (without generated fields)
export interface CreateUserData {
  email: string
  password_hash: string
}

export interface CreateContactData {
  user_id: string
  email: string
  name?: string
  status?: 'active' | 'unsubscribed' | 'bounced'
}

export interface CreateCampaignData {
  user_id: string
  name: string
  subject: string
  html_body: string
  text_body?: string
  scheduled_at?: string
}

export interface CreateEmailSendData {
  campaign_id: string
  contact_id: string
  status?: 'pending' | 'sent' | 'failed' | 'bounced'
  ses_message_id?: string
  error_message?: string
}

export interface CreateTrackingEventData {
  send_id: string
  type: 'open' | 'click' | 'bounce' | 'complaint'
  metadata?: Record<string, any>
}

export interface CreateSuppressionEntryData {
  user_id: string
  email: string
  reason: 'bounce' | 'complaint' | 'unsubscribe'
}

// Update types (partial with required id)
export interface UpdateContactData extends Partial<Omit<Contact, 'id' | 'user_id' | 'created_at' | 'updated_at'>> {
  id: string
}

export interface UpdateCampaignData extends Partial<Omit<Campaign, 'id' | 'user_id' | 'created_at' | 'updated_at'>> {
  id: string
}

export interface UpdateEmailSendData extends Partial<Omit<EmailSend, 'id' | 'campaign_id' | 'contact_id' | 'created_at'>> {
  id: string
}

// Filter and query types
export interface ContactFilters {
  status?: 'active' | 'unsubscribed' | 'bounced'
  search?: string
  limit?: number
  offset?: number
}

export interface CampaignFilters {
  status?: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
  search?: string
  limit?: number
  offset?: number
}

export interface EmailSendFilters {
  campaign_id?: string
  status?: 'pending' | 'sent' | 'failed' | 'bounced'
  limit?: number
  offset?: number
}

export interface TrackingEventFilters {
  send_id?: string
  type?: 'open' | 'click' | 'bounce' | 'complaint'
  limit?: number
  offset?: number
}

// Aggregate types for statistics
export interface CampaignStats {
  campaign_id: string
  total_sends: number
  successful_sends: number
  failed_sends: number
  bounced_sends: number
  opens: number
  clicks: number
  open_rate: number
  click_rate: number
  bounce_rate: number
}

export interface DashboardStats {
  total_contacts: number
  active_contacts: number
  total_campaigns: number
  sent_campaigns: number
  total_sends: number
  total_opens: number
  total_clicks: number
  overall_open_rate: number
  overall_click_rate: number
}

// Database response types
export interface DatabaseResponse<T> {
  data: T | null
  error: Error | null
}

export interface DatabaseListResponse<T> {
  data: T[]
  error: Error | null
  count?: number
}

// Supabase specific types
export interface SupabaseError {
  message: string
  details: string
  hint: string
  code: string
}

// Join types for complex queries
export interface ContactWithStats extends Contact {
  total_sends?: number
  total_opens?: number
  total_clicks?: number
  last_sent?: string
}

export interface CampaignWithStats extends Campaign {
  total_sends?: number
  successful_sends?: number
  failed_sends?: number
  bounced_sends?: number
  opens?: number
  clicks?: number
  open_rate?: number
  click_rate?: number
  bounce_rate?: number
}

export interface EmailSendWithDetails extends EmailSend {
  campaign?: Pick<Campaign, 'id' | 'name' | 'subject'>
  contact?: Pick<Contact, 'id' | 'email' | 'name'>
  events?: TrackingEvent[]
}