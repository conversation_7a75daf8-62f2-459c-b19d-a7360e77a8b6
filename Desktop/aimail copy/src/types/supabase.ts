// Supabase generated types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          password_hash: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      contacts: {
        Row: {
          id: string
          user_id: string
          email: string
          name: string | null
          status: 'active' | 'unsubscribed' | 'bounced'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          name?: string | null
          status?: 'active' | 'unsubscribed' | 'bounced'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          name?: string | null
          status?: 'active' | 'unsubscribed' | 'bounced'
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contacts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      campaigns: {
        Row: {
          id: string
          user_id: string
          name: string
          subject: string
          html_body: string
          text_body: string | null
          status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
          scheduled_at: string | null
          sent_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          subject: string
          html_body: string
          text_body?: string | null
          status?: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
          scheduled_at?: string | null
          sent_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          subject?: string
          html_body?: string
          text_body?: string | null
          status?: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
          scheduled_at?: string | null
          sent_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      sends: {
        Row: {
          id: string
          campaign_id: string
          contact_id: string
          status: 'pending' | 'sent' | 'failed' | 'bounced'
          ses_message_id: string | null
          error_message: string | null
          sent_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          campaign_id: string
          contact_id: string
          status?: 'pending' | 'sent' | 'failed' | 'bounced'
          ses_message_id?: string | null
          error_message?: string | null
          sent_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          campaign_id?: string
          contact_id?: string
          status?: 'pending' | 'sent' | 'failed' | 'bounced'
          ses_message_id?: string | null
          error_message?: string | null
          sent_at?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sends_campaign_id_fkey"
            columns: ["campaign_id"]
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sends_contact_id_fkey"
            columns: ["contact_id"]
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          }
        ]
      }
      events: {
        Row: {
          id: string
          send_id: string
          type: 'open' | 'click' | 'bounce' | 'complaint'
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          send_id: string
          type: 'open' | 'click' | 'bounce' | 'complaint'
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          send_id?: string
          type?: 'open' | 'click' | 'bounce' | 'complaint'
          metadata?: Json | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_send_id_fkey"
            columns: ["send_id"]
            referencedRelation: "sends"
            referencedColumns: ["id"]
          }
        ]
      }
      suppression: {
        Row: {
          id: string
          user_id: string
          email: string
          reason: 'bounce' | 'complaint' | 'unsubscribe'
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          reason: 'bounce' | 'complaint' | 'unsubscribe'
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          reason?: 'bounce' | 'complaint' | 'unsubscribe'
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "suppression_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_extension_if_not_exists: {
        Args: {
          extension_name: string
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}