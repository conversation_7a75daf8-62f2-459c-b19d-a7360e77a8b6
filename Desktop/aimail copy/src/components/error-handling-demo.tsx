'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useApiError, useAsyncOperation } from '@/hooks/use-api-error'
import { useToast, useErrorToast, useSuccessToast } from '@/components/ui/toast'
import { captureError, captureWarning } from '@/lib/monitoring'

export function ErrorHandlingDemo() {
  const [loading, setLoading] = useState(false)
  const { handleError } = useApiError()
  const { execute } = useAsyncOperation()
  const { addToast } = useToast()
  const errorToast = useErrorToast()
  const successToast = useSuccessToast()

  const simulateNetworkError = async () => {
    setLoading(true)
    try {
      // Simulate a network error
      throw new Error('Network Error: Unable to connect to server')
    } catch (error) {
      handleError(error, { operation: 'demo-network-error' })
    } finally {
      setLoading(false)
    }
  }

  const simulateValidationError = async () => {
    setLoading(true)
    try {
      // Simulate a validation error
      const response = await fetch('/api/contacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: 'invalid-email' })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw errorData
      }
    } catch (error) {
      handleError(error, { operation: 'demo-validation-error' })
    } finally {
      setLoading(false)
    }
  }

  const simulateAsyncOperation = async () => {
    await execute(
      async () => {
        // Simulate async operation that might fail
        await new Promise(resolve => setTimeout(resolve, 1000))
        if (Math.random() > 0.5) {
          throw new Error('Random async operation failed')
        }
        return 'Success!'
      },
      {
        successMessage: 'Async operation completed successfully!',
        errorContext: { operation: 'demo-async-operation' }
      }
    )
  }

  const simulateMonitoringCapture = () => {
    // Demonstrate error monitoring
    captureError('Demo error for monitoring', { 
      userId: 'demo-user',
      feature: 'error-handling-demo' 
    })
    
    captureWarning('Demo warning message', {
      context: 'user-action'
    })
    
    successToast('Error and warning captured for monitoring')
  }

  const showCustomToast = () => {
    addToast({
      type: 'info',
      title: 'Custom Toast',
      description: 'This is a custom toast notification',
      action: {
        label: 'Dismiss',
        onClick: () => console.log('Toast dismissed')
      }
    })
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Error Handling System Demo</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Button 
            onClick={simulateNetworkError}
            disabled={loading}
            variant="destructive"
          >
            Simulate Network Error
          </Button>
          
          <Button 
            onClick={simulateValidationError}
            disabled={loading}
            variant="destructive"
          >
            Simulate Validation Error
          </Button>
          
          <Button 
            onClick={simulateAsyncOperation}
            disabled={loading}
          >
            Test Async Operation
          </Button>
          
          <Button 
            onClick={simulateMonitoringCapture}
            variant="outline"
          >
            Test Error Monitoring
          </Button>
          
          <Button 
            onClick={showCustomToast}
            variant="outline"
          >
            Show Custom Toast
          </Button>
        </div>
        
        <div className="text-sm text-gray-600 space-y-2">
          <p><strong>Network Error:</strong> Shows connection error with retry option</p>
          <p><strong>Validation Error:</strong> Shows validation error from API</p>
          <p><strong>Async Operation:</strong> Random success/failure with proper handling</p>
          <p><strong>Error Monitoring:</strong> Captures errors for monitoring service</p>
          <p><strong>Custom Toast:</strong> Shows custom toast notification</p>
        </div>
      </CardContent>
    </Card>
  )
}