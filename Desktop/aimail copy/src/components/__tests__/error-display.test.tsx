import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import {
  ErrorDisplay,
  NetworkError,
  AuthError,
  NotFoundError,
  ValidationError
} from '../error-display'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}))

const mockPush = jest.fn()
const mockRouter = {
  push: mockPush,
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn()
}

beforeEach(() => {
  ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
  mockPush.mockClear()
})

describe('ErrorDisplay', () => {
  test('should render default error message', () => {
    render(<ErrorDisplay />)
    
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(screen.getByText(/An unexpected error occurred/)).toBeInTheDocument()
  })

  test('should render custom title and description', () => {
    render(
      <ErrorDisplay
        title="Custom Error"
        description="Custom description"
      />
    )
    
    expect(screen.getByText('Custom Error')).toBeInTheDocument()
    expect(screen.getByText('Custom description')).toBeInTheDocument()
  })

  test('should handle authentication error', () => {
    render(<ErrorDisplay error="Authentication required" />)
    
    expect(screen.getByText('Authentication Required')).toBeInTheDocument()
    expect(screen.getByText('Please log in to access this feature.')).toBeInTheDocument()
    expect(screen.getByText('Go to Login')).toBeInTheDocument()
  })

  test('should handle network error', () => {
    render(<ErrorDisplay error="Network Error" />)
    
    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(screen.getByText(/Unable to connect to the server/)).toBeInTheDocument()
  })

  test('should handle rate limit error', () => {
    render(<ErrorDisplay error="Rate limit exceeded" />)
    
    expect(screen.getByText('Too Many Requests')).toBeInTheDocument()
    expect(screen.getByText(/too many requests/)).toBeInTheDocument()
  })

  test('should handle validation error', () => {
    render(<ErrorDisplay error="Validation failed" />)
    
    expect(screen.getByText('Invalid Data')).toBeInTheDocument()
    expect(screen.getByText('Please check your input and try again.')).toBeInTheDocument()
  })

  test('should handle not found error', () => {
    render(<ErrorDisplay error="Not Found" />)
    
    expect(screen.getByText('Page Not Found')).toBeInTheDocument()
    expect(screen.getByText(/doesn\'t exist or has been moved/)).toBeInTheDocument()
  })

  test('should call onRetry when retry button is clicked', () => {
    const onRetry = jest.fn()
    render(<ErrorDisplay onRetry={onRetry} />)
    
    fireEvent.click(screen.getByText('Try Again'))
    expect(onRetry).toHaveBeenCalled()
  })

  test('should navigate to dashboard when dashboard button is clicked', () => {
    render(<ErrorDisplay />)
    
    fireEvent.click(screen.getByText('Dashboard'))
    expect(mockPush).toHaveBeenCalledWith('/dashboard')
  })

  test('should navigate to campaigns when campaigns button is clicked', () => {
    render(<ErrorDisplay />)
    
    fireEvent.click(screen.getByText('Campaigns'))
    expect(mockPush).toHaveBeenCalledWith('/campaigns')
  })

  test('should navigate to audience when audience button is clicked', () => {
    render(<ErrorDisplay />)
    
    fireEvent.click(screen.getByText('Audience'))
    expect(mockPush).toHaveBeenCalledWith('/audience')
  })

  test('should hide retry button when showRetry is false', () => {
    render(<ErrorDisplay showRetry={false} />)
    
    expect(screen.queryByText('Try Again')).not.toBeInTheDocument()
  })

  test('should hide navigation when showNavigation is false', () => {
    render(<ErrorDisplay showNavigation={false} />)
    
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument()
    expect(screen.queryByText('Campaigns')).not.toBeInTheDocument()
    expect(screen.queryByText('Audience')).not.toBeInTheDocument()
  })

  test('should show error details in development', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    const error = new Error('Test error with stack')
    render(<ErrorDisplay error={error} />)
    
    expect(screen.getByText('Technical Details')).toBeInTheDocument()
    
    process.env.NODE_ENV = originalEnv
  })
})

describe('Specialized Error Components', () => {
  test('NetworkError should render with correct props', () => {
    const onRetry = jest.fn()
    render(<NetworkError onRetry={onRetry} />)
    
    expect(screen.getByText('Connection Problem')).toBeInTheDocument()
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument() // showNavigation=false
  })

  test('AuthError should render with correct props', () => {
    render(<AuthError />)
    
    expect(screen.getByText('Authentication Required')).toBeInTheDocument()
    expect(screen.queryByText('Try Again')).not.toBeInTheDocument() // showRetry=false
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument() // showNavigation=false
  })

  test('NotFoundError should render with correct props', () => {
    render(<NotFoundError />)
    
    expect(screen.getByText('Page Not Found')).toBeInTheDocument()
    expect(screen.queryByText('Try Again')).not.toBeInTheDocument() // showRetry=false
  })

  test('ValidationError should render with correct props', () => {
    const onRetry = jest.fn()
    render(<ValidationError message="Custom validation error" onRetry={onRetry} />)
    
    expect(screen.getByText('Invalid Data')).toBeInTheDocument()
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument() // showNavigation=false
    
    fireEvent.click(screen.getByText('Try Again'))
    expect(onRetry).toHaveBeenCalled()
  })
})