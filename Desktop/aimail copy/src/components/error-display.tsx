'use client'

import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Mail, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useRouter } from 'next/navigation'

interface ErrorDisplayProps {
  error?: Error | string
  title?: string
  description?: string
  showRetry?: boolean
  showNavigation?: boolean
  onRetry?: () => void
  className?: string
}

export function ErrorDisplay({
  error,
  title = "Something went wrong",
  description,
  showRetry = true,
  showNavigation = true,
  onRetry,
  className
}: ErrorDisplayProps) {
  const router = useRouter()

  const errorMessage = error instanceof Error ? error.message : String(error || '')
  
  // Provide user-friendly messages for common errors
  const getFriendlyMessage = (message: string) => {
    if (message.includes('Authentication required') || message.includes('Unauthorized')) {
      return {
        title: 'Authentication Required',
        description: 'Please log in to access this feature.',
        actions: [
          { label: 'Go to Login', action: () => router.push('/login'), icon: Mail }
        ]
      }
    }
    
    if (message.includes('Network Error') || message.includes('fetch')) {
      return {
        title: 'Connection Problem',
        description: 'Unable to connect to the server. Please check your internet connection and try again.',
        actions: [
          { label: 'Try Again', action: onRetry || (() => window.location.reload()), icon: RefreshCw }
        ]
      }
    }
    
    if (message.includes('Rate limit') || message.includes('Too Many Requests')) {
      return {
        title: 'Too Many Requests',
        description: 'You\'ve made too many requests. Please wait a moment before trying again.',
        actions: []
      }
    }
    
    if (message.includes('Validation') || message.includes('Invalid')) {
      return {
        title: 'Invalid Data',
        description: 'Please check your input and try again.',
        actions: [
          { label: 'Try Again', action: onRetry, icon: RefreshCw }
        ]
      }
    }
    
    if (message.includes('Not Found') || message.includes('404')) {
      return {
        title: 'Page Not Found',
        description: 'The page you\'re looking for doesn\'t exist or has been moved.',
        actions: [
          { label: 'Go to Dashboard', action: () => router.push('/dashboard'), icon: Home }
        ]
      }
    }
    
    // Default error message
    return {
      title: title,
      description: description || 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      actions: [
        { label: 'Try Again', action: onRetry || (() => window.location.reload()), icon: RefreshCw }
      ]
    }
  }

  const friendlyError = getFriendlyMessage(errorMessage)

  const navigationActions = [
    { label: 'Dashboard', action: () => router.push('/dashboard'), icon: Home },
    { label: 'Campaigns', action: () => router.push('/campaigns'), icon: Mail },
    { label: 'Audience', action: () => router.push('/audience'), icon: Users }
  ]

  return (
    <div className={`flex items-center justify-center p-4 ${className}`}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 text-red-500">
            <AlertTriangle className="h-full w-full" />
          </div>
          <CardTitle className="text-xl">{friendlyError.title}</CardTitle>
          <CardDescription>{friendlyError.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Primary actions */}
          {showRetry && friendlyError.actions.length > 0 && (
            <div className="space-y-2">
              {friendlyError.actions.map((action, index) => (
                <Button
                  key={index}
                  onClick={action.action}
                  className="w-full"
                  disabled={!action.action}
                >
                  {action.icon && <action.icon className="mr-2 h-4 w-4" />}
                  {action.label}
                </Button>
              ))}
            </div>
          )}

          {/* Navigation actions */}
          {showNavigation && (
            <div className="border-t pt-4">
              <p className="text-sm text-gray-600 mb-2">Or go to:</p>
              <div className="grid grid-cols-3 gap-2">
                {navigationActions.map((nav, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={nav.action}
                    className="text-xs"
                  >
                    <nav.icon className="mr-1 h-3 w-3" />
                    {nav.label}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Development error details */}
          {process.env.NODE_ENV === 'development' && errorMessage && (
            <details className="text-sm border-t pt-4">
              <summary className="cursor-pointer font-medium text-gray-700">
                Technical Details
              </summary>
              <pre className="mt-2 whitespace-pre-wrap break-words text-xs bg-gray-100 p-2 rounded">
                {errorMessage}
                {error instanceof Error && error.stack}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Specialized error components
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorDisplay
      error="Network Error"
      onRetry={onRetry}
      showNavigation={false}
    />
  )
}

export function AuthError() {
  return (
    <ErrorDisplay
      error="Authentication required"
      showRetry={false}
      showNavigation={false}
    />
  )
}

export function NotFoundError() {
  return (
    <ErrorDisplay
      error="Not Found"
      showRetry={false}
    />
  )
}

export function ValidationError({ message, onRetry }: { message?: string; onRetry?: () => void }) {
  return (
    <ErrorDisplay
      error={message || "Validation Error"}
      onRetry={onRetry}
      showNavigation={false}
    />
  )
}