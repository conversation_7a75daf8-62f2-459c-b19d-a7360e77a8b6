import { AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FormErrorProps {
  message?: string
  className?: string
}

export function FormError({ message, className }: FormErrorProps) {
  if (!message) return null

  return (
    <div className={cn("flex items-center gap-2 text-sm text-red-600", className)}>
      <AlertCircle className="h-4 w-4" />
      <span>{message}</span>
    </div>
  )
}

interface FormFieldErrorProps {
  errors?: Record<string, string>
  name: string
  className?: string
}

export function FormFieldError({ errors, name, className }: FormFieldErrorProps) {
  const error = errors?.[name]
  if (!error) return null

  return <FormError message={error} className={className} />
}

interface FormErrorListProps {
  errors: string[]
  className?: string
}

export function FormErrorList({ errors, className }: FormErrorListProps) {
  if (!errors.length) return null

  return (
    <div className={cn("space-y-1", className)}>
      {errors.map((error, index) => (
        <FormError key={index} message={error} />
      ))}
    </div>
  )
}