'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { DashboardMetrics } from '../../lib/analytics'

interface PerformanceOverviewProps {
  metrics: DashboardMetrics
  loading?: boolean
}

export function PerformanceOverview({ metrics, loading = false }: PerformanceOverviewProps) {
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatNumber = (value: number) => value.toLocaleString()

  // Calculate performance indicators
  const getPerformanceLevel = (rate: number, type: 'open' | 'click') => {
    if (type === 'open') {
      if (rate >= 25) return { level: 'excellent', color: 'bg-green-500' }
      if (rate >= 20) return { level: 'good', color: 'bg-blue-500' }
      if (rate >= 15) return { level: 'average', color: 'bg-yellow-500' }
      return { level: 'needs improvement', color: 'bg-red-500' }
    } else {
      if (rate >= 5) return { level: 'excellent', color: 'bg-green-500' }
      if (rate >= 3) return { level: 'good', color: 'bg-blue-500' }
      if (rate >= 2) return { level: 'average', color: 'bg-yellow-500' }
      return { level: 'needs improvement', color: 'bg-red-500' }
    }
  }

  const openPerformance = getPerformanceLevel(metrics.averageOpenRate, 'open')
  const clickPerformance = getPerformanceLevel(metrics.averageClickRate, 'click')

  const engagementRate = metrics.totalSends > 0 
    ? ((metrics.totalOpens + metrics.totalClicks) / metrics.totalSends) * 100 
    : 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🎯 Performance Overview
        </CardTitle>
        <CardDescription>
          Email marketing performance insights and benchmarks
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-6">
            <div className="space-y-3">
              <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
              <div className="h-2 w-full bg-gray-200 animate-pulse rounded"></div>
            </div>
            <div className="space-y-3">
              <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
              <div className="h-2 w-full bg-gray-200 animate-pulse rounded"></div>
            </div>
          </div>
        ) : metrics.totalSends > 0 ? (
          <div className="space-y-6">
            {/* Open Rate Performance */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Open Rate Performance</span>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-bold">{formatPercentage(metrics.averageOpenRate)}</span>
                  <Badge variant="secondary" className={`text-white ${openPerformance.color}`}>
                    {openPerformance.level}
                  </Badge>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${openPerformance.color}`}
                  style={{ width: `${Math.min(metrics.averageOpenRate, 100)}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>0%</span>
                <span>Industry avg: 20-25%</span>
                <span>50%</span>
              </div>
            </div>

            {/* Click Rate Performance */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Click Rate Performance</span>
                <div className="flex items-center gap-2">
                  <span className="text-lg font-bold">{formatPercentage(metrics.averageClickRate)}</span>
                  <Badge variant="secondary" className={`text-white ${clickPerformance.color}`}>
                    {clickPerformance.level}
                  </Badge>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${clickPerformance.color}`}
                  style={{ width: `${Math.min(metrics.averageClickRate * 10, 100)}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>0%</span>
                <span>Industry avg: 2-5%</span>
                <span>10%</span>
              </div>
            </div>

            {/* Key Metrics Grid */}
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{formatNumber(metrics.totalSends)}</div>
                <div className="text-sm text-gray-600">Total Emails Sent</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{formatPercentage(engagementRate)}</div>
                <div className="text-sm text-gray-600">Engagement Rate</div>
              </div>
            </div>

            {/* Performance Tips */}
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">💡 Performance Tips</h4>
              <div className="space-y-1 text-xs text-gray-600">
                {metrics.averageOpenRate < 20 && (
                  <p>• Try improving your subject lines to increase open rates</p>
                )}
                {metrics.averageClickRate < 3 && (
                  <p>• Add clear call-to-action buttons to improve click rates</p>
                )}
                {metrics.totalCampaigns < 5 && (
                  <p>• Send more campaigns to get better performance insights</p>
                )}
                {metrics.averageOpenRate >= 25 && metrics.averageClickRate >= 5 && (
                  <p>• Excellent performance! Keep up the great work!</p>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <div className="text-4xl mb-4">📊</div>
            <p className="text-lg font-medium mb-2">No performance data yet</p>
            <p className="text-sm">Send your first campaign to see performance insights</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}