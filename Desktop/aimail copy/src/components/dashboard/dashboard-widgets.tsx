'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { DashboardMetrics, CampaignAnalytics } from '../../lib/analytics'
import Link from 'next/link'

interface DashboardWidgetsProps {
  metrics: DashboardMetrics
  loading?: boolean
}

export function DashboardWidgets({ metrics, loading = false }: DashboardWidgetsProps) {
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatNumber = (value: number) => value.toLocaleString()

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Contacts"
          value={formatNumber(metrics.totalContacts)}
          description={metrics.totalContacts === 0 ? 'No contacts yet' : 'Active contacts'}
          loading={loading}
          icon="👥"
        />
        
        <MetricCard
          title="Campaigns"
          value={formatNumber(metrics.totalCampaigns)}
          description={metrics.totalCampaigns === 0 ? 'No campaigns created' : 'Total campaigns'}
          loading={loading}
          icon="📧"
        />
        
        <MetricCard
          title="Open Rate"
          value={formatPercentage(metrics.averageOpenRate)}
          description={metrics.totalSends === 0 ? 'No data available' : 'Average across campaigns'}
          loading={loading}
          icon="📊"
          trend={metrics.averageOpenRate > 20 ? 'up' : metrics.averageOpenRate > 0 ? 'neutral' : undefined}
        />
        
        <MetricCard
          title="Click Rate"
          value={formatPercentage(metrics.averageClickRate)}
          description={metrics.totalSends === 0 ? 'No data available' : 'Average across campaigns'}
          loading={loading}
          icon="🖱️"
          trend={metrics.averageClickRate > 3 ? 'up' : metrics.averageClickRate > 0 ? 'neutral' : undefined}
        />
      </div>

      {/* Activity and Quick Actions Row */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <EmailActivityWidget metrics={metrics} loading={loading} />
        <QuickActionsWidget />
      </div>

      {/* Recent Campaigns */}
      <RecentCampaignsWidget campaigns={metrics.recentCampaigns} loading={loading} />
    </div>
  )
}

interface MetricCardProps {
  title: string
  value: string
  description: string
  loading?: boolean
  icon?: string
  trend?: 'up' | 'down' | 'neutral'
}

function MetricCard({ title, value, description, loading, icon, trend }: MetricCardProps) {
  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon && <span className="text-2xl opacity-60">{icon}</span>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <div className="h-8 w-16 bg-gray-200 animate-pulse rounded"></div>
          ) : (
            <div className="flex items-center gap-2">
              {value}
              {trend && (
                <span className={`text-sm ${
                  trend === 'up' ? 'text-green-600' : 
                  trend === 'down' ? 'text-red-600' : 
                  'text-gray-600'
                }`}>
                  {trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '→'}
                </span>
              )}
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
      </CardContent>
      {trend === 'up' && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-green-600"></div>
      )}
    </Card>
  )
}

interface EmailActivityWidgetProps {
  metrics: DashboardMetrics
  loading?: boolean
}

function EmailActivityWidget({ metrics, loading }: EmailActivityWidgetProps) {
  const formatNumber = (value: number) => value.toLocaleString()

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          📈 Email Activity
        </CardTitle>
        <CardDescription>Total email engagement metrics</CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                <div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div>
              </div>
            ))}
          </div>
        ) : metrics.totalSends > 0 ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Emails Sent</span>
              <span className="text-lg font-bold text-blue-600">{formatNumber(metrics.totalSends)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Total Opens</span>
              <span className="text-lg font-bold text-green-600">{formatNumber(metrics.totalOpens)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Total Clicks</span>
              <span className="text-lg font-bold text-purple-600">{formatNumber(metrics.totalClicks)}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex justify-between items-center text-sm text-gray-600">
                <span>Engagement Rate</span>
                <span>{((metrics.totalOpens + metrics.totalClicks) / Math.max(metrics.totalSends, 1) * 100).toFixed(1)}%</span>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-6 text-gray-500">
            <p className="mb-2">No email activity yet</p>
            <p className="text-sm">Send your first campaign to see metrics here</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

function QuickActionsWidget() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          ⚡ Quick Actions
        </CardTitle>
        <CardDescription>Common tasks and shortcuts</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          <Link href="/campaigns" className="block">
            <Button className="w-full justify-start" size="lg">
              <span className="mr-2">📝</span>
              Create New Campaign
            </Button>
          </Link>
          
          <Link href="/audience" className="block">
            <Button variant="outline" className="w-full justify-start" size="lg">
              <span className="mr-2">👥</span>
              Manage Contacts
            </Button>
          </Link>
          
          <Link href="/analytics" className="block">
            <Button variant="outline" className="w-full justify-start" size="lg">
              <span className="mr-2">📊</span>
              View Analytics
            </Button>
          </Link>
          
          <Link href="/settings" className="block">
            <Button variant="ghost" className="w-full justify-start" size="lg">
              <span className="mr-2">⚙️</span>
              Settings
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}

interface RecentCampaignsWidgetProps {
  campaigns: CampaignAnalytics[]
  loading?: boolean
}

function RecentCampaignsWidget({ campaigns, loading }: RecentCampaignsWidgetProps) {
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatNumber = (value: number) => value.toLocaleString()

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              📋 Recent Campaigns
            </CardTitle>
            <CardDescription>
              Your latest email marketing activities
            </CardDescription>
          </div>
          <Link href="/campaigns">
            <Button variant="outline" size="sm">
              View All
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-48 bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-3 w-32 bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-3 w-16 bg-gray-200 animate-pulse rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 w-16 bg-gray-200 animate-pulse rounded"></div>
                  <div className="h-3 w-16 bg-gray-200 animate-pulse rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : campaigns && campaigns.length > 0 ? (
          <div className="space-y-4">
            {campaigns.map((campaign) => (
              <div key={campaign.campaignId} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium truncate">{campaign.campaignName}</h3>
                  <p className="text-sm text-gray-600 truncate">{campaign.subject}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={campaign.status === 'sent' ? 'default' : 'secondary'}>
                      {campaign.status}
                    </Badge>
                    {campaign.sentAt && (
                      <span className="text-sm text-gray-500">
                        {new Date(campaign.sentAt).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right ml-4 flex-shrink-0">
                  <div className="text-sm text-gray-500 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="w-12 text-right">Sent:</span>
                      <span className="font-medium">{formatNumber(campaign.totalSends)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-12 text-right">Opens:</span>
                      <span className="font-medium text-green-600">{formatPercentage(campaign.openRate)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-12 text-right">Clicks:</span>
                      <span className="font-medium text-purple-600">{formatPercentage(campaign.clickRate)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-4">📧</div>
            <p className="text-lg font-medium mb-2">No campaigns yet</p>
            <p className="text-sm mb-4">Create your first campaign to get started</p>
            <Link href="/campaigns">
              <Button>
                Create Your First Campaign
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}