'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import Link from 'next/link'

interface WelcomeWidgetProps {
  userName?: string
  totalCampaigns: number
  totalContacts: number
}

export function WelcomeWidget({ userName, totalCampaigns, totalContacts }: WelcomeWidgetProps) {
  const isNewUser = totalCampaigns === 0 && totalContacts === 0
  const hasContacts = totalContacts > 0
  const hasCampaigns = totalCampaigns > 0

  // Determine user progress
  const getProgressStep = () => {
    if (!hasContacts && !hasCampaigns) return 'start'
    if (hasContacts && !hasCampaigns) return 'contacts'
    if (hasCampaigns) return 'campaigns'
    return 'complete'
  }

  const progressStep = getProgressStep()

  const steps = [
    { id: 'start', label: 'Get Started', completed: hasContacts || hasCampaigns },
    { id: 'contacts', label: 'Add Contacts', completed: hasContacts },
    { id: 'campaigns', label: 'Create Campaign', completed: hasCampaigns },
  ]

  if (isNewUser) {
    return (
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            👋 Welcome to your Email Marketing Platform!
          </CardTitle>
          <CardDescription>
            Let&apos;s get you started with your first email campaign
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Follow these steps to send your first email campaign:
            </p>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-sm">
                  1
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">Import your contacts</h4>
                  <p className="text-sm text-gray-600">Upload a CSV file with your email list</p>
                </div>
                <Link href="/audience">
                  <Button size="sm">Start</Button>
                </Link>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border opacity-60">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">
                  2
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">Create your first campaign</h4>
                  <p className="text-sm text-gray-600">Design and send your email</p>
                </div>
                <Button size="sm" disabled>Next</Button>
              </div>

              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border opacity-60">
                <div className="flex-shrink-0 w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">
                  3
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">Track your results</h4>
                  <p className="text-sm text-gray-600">Monitor opens, clicks, and engagement</p>
                </div>
                <Button size="sm" disabled>Later</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🎉 Great progress{userName ? `, ${userName.split('@')[0]}` : ''}!
        </CardTitle>
        <CardDescription>
          Here&apos;s what you&apos;ve accomplished so far
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Progress Steps */}
          <div className="flex items-center gap-2 mb-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  step.completed 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-200 text-gray-500'
                }`}>
                  {step.completed ? '✓' : index + 1}
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-8 h-1 mx-1 ${
                    step.completed ? 'bg-green-500' : 'bg-gray-200'
                  }`}></div>
                )}
              </div>
            ))}
          </div>

          {/* Current Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-blue-600">{totalContacts}</div>
              <div className="text-sm text-gray-600">Contacts</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-green-600">{totalCampaigns}</div>
              <div className="text-sm text-gray-600">Campaigns</div>
            </div>
          </div>

          {/* Next Action */}
          <div className="pt-2">
            {progressStep === 'contacts' && (
              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                <div>
                  <h4 className="font-medium">Ready for your first campaign?</h4>
                  <p className="text-sm text-gray-600">You have {totalContacts} contacts ready to receive emails</p>
                </div>
                <Link href="/campaigns">
                  <Button>Create Campaign</Button>
                </Link>
              </div>
            )}
            
            {progressStep === 'campaigns' && (
              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                <div>
                  <h4 className="font-medium">Check your campaign performance</h4>
                  <p className="text-sm text-gray-600">See how your emails are performing</p>
                </div>
                <Link href="/analytics">
                  <Button variant="outline">View Analytics</Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}