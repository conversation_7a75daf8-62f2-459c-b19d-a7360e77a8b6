import { render, screen } from '@testing-library/react'
import { PerformanceOverview } from '../performance-overview'
import { DashboardMetrics } from '../../../lib/analytics'

const mockMetrics: DashboardMetrics = {
  totalCampaigns: 5,
  totalContacts: 150,
  totalSends: 1000,
  totalOpens: 250,
  totalClicks: 50,
  averageOpenRate: 25.0,
  averageClickRate: 5.0,
  recentCampaigns: [],
}

const lowPerformanceMetrics: DashboardMetrics = {
  totalCampaigns: 3,
  totalContacts: 100,
  totalSends: 500,
  totalOpens: 50,
  totalClicks: 5,
  averageOpenRate: 10.0,
  averageClickRate: 1.0,
  recentCampaigns: [],
}

const emptyMetrics: DashboardMetrics = {
  totalCampaigns: 0,
  totalContacts: 0,
  totalSends: 0,
  totalOpens: 0,
  totalClicks: 0,
  averageOpenRate: 0,
  averageClickRate: 0,
  recentCampaigns: [],
}

describe('PerformanceOverview', () => {
  it('renders performance overview with correct title', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getByText('🎯 Performance Overview')).toBeInTheDocument()
    expect(screen.getByText('Email marketing performance insights and benchmarks')).toBeInTheDocument()
  })

  it('displays open rate performance with correct values', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getByText('Open Rate Performance')).toBeInTheDocument()
    expect(screen.getByText('25.0%')).toBeInTheDocument()
  })

  it('displays click rate performance with correct values', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getByText('Click Rate Performance')).toBeInTheDocument()
    expect(screen.getByText('5.0%')).toBeInTheDocument()
  })

  it('shows excellent performance badge for high open rates', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getAllByText('excellent').length).toBeGreaterThan(0)
  })

  it('shows needs improvement badge for low performance', () => {
    render(<PerformanceOverview metrics={lowPerformanceMetrics} />)

    const needsImprovementBadges = screen.getAllByText('needs improvement')
    expect(needsImprovementBadges.length).toBeGreaterThan(0)
  })

  it('displays key metrics correctly', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getByText('1,000')).toBeInTheDocument() // Total emails sent
    expect(screen.getByText('Total Emails Sent')).toBeInTheDocument()
    expect(screen.getByText('Engagement Rate')).toBeInTheDocument()
  })

  it('calculates engagement rate correctly', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    // Engagement rate = (opens + clicks) / sends * 100 = (250 + 50) / 1000 * 100 = 30%
    expect(screen.getByText('30.0%')).toBeInTheDocument()
  })

  it('shows performance tips for low open rates', () => {
    render(<PerformanceOverview metrics={lowPerformanceMetrics} />)

    expect(screen.getByText('💡 Performance Tips')).toBeInTheDocument()
    expect(screen.getByText(/try improving your subject lines/i)).toBeInTheDocument()
  })

  it('shows performance tips for low click rates', () => {
    render(<PerformanceOverview metrics={lowPerformanceMetrics} />)

    expect(screen.getByText(/add clear call-to-action buttons/i)).toBeInTheDocument()
  })

  it('shows encouragement for excellent performance', () => {
    const excellentMetrics: DashboardMetrics = {
      ...mockMetrics,
      averageOpenRate: 30.0,
      averageClickRate: 6.0,
      totalCampaigns: 10,
    }

    render(<PerformanceOverview metrics={excellentMetrics} />)

    expect(screen.getByText(/excellent performance! keep up the great work!/i)).toBeInTheDocument()
  })

  it('shows tip for new users with few campaigns', () => {
    const fewCampaignsMetrics: DashboardMetrics = {
      ...mockMetrics,
      totalCampaigns: 2,
    }

    render(<PerformanceOverview metrics={fewCampaignsMetrics} />)

    expect(screen.getByText(/send more campaigns to get better performance insights/i)).toBeInTheDocument()
  })

  it('shows loading state when loading prop is true', () => {
    render(<PerformanceOverview metrics={mockMetrics} loading={true} />)

    const loadingElements = screen.getAllByRole('generic')
    const animatedElements = loadingElements.filter(el => 
      el.className.includes('animate-pulse')
    )
    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('shows empty state when no sends exist', () => {
    render(<PerformanceOverview metrics={emptyMetrics} />)

    expect(screen.getByText('No performance data yet')).toBeInTheDocument()
    expect(screen.getByText('Send your first campaign to see performance insights')).toBeInTheDocument()
  })

  it('displays industry benchmarks', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    expect(screen.getByText('Industry avg: 20-25%')).toBeInTheDocument()
    expect(screen.getByText('Industry avg: 2-5%')).toBeInTheDocument()
  })

  it('shows progress bars with correct styling', () => {
    render(<PerformanceOverview metrics={mockMetrics} />)

    const progressBars = screen.getAllByRole('generic').filter(el => 
      el.className.includes('bg-green-500') && el.className.includes('h-2')
    )
    expect(progressBars.length).toBeGreaterThan(0)
  })

  it('handles zero engagement rate correctly', () => {
    render(<PerformanceOverview metrics={emptyMetrics} />)

    // Should not crash and should show empty state
    expect(screen.getByText('No performance data yet')).toBeInTheDocument()
  })

  it('formats large numbers with locale formatting', () => {
    const largeMetrics: DashboardMetrics = {
      ...mockMetrics,
      totalSends: 10000,
    }

    render(<PerformanceOverview metrics={largeMetrics} />)

    expect(screen.getByText('10,000')).toBeInTheDocument()
  })

  it('shows good performance badge for moderate rates', () => {
    const moderateMetrics: DashboardMetrics = {
      ...mockMetrics,
      averageOpenRate: 22.0,
      averageClickRate: 4.0,
    }

    render(<PerformanceOverview metrics={moderateMetrics} />)

    const goodBadges = screen.getAllByText('good')
    expect(goodBadges.length).toBe(2)
  })

  it('shows average performance badge for borderline rates', () => {
    const averageMetrics: DashboardMetrics = {
      ...mockMetrics,
      averageOpenRate: 17.0,
      averageClickRate: 2.5,
    }

    render(<PerformanceOverview metrics={averageMetrics} />)

    const averageBadges = screen.getAllByText('average')
    expect(averageBadges.length).toBe(2)
  })
})