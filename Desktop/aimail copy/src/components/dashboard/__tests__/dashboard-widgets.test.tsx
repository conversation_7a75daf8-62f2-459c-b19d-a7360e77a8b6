import { render, screen } from '@testing-library/react'
import { DashboardWidgets } from '../dashboard-widgets'
import { DashboardMetrics } from '../../../lib/analytics'

// Mock Next.js Link component
jest.mock('next/link', () => {
  const MockLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  )
  MockLink.displayName = 'MockLink'
  return MockLink
})

const mockMetrics: DashboardMetrics = {
  totalCampaigns: 5,
  totalContacts: 150,
  totalSends: 500,
  totalOpens: 125,
  totalClicks: 25,
  averageOpenRate: 25.0,
  averageClickRate: 5.0,
  recentCampaigns: [
    {
      campaignId: '1',
      campaignName: 'Test Campaign 1',
      subject: 'Test Subject 1',
      status: 'sent',
      sentAt: '2024-01-15T10:00:00Z',
      totalSends: 100,
      opens: 25,
      clicks: 5,
      bounces: 2,
      complaints: 0,
      openRate: 25.0,
      clickRate: 5.0,
      bounceRate: 2.0,
      complaintRate: 0.0,
      uniqueOpens: 23,
      uniqueClicks: 4,
    },
    {
      campaignId: '2',
      campaignName: 'Test Campaign 2',
      subject: 'Test Subject 2',
      status: 'sent',
      sentAt: '2024-01-10T14:30:00Z',
      totalSends: 200,
      opens: 50,
      clicks: 10,
      bounces: 1,
      complaints: 0,
      openRate: 25.0,
      clickRate: 5.0,
      bounceRate: 0.5,
      complaintRate: 0.0,
      uniqueOpens: 48,
      uniqueClicks: 9,
    },
  ],
}

const emptyMetrics: DashboardMetrics = {
  totalCampaigns: 0,
  totalContacts: 0,
  totalSends: 0,
  totalOpens: 0,
  totalClicks: 0,
  averageOpenRate: 0,
  averageClickRate: 0,
  recentCampaigns: [],
}

describe('DashboardWidgets', () => {
  it('renders metric cards with correct values', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    expect(screen.getByText('150')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getAllByText('25.0%').length).toBeGreaterThan(0)
    expect(screen.getAllByText('5.0%').length).toBeGreaterThan(0)
  })

  it('shows loading state when loading prop is true', () => {
    render(<DashboardWidgets metrics={mockMetrics} loading={true} />)

    const loadingElements = screen.getAllByRole('generic')
    const animatedElements = loadingElements.filter(el => 
      el.className.includes('animate-pulse')
    )
    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('displays email activity widget with correct data', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    expect(screen.getByText('📈 Email Activity')).toBeInTheDocument()
    expect(screen.getByText('500')).toBeInTheDocument() // Total sends
    expect(screen.getByText('125')).toBeInTheDocument() // Total opens
    expect(screen.getByText('25')).toBeInTheDocument() // Total clicks
  })

  it('shows quick actions widget with navigation links', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    expect(screen.getByText('⚡ Quick Actions')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /create new campaign/i })).toHaveAttribute('href', '/campaigns')
    expect(screen.getByRole('link', { name: /manage contacts/i })).toHaveAttribute('href', '/audience')
    expect(screen.getByRole('link', { name: /view analytics/i })).toHaveAttribute('href', '/analytics')
    expect(screen.getByRole('link', { name: /settings/i })).toHaveAttribute('href', '/settings')
  })

  it('displays recent campaigns with correct information', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    expect(screen.getByText('📋 Recent Campaigns')).toBeInTheDocument()
    expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    expect(screen.getByText('Test Subject 1')).toBeInTheDocument()
    expect(screen.getByText('Test Campaign 2')).toBeInTheDocument()
    expect(screen.getByText('Test Subject 2')).toBeInTheDocument()
  })

  it('shows empty state when no campaigns exist', () => {
    render(<DashboardWidgets metrics={emptyMetrics} />)

    expect(screen.getByText('No campaigns yet')).toBeInTheDocument()
    expect(screen.getByText('Create your first campaign to get started')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /create your first campaign/i })).toHaveAttribute('href', '/campaigns')
  })

  it('shows empty state for email activity when no sends', () => {
    render(<DashboardWidgets metrics={emptyMetrics} />)

    expect(screen.getByText('No email activity yet')).toBeInTheDocument()
    expect(screen.getByText('Send your first campaign to see metrics here')).toBeInTheDocument()
  })

  it('calculates engagement rate correctly', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    // Engagement rate = (opens + clicks) / sends * 100 = (125 + 25) / 500 * 100 = 30%
    expect(screen.getByText('30.0%')).toBeInTheDocument()
  })

  it('formats numbers correctly with locale formatting', () => {
    const largeMetrics: DashboardMetrics = {
      ...mockMetrics,
      totalContacts: 1500,
      totalSends: 5000,
    }

    render(<DashboardWidgets metrics={largeMetrics} />)

    expect(screen.getByText('1,500')).toBeInTheDocument()
    expect(screen.getByText('5,000')).toBeInTheDocument()
  })

  it('shows trend indicators for performance metrics', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    // Should show up trend for good open rate (25%)
    const trendElements = screen.getAllByText('↗️')
    expect(trendElements.length).toBeGreaterThan(0)
  })

  it('displays campaign status badges correctly', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    const sentBadges = screen.getAllByText('sent')
    expect(sentBadges.length).toBe(2)
  })

  it('formats dates correctly in recent campaigns', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    // Check that dates are formatted (exact format may vary by locale)
    expect(screen.getByText(/1\/15\/2024|15\/1\/2024|2024-01-15/)).toBeInTheDocument()
    expect(screen.getByText(/1\/10\/2024|10\/1\/2024|2024-01-10/)).toBeInTheDocument()
  })

  it('shows view all campaigns link', () => {
    render(<DashboardWidgets metrics={mockMetrics} />)

    expect(screen.getByRole('link', { name: /view all/i })).toHaveAttribute('href', '/campaigns')
  })
})