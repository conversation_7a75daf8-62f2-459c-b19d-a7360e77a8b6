import { render, screen } from '@testing-library/react'
import { WelcomeWidget } from '../welcome-widget'

// Mock Next.js Link component
jest.mock('next/link', () => {
  const MockLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  )
  MockLink.displayName = 'MockLink'
  return MockLink
})

describe('WelcomeWidget', () => {
  it('shows welcome message for new users with no contacts or campaigns', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={0} />)

    expect(screen.getByText(/welcome to your email marketing platform/i)).toBeInTheDocument()
    expect(screen.getByText(/let's get you started/i)).toBeInTheDocument()
  })

  it('displays onboarding steps for new users', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={0} />)

    expect(screen.getByText('Import your contacts')).toBeInTheDocument()
    expect(screen.getByText('Create your first campaign')).toBeInTheDocument()
    expect(screen.getByText('Track your results')).toBeInTheDocument()
  })

  it('shows start button for first step and disabled buttons for later steps', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={0} />)

    const startButton = screen.getByRole('link', { name: /start/i })
    expect(startButton).toHaveAttribute('href', '/audience')

    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).toBeDisabled()

    const laterButton = screen.getByRole('button', { name: /later/i })
    expect(laterButton).toBeDisabled()
  })

  it('shows progress message when user has contacts but no campaigns', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={50} />)

    expect(screen.getByText(/great progress/i)).toBeInTheDocument()
    expect(screen.getByText(/here's what you've accomplished/i)).toBeInTheDocument()
  })

  it('displays current stats for users with progress', () => {
    render(<WelcomeWidget totalCampaigns={3} totalContacts={150} />)

    expect(screen.getByText('150')).toBeInTheDocument()
    expect(screen.getByText('Contacts')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('Campaigns')).toBeInTheDocument()
  })

  it('shows progress steps with correct completion status', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={50} />)

    // Should show checkmarks for completed steps
    const checkmarks = screen.getAllByText('✓')
    expect(checkmarks.length).toBeGreaterThanOrEqual(1) // At least contacts step completed
  })

  it('shows next action for users with contacts but no campaigns', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={50} />)

    expect(screen.getByText('Ready for your first campaign?')).toBeInTheDocument()
    expect(screen.getByText('You have 50 contacts ready to receive emails')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /create campaign/i })).toHaveAttribute('href', '/campaigns')
  })

  it('shows analytics action for users with campaigns', () => {
    render(<WelcomeWidget totalCampaigns={3} totalContacts={150} />)

    expect(screen.getByText('Check your campaign performance')).toBeInTheDocument()
    expect(screen.getByText('See how your emails are performing')).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /view analytics/i })).toHaveAttribute('href', '/analytics')
  })

  it('includes user name in greeting when provided', () => {
    render(<WelcomeWidget userName="<EMAIL>" totalCampaigns={3} totalContacts={150} />)

    expect(screen.getByText(/great progress, john!/i)).toBeInTheDocument()
  })

  it('shows appropriate styling for new user welcome', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={0} />)

    const welcomeCard = screen.getByText(/welcome to your email marketing platform/i).closest('.bg-gradient-to-r')
    expect(welcomeCard).toHaveClass('from-blue-50', 'to-indigo-50')
  })

  it('shows appropriate styling for progress message', () => {
    render(<WelcomeWidget totalCampaigns={3} totalContacts={150} />)

    const progressCard = screen.getByText(/great progress/i).closest('.bg-gradient-to-r')
    expect(progressCard).toHaveClass('from-green-50', 'to-emerald-50')
  })

  it('handles edge case with zero values correctly', () => {
    render(<WelcomeWidget totalCampaigns={0} totalContacts={0} />)

    // Should show new user flow, not progress flow
    expect(screen.queryByText(/great progress/i)).not.toBeInTheDocument()
    expect(screen.getByText(/welcome to your email marketing platform/i)).toBeInTheDocument()
  })

  it('shows all progress steps as completed when user has campaigns', () => {
    render(<WelcomeWidget totalCampaigns={5} totalContacts={100} />)

    const checkmarks = screen.getAllByText('✓')
    expect(checkmarks.length).toBeGreaterThanOrEqual(2) // Both contacts and campaigns steps completed
  })
})