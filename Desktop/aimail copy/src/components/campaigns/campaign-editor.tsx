'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { <PERSON>rkles } from 'lucide-react'
import { campaignValidation } from '../../lib/campaigns'
import { AIGenerator } from './ai-generator'
import type { Campaign, CreateCampaignData } from '../../types/database'

interface CampaignEditorProps {
  campaign?: Campaign | null
  onSave: (data: CreateCampaignData | { id: string } & Partial<CreateCampaignData>) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function CampaignEditor({
  campaign,
  onSave,
  onCancel,
  loading = false
}: CampaignEditorProps) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    html_body: '',
    text_body: '',
    scheduled_at: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [previewMode, setPreviewMode] = useState<'edit' | 'preview'>('edit')
  const [showAIGenerator, setShowAIGenerator] = useState(false)

  const isEditing = !!campaign

  // Update form data when campaign prop changes
  useEffect(() => {
    if (campaign) {
      setFormData({
        name: campaign.name,
        subject: campaign.subject,
        html_body: campaign.html_body,
        text_body: campaign.text_body || '',
        scheduled_at: campaign.scheduled_at ? 
          new Date(campaign.scheduled_at).toISOString().slice(0, 16) : ''
      })
    }
  }, [campaign])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form data
    const validation = campaignValidation.validateCampaignData({
      user_id: 'temp', // Will be set by the API
      name: formData.name,
      subject: formData.subject,
      html_body: formData.html_body,
      text_body: formData.text_body || undefined,
      scheduled_at: formData.scheduled_at || undefined
    })

    if (!validation.isValid) {
      const newErrors: Record<string, string> = {}
      validation.errors.forEach(error => {
        if (error.includes('name')) {
          newErrors.name = error
        } else if (error.includes('Subject')) {
          newErrors.subject = error
        } else if (error.includes('HTML body')) {
          newErrors.html_body = error
        } else if (error.includes('Text body')) {
          newErrors.text_body = error
        } else if (error.includes('Scheduled')) {
          newErrors.scheduled_at = error
        }
      })
      setErrors(newErrors)
      return
    }

    setErrors({})

    try {
      if (isEditing) {
        await onSave({
          id: campaign.id,
          name: formData.name,
          subject: formData.subject,
          html_body: formData.html_body,
          text_body: formData.text_body || undefined,
          scheduled_at: formData.scheduled_at || undefined
        })
      } else {
        await onSave({
          user_id: 'temp', // Will be set by the API
          name: formData.name,
          subject: formData.subject,
          html_body: formData.html_body,
          text_body: formData.text_body || undefined,
          scheduled_at: formData.scheduled_at || undefined
        })
      }
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const generateTextFromHtml = () => {
    // Simple HTML to text conversion
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = formData.html_body
    const textContent = tempDiv.textContent || tempDiv.innerText || ''
    setFormData({ ...formData, text_body: textContent })
  }

  const getStatusBadge = () => {
    if (!campaign) return null
    
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      scheduled: 'bg-blue-100 text-blue-800',
      sending: 'bg-yellow-100 text-yellow-800',
      sent: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800'
    }

    return (
      <Badge className={statusColors[campaign.status]}>
        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
      </Badge>
    )
  }

  const canEdit = !campaign || (campaign.status !== 'sent' && campaign.status !== 'sending')

  const handleAIContentSelect = (subject: string, body: string) => {
    setFormData(prev => ({
      ...prev,
      subject: subject,
      html_body: body
    }))
    // Auto-generate text version from HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = body
    const textContent = tempDiv.textContent || tempDiv.innerText || ''
    setFormData(prev => ({
      ...prev,
      text_body: textContent
    }))
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">
            {isEditing ? 'Edit Campaign' : 'Create Campaign'}
          </h1>
          {getStatusBadge()}
        </div>
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setPreviewMode(previewMode === 'edit' ? 'preview' : 'edit')}
            disabled={!formData.html_body}
          >
            {previewMode === 'edit' ? 'Preview' : 'Edit'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        </div>
      </div>

      {previewMode === 'preview' ? (
        /* Preview Mode */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Email Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Subject:</Label>
                  <p className="text-lg font-semibold">{formData.subject || 'No subject'}</p>
                </div>
                <div className="border rounded-lg p-4 bg-white">
                  <div 
                    dangerouslySetInnerHTML={{ __html: formData.html_body || '<p>No content</p>' }}
                    className="prose max-w-none"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Plain Text Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-gray-50">
                <pre className="whitespace-pre-wrap text-sm">
                  {formData.text_body || 'No plain text version'}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Edit Mode */
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Campaign Details */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Campaign Name *</Label>
                  <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Monthly Newsletter"
                    className={errors.name ? 'border-red-500' : ''}
                    disabled={!canEdit}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scheduled_at">Schedule (Optional)</Label>
                  <Input
                    id="scheduled_at"
                    type="datetime-local"
                    value={formData.scheduled_at}
                    onChange={(e) => setFormData({ ...formData, scheduled_at: e.target.value })}
                    min={new Date().toISOString().slice(0, 16)}
                    className={errors.scheduled_at ? 'border-red-500' : ''}
                    disabled={!canEdit}
                  />
                  {errors.scheduled_at && (
                    <p className="text-sm text-red-600">{errors.scheduled_at}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Subject Line *</Label>
                <Input
                  id="subject"
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                  placeholder="Your amazing subject line"
                  className={errors.subject ? 'border-red-500' : ''}
                  disabled={!canEdit}
                />
                {errors.subject && (
                  <p className="text-sm text-red-600">{errors.subject}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Email Content */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Email Content</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAIGenerator(true)}
                  disabled={!canEdit}
                  className="flex items-center gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  Generate with AI
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="html_body">HTML Content *</Label>
                <Textarea
                  id="html_body"
                  value={formData.html_body}
                  onChange={(e) => setFormData({ ...formData, html_body: e.target.value })}
                  placeholder="<h1>Hello {{name}}!</h1><p>Your email content here...</p>"
                  className={`min-h-[300px] font-mono text-sm ${errors.html_body ? 'border-red-500' : ''}`}
                  disabled={!canEdit}
                />
                {errors.html_body && (
                  <p className="text-sm text-red-600">{errors.html_body}</p>
                )}
                <p className="text-xs text-gray-500">
                  Use HTML tags for formatting. Variables like {'{'}{'{'} name {'}'}{'}'}  will be replaced with contact data.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="text_body">Plain Text Version (Optional)</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={generateTextFromHtml}
                    disabled={!formData.html_body || !canEdit}
                  >
                    Generate from HTML
                  </Button>
                </div>
                <Textarea
                  id="text_body"
                  value={formData.text_body}
                  onChange={(e) => setFormData({ ...formData, text_body: e.target.value })}
                  placeholder="Plain text version of your email..."
                  className={`min-h-[200px] ${errors.text_body ? 'border-red-500' : ''}`}
                  disabled={!canEdit}
                />
                {errors.text_body && (
                  <p className="text-sm text-red-600">{errors.text_body}</p>
                )}
                <p className="text-xs text-gray-500">
                  A plain text version is recommended for better deliverability.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          {canEdit && (
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  isEditing ? 'Update Campaign' : 'Create Campaign'
                )}
              </Button>
            </div>
          )}
        </form>
      )}

      {/* AI Generator Modal */}
      <AIGenerator
        isOpen={showAIGenerator}
        onClose={() => setShowAIGenerator(false)}
        onContentSelect={handleAIContentSelect}
      />
    </div>
  )
}