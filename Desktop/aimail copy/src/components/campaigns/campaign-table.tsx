'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { CampaignPreview } from './campaign-preview'
import { DeleteCampaignDialog } from './delete-campaign-dialog'
import type { Campaign, CampaignStats } from '../../types/database'

interface CampaignTableProps {
  campaigns: Campaign[]
  campaignStats?: { [campaignId: string]: CampaignStats }
  onEdit: (campaign: Campaign) => void
  onDuplicate: (campaign: Campaign) => void
  onDelete: (campaignId: string) => Promise<void>
  onSendTest?: (campaign: Campaign, email: string) => Promise<void>
  loading?: boolean
}

export function CampaignTable({
  campaigns,
  campaignStats = {},
  onEdit,
  onDuplicate,
  onDelete,
  onSendTest,
  loading = false
}: CampaignTableProps) {
  const [previewCampaign, setPreviewCampaign] = useState<Campaign | null>(null)
  const [deleteCampaign, setDeleteCampaign] = useState<Campaign | null>(null)

  const getStatusBadge = (status: Campaign['status']) => {
    const statusConfig = {
      draft: { label: 'Draft', className: 'bg-gray-100 text-gray-800' },
      scheduled: { label: 'Scheduled', className: 'bg-blue-100 text-blue-800' },
      sending: { label: 'Sending', className: 'bg-yellow-100 text-yellow-800' },
      sent: { label: 'Sent', className: 'bg-green-100 text-green-800' },
      failed: { label: 'Failed', className: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[status]
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStats = (campaignId: string) => {
    const stats = campaignStats[campaignId]
    if (!stats) return null

    return {
      sends: stats.total_sends,
      opens: stats.opens,
      clicks: stats.clicks,
      openRate: stats.open_rate,
      clickRate: stats.click_rate
    }
  }

  const canEdit = (campaign: Campaign) => {
    return campaign.status !== 'sent' && campaign.status !== 'sending'
  }

  const canDelete = (campaign: Campaign) => {
    return campaign.status !== 'sent' && campaign.status !== 'sending'
  }

  if (campaigns.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns yet</h3>
        <p className="text-gray-500 mb-4">Create your first email campaign to get started.</p>
      </div>
    )
  }

  return (
    <>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Campaign</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Scheduled</TableHead>
              <TableHead>Stats</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {campaigns.map((campaign) => {
              const stats = getStats(campaign.id)
              
              return (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      <div className="text-sm text-gray-500 truncate max-w-[300px]">
                        {campaign.subject}
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    {getStatusBadge(campaign.status)}
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-500">
                    {formatDate(campaign.created_at)}
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-500">
                    {campaign.scheduled_at ? formatDate(campaign.scheduled_at) : '-'}
                  </TableCell>
                  
                  <TableCell>
                    {stats ? (
                      <div className="text-sm">
                        <div>{stats.sends} sends</div>
                        <div className="text-gray-500">
                          {stats.openRate}% opens, {stats.clickRate}% clicks
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" disabled={loading}>
                          ⋯
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setPreviewCampaign(campaign)}>
                          Preview
                        </DropdownMenuItem>
                        
                        {canEdit(campaign) && (
                          <DropdownMenuItem onClick={() => onEdit(campaign)}>
                            Edit
                          </DropdownMenuItem>
                        )}
                        
                        <DropdownMenuItem onClick={() => onDuplicate(campaign)}>
                          Duplicate
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        
                        {canDelete(campaign) && (
                          <DropdownMenuItem 
                            onClick={() => setDeleteCampaign(campaign)}
                            className="text-red-600"
                          >
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* Preview Dialog */}
      {previewCampaign && (
        <CampaignPreview
          open={true}
          onClose={() => setPreviewCampaign(null)}
          campaign={previewCampaign}
          onSendTest={onSendTest ? (email) => onSendTest(previewCampaign, email) : undefined}
        />
      )}

      {/* Delete Dialog */}
      {deleteCampaign && (
        <DeleteCampaignDialog
          open={true}
          onClose={() => setDeleteCampaign(null)}
          campaign={deleteCampaign}
          onConfirm={async () => {
            await onDelete(deleteCampaign.id)
            setDeleteCampaign(null)
          }}
        />
      )}
    </>
  )
}