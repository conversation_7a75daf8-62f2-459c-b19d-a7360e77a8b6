import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AIGenerator } from '../ai-generator'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

// Mock fetch
global.fetch = jest.fn()

// Mock clipboard API
const mockWriteText = jest.fn(() => Promise.resolve())
Object.assign(navigator, {
  clipboard: {
    writeText: mockWriteText
  }
})

// Mock toast
const mockToast = jest.fn()
jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: mockToast })
}))

describe('AIGenerator', () => {
  const mockOnContentSelect = jest.fn()
  const mockOnClose = jest.fn()

  const defaultProps = {
    onContentSelect: mockOnContentSelect,
    isOpen: true,
    onClose: mockOnClose
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockClear()
    mockWriteText.mockClear()
  })

  it('should not render when isOpen is false', () => {
    render(<AIGenerator {...defaultProps} isOpen={false} />)
    expect(screen.queryByText('AI Email Generator')).not.toBeInTheDocument()
  })

  it('should render form fields when open', () => {
    render(<AIGenerator {...defaultProps} />)
    
    expect(screen.getByText('AI Email Generator')).toBeInTheDocument()
    expect(screen.getByLabelText(/Product\/Offer/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Campaign Goal/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Target Audience/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Tone/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Generate Content/ })).toBeInTheDocument()
  })

  it('should show validation error for empty required fields', async () => {
    const user = userEvent.setup()
    render(<AIGenerator {...defaultProps} />)
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    await user.click(generateButton)
    
    expect(mockToast).toHaveBeenCalledWith({
      title: 'Missing Information',
      description: 'Please provide both product/offer and goal information.',
      variant: 'destructive'
    })
  })

  it('should disable generate button when fields are empty', () => {
    render(<AIGenerator {...defaultProps} />)
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    expect(generateButton).toBeDisabled()
  })

  it('should enable generate button when required fields are filled', async () => {
    const user = userEvent.setup()
    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    expect(generateButton).not.toBeDisabled()
  })

  it('should show character count for text areas', async () => {
    const user = userEvent.setup()
    render(<AIGenerator {...defaultProps} />)
    
    const productField = screen.getByLabelText(/Product\/Offer/)
    await user.type(productField, 'Test product')
    
    expect(screen.getByText('12/500 characters')).toBeInTheDocument()
  })

  it('should generate AI content successfully', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1', 'Subject 2', 'Subject 3'],
        bodies: ['<p>Body 1</p>', '<p>Body 2</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(screen.getByText('Subject Lines')).toBeInTheDocument()
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
      expect(screen.getByText('Subject 2')).toBeInTheDocument()
      expect(screen.getByText('Subject 3')).toBeInTheDocument()
      expect(screen.getByText('Email Bodies')).toBeInTheDocument()
    })

    expect(mockToast).toHaveBeenCalledWith({
      title: 'Content Generated!',
      description: 'AI has generated email content options for you.'
    })
  })

  it('should handle API errors gracefully', async () => {
    const user = userEvent.setup()
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({
        message: 'API rate limit exceeded'
      })
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    await user.click(generateButton)
    
    await waitFor(() => {
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Generation Failed',
        description: 'API rate limit exceeded',
        variant: 'destructive'
      })
    })
  })

  it('should show loading state during generation', async () => {
    const user = userEvent.setup()
    ;(fetch as jest.Mock).mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    
    const generateButton = screen.getByRole('button', { name: /Generate Content/ })
    await user.click(generateButton)
    
    expect(screen.getByText('Generating...')).toBeInTheDocument()
    expect(generateButton).toBeDisabled()
  })

  it('should allow selecting subject and body', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1', 'Subject 2'],
        bodies: ['<p>Body 1</p>', '<p>Body 2</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    await user.click(screen.getByRole('button', { name: /Generate Content/ }))
    
    await waitFor(() => {
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
    })

    // Select subject and body
    await user.click(screen.getByText('Subject 1'))
    await user.click(screen.getByText('Version 1'))
    
    expect(screen.getAllByText('Selected')).toHaveLength(2)
  })

  it('should copy content to clipboard', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1'],
        bodies: ['<p>Body 1</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    await user.click(screen.getByRole('button', { name: /Generate Content/ }))
    
    await waitFor(() => {
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
    })

    // Click copy button
    const copyButtons = screen.getAllByRole('button')
    const copyButton = copyButtons.find(btn => btn.querySelector('svg'))
    if (copyButton) {
      await user.click(copyButton)
    }

    expect(mockWriteText).toHaveBeenCalled()
  })

  it('should use selected content', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1'],
        bodies: ['<p>Body 1</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    await user.click(screen.getByRole('button', { name: /Generate Content/ }))
    
    await waitFor(() => {
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
    })

    // Select content
    await user.click(screen.getByText('Subject 1'))
    await user.click(screen.getByText('Version 1'))
    
    // Use content
    await user.click(screen.getByRole('button', { name: /Use Selected Content/ }))
    
    expect(mockOnContentSelect).toHaveBeenCalledWith('Subject 1', '<p>Body 1</p>')
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('should show error when trying to use content without selection', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1'],
        bodies: ['<p>Body 1</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    await user.click(screen.getByRole('button', { name: /Generate Content/ }))
    
    await waitFor(() => {
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
    })

    // Try to use without selection
    await user.click(screen.getByRole('button', { name: /Use Selected Content/ }))
    
    expect(mockToast).toHaveBeenCalledWith({
      title: 'Selection Required',
      description: 'Please select both a subject line and email body.',
      variant: 'destructive'
    })
  })

  it('should reset form and content', async () => {
    const user = userEvent.setup()
    const mockResponse = {
      success: true,
      data: {
        subjects: ['Subject 1'],
        bodies: ['<p>Body 1</p>']
      }
    }

    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    })

    render(<AIGenerator {...defaultProps} />)
    
    await user.type(screen.getByLabelText(/Product\/Offer/), 'Test product')
    await user.type(screen.getByLabelText(/Campaign Goal/), 'Test goal')
    await user.click(screen.getByRole('button', { name: /Generate Content/ }))
    
    await waitFor(() => {
      expect(screen.getByText('Subject 1')).toBeInTheDocument()
    })

    // Reset
    await user.click(screen.getByRole('button', { name: /Reset/ }))
    
    expect(screen.queryByText('Subject Lines')).not.toBeInTheDocument()
    expect(screen.getByLabelText(/Product\/Offer/)).toHaveValue('')
  })

  it('should close modal when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<AIGenerator {...defaultProps} />)
    
    await user.click(screen.getByRole('button', { name: '✕' }))
    
    expect(mockOnClose).toHaveBeenCalled()
  })
})