import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CampaignForm } from '../campaign-form'
import type { Campaign } from '../../../types/database'

// Mock the campaign validation
jest.mock('../../../lib/campaigns', () => ({
  campaignValidation: {
    validateCampaignData: jest.fn(() => ({ isValid: true, errors: [] }))
  }
}))

// Mock the Dialog components to avoid Radix UI issues in tests
jest.mock('../../ui/dialog', () => ({
  Dialog: ({ children, open }: { children: React.ReactNode; open: boolean }) => 
    open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: { children: React.ReactNode }) => 
    <h2 data-testid="dialog-title">{children}</h2>,
  DialogDescription: ({ children }: { children: React.ReactNode }) => 
    <p data-testid="dialog-description">{children}</p>,
  DialogFooter: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="dialog-footer">{children}</div>
}))

// Mock the Textarea component
jest.mock('../../ui/textarea', () => ({
  Textarea: React.forwardRef<HTMLTextAreaElement, React.ComponentProps<"textarea">>(
    ({ className, ...props }, ref) => (
      <textarea ref={ref} className={className} {...props} />
    )
  )
}))

// Mock the Select components
jest.mock('../../ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <select onChange={(e) => onValueChange?.(e.target.value)} value={value}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SelectItem: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <option value={value}>{children}</option>
  ),
  SelectTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SelectValue: ({ placeholder }: { placeholder?: string }) => <span>{placeholder}</span>
}))

const mockCampaign: Campaign = {
  id: 'campaign-123',
  user_id: 'user-123',
  name: 'Test Campaign',
  subject: 'Test Subject',
  html_body: '<p>Test HTML content</p>',
  text_body: 'Test text content',
  status: 'draft',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

describe('CampaignForm', () => {
  const mockOnSubmit = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders create mode correctly', () => {
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    expect(screen.getByText('Create New Campaign')).toBeInTheDocument()
    expect(screen.getByLabelText('Campaign Name *')).toBeInTheDocument()
    expect(screen.getByLabelText('Subject Line *')).toBeInTheDocument()
    expect(screen.getByLabelText('HTML Content *')).toBeInTheDocument()
    expect(screen.getByText('Create Campaign')).toBeInTheDocument()
  })

  it('renders edit mode correctly', () => {
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
        campaign={mockCampaign}
      />
    )

    expect(screen.getByText('Edit Campaign')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test Campaign')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test Subject')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<p>Test HTML content</p>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test text content')).toBeInTheDocument()
    expect(screen.getByText('Update Campaign')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(
      <CampaignForm
        open={false}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    expect(screen.queryByText('Create New Campaign')).not.toBeInTheDocument()
  })

  it('generates text from HTML', async () => {
    const user = userEvent.setup()
    
    // Mock DOM methods
    const mockDiv = {
      innerHTML: '',
      textContent: 'Generated text content',
      innerText: 'Generated text content'
    }
    jest.spyOn(document, 'createElement').mockReturnValue(mockDiv as any)
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Add HTML content
    const htmlTextarea = screen.getByLabelText('HTML Content *')
    await user.type(htmlTextarea, '<p>Test HTML</p>')
    
    // Click generate from HTML button
    await user.click(screen.getByText('Generate from HTML'))
    
    // Should populate text area
    expect(screen.getByLabelText('Plain Text Version (Optional)')).toHaveValue('Generated text content')
  })

  it('calls onSubmit with correct data for new campaign', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Fill form
    await user.type(screen.getByLabelText('Campaign Name *'), 'New Campaign')
    await user.type(screen.getByLabelText('Subject Line *'), 'New Subject')
    await user.type(screen.getByLabelText('HTML Content *'), '<p>New HTML</p>')
    await user.type(screen.getByLabelText('Plain Text Version (Optional)'), 'New text')
    
    // Submit form
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        user_id: 'temp',
        name: 'New Campaign',
        subject: 'New Subject',
        html_body: '<p>New HTML</p>',
        text_body: 'New text',
        scheduled_at: undefined
      })
    })
  })

  it('calls onSubmit with correct data for existing campaign', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
        campaign={mockCampaign}
      />
    )

    // Update name
    const nameInput = screen.getByLabelText('Campaign Name *')
    await user.clear(nameInput)
    await user.type(nameInput, 'Updated Campaign')
    
    // Submit form
    await user.click(screen.getByText('Update Campaign'))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        id: 'campaign-123',
        name: 'Updated Campaign',
        subject: 'Test Subject',
        html_body: '<p>Test HTML content</p>',
        text_body: 'Test text content',
        scheduled_at: undefined
      })
    })
  })

  it('calls onClose when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    await user.click(screen.getByText('Cancel'))
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('calls onClose when dialog is closed', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Simulate dialog close (ESC key)
    fireEvent.keyDown(document, { key: 'Escape' })
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('shows loading state', () => {
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
        loading={true}
      />
    )

    expect(screen.getByText('Creating...')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeDisabled()
    expect(screen.getByText('Preview')).toBeDisabled()
  })

  it('handles scheduled date correctly', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Set scheduled date
    const scheduledInput = screen.getByLabelText('Schedule (Optional)')
    await user.type(scheduledInput, '2024-12-25T10:00')
    
    // Fill required fields
    await user.type(screen.getByLabelText('Campaign Name *'), 'Scheduled Campaign')
    await user.type(screen.getByLabelText('Subject Line *'), 'Scheduled Subject')
    await user.type(screen.getByLabelText('HTML Content *'), '<p>Scheduled HTML</p>')
    
    // Submit form
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          scheduled_at: '2024-12-25T10:00'
        })
      )
    })
  })

  it('displays validation errors', async () => {
    const { campaignValidation } = require('../../../lib/campaigns')
    campaignValidation.validateCampaignData.mockReturnValue({
      isValid: false,
      errors: ['Campaign name is required', 'Subject line is required', 'HTML body is required']
    })

    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Submit form without filling required fields
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(screen.getByText('Campaign name is required')).toBeInTheDocument()
      expect(screen.getByText('Subject line is required')).toBeInTheDocument()
      expect(screen.getByText('HTML body is required')).toBeInTheDocument()
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('resets form when closed', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Fill form
    await user.type(screen.getByLabelText('Campaign Name *'), 'Test Campaign')
    
    // Close form
    await user.click(screen.getByText('Cancel'))
    
    expect(mockOnClose).toHaveBeenCalled()
    
    // Reopen form - should be empty
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )
    
    expect(screen.getByLabelText('Campaign Name *')).toHaveValue('')
  })

  it('updates form data when campaign prop changes', () => {
    const { rerender } = render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Initially empty
    expect(screen.getByLabelText('Campaign Name *')).toHaveValue('')
    
    // Update with campaign data
    rerender(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
        campaign={mockCampaign}
      />
    )
    
    expect(screen.getByLabelText('Campaign Name *')).toHaveValue('Test Campaign')
    expect(screen.getByLabelText('Subject Line *')).toHaveValue('Test Subject')
  })

  it('disables preview button when no HTML content', () => {
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    expect(screen.getByText('Preview')).toBeDisabled()
  })

  it('enables preview button when HTML content exists', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignForm
        open={true}
        onClose={mockOnClose}
        onSubmit={mockOnSubmit}
      />
    )

    // Add HTML content
    await user.type(screen.getByLabelText('HTML Content *'), '<p>Test HTML</p>')
    
    expect(screen.getByText('Preview')).not.toBeDisabled()
  })
})