import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CampaignManager } from '../campaign-manager'
import type { Campaign, CampaignStats } from '../../../types/database'

// Mock all the UI components
jest.mock('../../ui/button', () => ({
  Button: ({ children, onClick, disabled, variant }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant}>
      {children}
    </button>
  )
}))

jest.mock('../../ui/input', () => ({
  Input: ({ value, onChange, placeholder }: any) => (
    <input 
      value={value} 
      onChange={onChange} 
      placeholder={placeholder}
    />
  )
}))

jest.mock('../../ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <select onChange={(e) => onValueChange?.(e.target.value)} value={value}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  SelectItem: ({ children, value }: { children: React.ReactNode; value: string }) => (
    <option value={value}>{children}</option>
  ),
  SelectTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SelectValue: ({ placeholder }: { placeholder?: string }) => <span>{placeholder}</span>
}))

// Mock the child components
jest.mock('../campaign-table', () => ({
  CampaignTable: ({ campaigns, onEdit, onDuplicate, onDelete }: any) => (
    <div data-testid="campaign-table">
      {campaigns.map((campaign: Campaign) => (
        <div key={campaign.id} data-testid={`campaign-${campaign.id}`}>
          <span>{campaign.name}</span>
          <button onClick={() => onEdit(campaign)}>Edit</button>
          <button onClick={() => onDuplicate(campaign)}>Duplicate</button>
          <button onClick={() => onDelete(campaign.id)}>Delete</button>
        </div>
      ))}
    </div>
  )
}))

jest.mock('../campaign-form', () => ({
  CampaignForm: ({ open, onSubmit, onClose }: any) => 
    open ? (
      <div data-testid="campaign-form">
        <button onClick={() => onSubmit({ name: 'Test Campaign', subject: 'Test', html_body: '<p>Test</p>' })}>
          Submit
        </button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null
}))

jest.mock('../campaign-editor', () => ({
  CampaignEditor: ({ campaign, onSave, onCancel }: any) => (
    <div data-testid="campaign-editor">
      <span>Editing: {campaign?.name || 'New Campaign'}</span>
      <button onClick={() => onSave({ id: campaign?.id, name: 'Updated Campaign' })}>
        Save
      </button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}))

const mockCampaigns: Campaign[] = [
  {
    id: 'campaign-1',
    user_id: 'user-1',
    name: 'Test Campaign 1',
    subject: 'Test Subject 1',
    html_body: '<p>Test HTML 1</p>',
    status: 'draft',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 'campaign-2',
    user_id: 'user-1',
    name: 'Test Campaign 2',
    subject: 'Test Subject 2',
    html_body: '<p>Test HTML 2</p>',
    status: 'sent',
    created_at: '2023-01-02T00:00:00Z',
    updated_at: '2023-01-02T00:00:00Z'
  }
]

const mockStats: { [campaignId: string]: CampaignStats } = {
  'campaign-1': {
    campaign_id: 'campaign-1',
    total_sends: 100,
    successful_sends: 95,
    failed_sends: 5,
    bounced_sends: 2,
    opens: 50,
    clicks: 10,
    open_rate: 52.63,
    click_rate: 10.53,
    bounce_rate: 2.0
  }
}

describe('CampaignManager', () => {
  const mockProps = {
    onCreateCampaign: jest.fn(),
    onUpdateCampaign: jest.fn(),
    onDeleteCampaign: jest.fn(),
    onDuplicateCampaign: jest.fn(),
    onSendTestEmail: jest.fn(),
    onLoadCampaigns: jest.fn(),
    onLoadCampaignStats: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockProps.onLoadCampaigns.mockResolvedValue({
      campaigns: mockCampaigns,
      count: mockCampaigns.length
    })
    mockProps.onLoadCampaignStats.mockResolvedValue(mockStats)
  })

  it('renders campaign manager correctly', async () => {
    render(<CampaignManager {...mockProps} />)

    expect(screen.getByText('Campaigns')).toBeInTheDocument()
    expect(screen.getByText('Create and manage your email campaigns')).toBeInTheDocument()
    expect(screen.getByText('Create Campaign')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search campaigns...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByTestId('campaign-table')).toBeInTheDocument()
    })
  })

  it('loads campaigns on mount', async () => {
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(mockProps.onLoadCampaigns).toHaveBeenCalledWith({})
    })

    expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    expect(screen.getByText('Test Campaign 2')).toBeInTheDocument()
  })

  it('loads campaign stats after campaigns load', async () => {
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(mockProps.onLoadCampaignStats).toHaveBeenCalledWith(['campaign-1', 'campaign-2'])
    })
  })

  it('opens create form when create button is clicked', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    await user.click(screen.getByText('Create Campaign'))

    expect(screen.getByTestId('campaign-form')).toBeInTheDocument()
  })

  it('creates new campaign', async () => {
    const user = userEvent.setup()
    const newCampaign = { ...mockCampaigns[0], id: 'new-campaign' }
    mockProps.onCreateCampaign.mockResolvedValue(newCampaign)

    render(<CampaignManager {...mockProps} />)

    await user.click(screen.getByText('Create Campaign'))
    await user.click(screen.getByText('Submit'))

    await waitFor(() => {
      expect(mockProps.onCreateCampaign).toHaveBeenCalledWith({
        name: 'Test Campaign',
        subject: 'Test',
        html_body: '<p>Test</p>'
      })
    })
  })

  it('opens editor when edit is clicked', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Edit')[0])

    expect(screen.getByTestId('campaign-editor')).toBeInTheDocument()
    expect(screen.getByText('Editing: Test Campaign 1')).toBeInTheDocument()
  })

  it('updates campaign', async () => {
    const user = userEvent.setup()
    const updatedCampaign = { ...mockCampaigns[0], name: 'Updated Campaign' }
    mockProps.onUpdateCampaign.mockResolvedValue(updatedCampaign)

    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Edit')[0])
    await user.click(screen.getByText('Save'))

    await waitFor(() => {
      expect(mockProps.onUpdateCampaign).toHaveBeenCalledWith({
        id: 'campaign-1',
        name: 'Updated Campaign'
      })
    })
  })

  it('duplicates campaign', async () => {
    const user = userEvent.setup()
    const duplicatedCampaign = { ...mockCampaigns[0], id: 'duplicated-campaign' }
    mockProps.onDuplicateCampaign.mockResolvedValue(duplicatedCampaign)

    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Duplicate')[0])

    await waitFor(() => {
      expect(mockProps.onDuplicateCampaign).toHaveBeenCalledWith('campaign-1', 'Test Campaign 1 (Copy)')
    })
  })

  it('deletes campaign', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Delete')[0])

    await waitFor(() => {
      expect(mockProps.onDeleteCampaign).toHaveBeenCalledWith('campaign-1')
    })
  })

  it('filters campaigns by search query', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    const searchInput = screen.getByPlaceholderText('Search campaigns...')
    await user.type(searchInput, 'test query')

    await waitFor(() => {
      expect(mockProps.onLoadCampaigns).toHaveBeenCalledWith({
        search: 'test query'
      })
    })
  })

  it('filters campaigns by status', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    const statusSelect = screen.getByDisplayValue('Filter by status')
    await user.selectOptions(statusSelect, 'draft')

    await waitFor(() => {
      expect(mockProps.onLoadCampaigns).toHaveBeenCalledWith({
        status: 'draft'
      })
    })
  })

  it('refreshes campaigns when refresh button is clicked', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    await user.click(screen.getByText('Refresh'))

    await waitFor(() => {
      expect(mockProps.onLoadCampaigns).toHaveBeenCalledTimes(2) // Once on mount, once on refresh
    })
  })

  it('handles campaign loading errors', async () => {
    mockProps.onLoadCampaigns.mockRejectedValue(new Error('Failed to load campaigns'))
    
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Error')).toBeInTheDocument()
      expect(screen.getByText('Failed to load campaigns')).toBeInTheDocument()
    })
  })

  it('handles campaign creation errors', async () => {
    const user = userEvent.setup()
    mockProps.onCreateCampaign.mockRejectedValue(new Error('Failed to create campaign'))

    render(<CampaignManager {...mockProps} />)

    await user.click(screen.getByText('Create Campaign'))
    
    // The form should handle the error, so we just verify the method was called
    await user.click(screen.getByText('Submit'))

    await waitFor(() => {
      expect(mockProps.onCreateCampaign).toHaveBeenCalled()
    })
  })

  it('cancels editor and returns to list view', async () => {
    const user = userEvent.setup()
    render(<CampaignManager {...mockProps} />)

    await waitFor(() => {
      expect(screen.getByText('Test Campaign 1')).toBeInTheDocument()
    })

    await user.click(screen.getAllByText('Edit')[0])
    expect(screen.getByTestId('campaign-editor')).toBeInTheDocument()

    await user.click(screen.getByText('Cancel'))
    expect(screen.queryByTestId('campaign-editor')).not.toBeInTheDocument()
    expect(screen.getByTestId('campaign-table')).toBeInTheDocument()
  })
})