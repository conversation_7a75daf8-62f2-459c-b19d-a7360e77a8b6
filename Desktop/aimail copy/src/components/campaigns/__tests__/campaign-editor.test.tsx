import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CampaignEditor } from '../campaign-editor'
import type { Campaign } from '../../../types/database'

// Mock the campaign validation
jest.mock('../../../lib/campaigns', () => ({
  campaignValidation: {
    validateCampaignData: jest.fn(() => ({ isValid: true, errors: [] }))
  }
}))

// Mock the Card components
jest.mock('../../ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="card">{children}</div>,
  CardContent: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="card-content">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => 
    <h3 data-testid="card-title">{children}</h3>
}))

// Mock the Badge component
jest.mock('../../ui/badge', () => ({
  Badge: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <span data-testid="badge" className={className}>{children}</span>
}))

const mockCampaign: Campaign = {
  id: 'campaign-123',
  user_id: 'user-123',
  name: 'Test Campaign',
  subject: 'Test Subject',
  html_body: '<p>Test HTML content</p>',
  text_body: 'Test text content',
  status: 'draft',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

describe('CampaignEditor', () => {
  const mockOnSave = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders create mode correctly', () => {
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByText('Create Campaign')).toBeInTheDocument()
    expect(screen.getByLabelText('Campaign Name *')).toBeInTheDocument()
    expect(screen.getByLabelText('Subject Line *')).toBeInTheDocument()
    expect(screen.getByLabelText('HTML Content *')).toBeInTheDocument()
    expect(screen.getByText('Create Campaign')).toBeInTheDocument()
  })

  it('renders edit mode correctly', () => {
    render(
      <CampaignEditor
        campaign={mockCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByText('Edit Campaign')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test Campaign')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test Subject')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<p>Test HTML content</p>')).toBeInTheDocument()
    expect(screen.getByDisplayValue('Test text content')).toBeInTheDocument()
    expect(screen.getByText('Update Campaign')).toBeInTheDocument()
  })

  it('shows campaign status badge in edit mode', () => {
    render(
      <CampaignEditor
        campaign={mockCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByText('Draft')).toBeInTheDocument()
  })

  it('disables editing for sent campaigns', () => {
    const sentCampaign = { ...mockCampaign, status: 'sent' as const }
    
    render(
      <CampaignEditor
        campaign={sentCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByLabelText('Campaign Name *')).toBeDisabled()
    expect(screen.getByLabelText('Subject Line *')).toBeDisabled()
    expect(screen.getByLabelText('HTML Content *')).toBeDisabled()
    expect(screen.queryByText('Update Campaign')).not.toBeInTheDocument()
  })

  it('disables editing for campaigns being sent', () => {
    const sendingCampaign = { ...mockCampaign, status: 'sending' as const }
    
    render(
      <CampaignEditor
        campaign={sendingCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    expect(screen.getByLabelText('Campaign Name *')).toBeDisabled()
    expect(screen.getByLabelText('Subject Line *')).toBeDisabled()
    expect(screen.getByLabelText('HTML Content *')).toBeDisabled()
    expect(screen.queryByText('Update Campaign')).not.toBeInTheDocument()
  })

  it('toggles between edit and preview modes', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        campaign={mockCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Initially in edit mode
    expect(screen.getByLabelText('Campaign Name *')).toBeInTheDocument()
    
    // Click preview button
    await user.click(screen.getByText('Preview'))
    
    // Should show preview mode
    expect(screen.getByText('Email Preview')).toBeInTheDocument()
    expect(screen.getByText('Plain Text Preview')).toBeInTheDocument()
    expect(screen.queryByLabelText('Campaign Name *')).not.toBeInTheDocument()
    
    // Click edit button
    await user.click(screen.getByText('Edit'))
    
    // Should return to edit mode
    expect(screen.getByLabelText('Campaign Name *')).toBeInTheDocument()
  })

  it('generates text from HTML', async () => {
    const user = userEvent.setup()
    
    // Mock DOM methods
    const mockDiv = {
      innerHTML: '',
      textContent: 'Generated text content',
      innerText: 'Generated text content'
    }
    jest.spyOn(document, 'createElement').mockReturnValue(mockDiv as any)
    
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Add HTML content
    const htmlTextarea = screen.getByLabelText('HTML Content *')
    await user.type(htmlTextarea, '<p>Test HTML</p>')
    
    // Click generate from HTML button
    await user.click(screen.getByText('Generate from HTML'))
    
    // Should populate text area
    expect(screen.getByLabelText('Plain Text Version (Optional)')).toHaveValue('Generated text content')
  })

  it('calls onSave with correct data for new campaign', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Fill form
    await user.type(screen.getByLabelText('Campaign Name *'), 'New Campaign')
    await user.type(screen.getByLabelText('Subject Line *'), 'New Subject')
    await user.type(screen.getByLabelText('HTML Content *'), '<p>New HTML</p>')
    
    // Submit form
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        user_id: 'temp',
        name: 'New Campaign',
        subject: 'New Subject',
        html_body: '<p>New HTML</p>',
        text_body: undefined,
        scheduled_at: undefined
      })
    })
  })

  it('calls onSave with correct data for existing campaign', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        campaign={mockCampaign}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Update name
    const nameInput = screen.getByLabelText('Campaign Name *')
    await user.clear(nameInput)
    await user.type(nameInput, 'Updated Campaign')
    
    // Submit form
    await user.click(screen.getByText('Update Campaign'))
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        id: 'campaign-123',
        name: 'Updated Campaign',
        subject: 'Test Subject',
        html_body: '<p>Test HTML content</p>',
        text_body: 'Test text content',
        scheduled_at: undefined
      })
    })
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    await user.click(screen.getByText('Cancel'))
    
    expect(mockOnCancel).toHaveBeenCalled()
  })

  it('shows loading state', () => {
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        loading={true}
      />
    )

    expect(screen.getByText('Creating...')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeDisabled()
  })

  it('handles scheduled date correctly', async () => {
    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Set scheduled date
    const scheduledInput = screen.getByLabelText('Schedule (Optional)')
    await user.type(scheduledInput, '2024-12-25T10:00')
    
    // Fill required fields
    await user.type(screen.getByLabelText('Campaign Name *'), 'Scheduled Campaign')
    await user.type(screen.getByLabelText('Subject Line *'), 'Scheduled Subject')
    await user.type(screen.getByLabelText('HTML Content *'), '<p>Scheduled HTML</p>')
    
    // Submit form
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          scheduled_at: '2024-12-25T10:00'
        })
      )
    })
  })

  it('displays validation errors', async () => {
    const { campaignValidation } = require('../../../lib/campaigns')
    campaignValidation.validateCampaignData.mockReturnValue({
      isValid: false,
      errors: ['Campaign name is required', 'Subject line is required']
    })

    const user = userEvent.setup()
    
    render(
      <CampaignEditor
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    )

    // Submit form without filling required fields
    await user.click(screen.getByText('Create Campaign'))
    
    await waitFor(() => {
      expect(screen.getByText('Campaign name is required')).toBeInTheDocument()
      expect(screen.getByText('Subject line is required')).toBeInTheDocument()
    })
    
    expect(mockOnSave).not.toHaveBeenCalled()
  })
})