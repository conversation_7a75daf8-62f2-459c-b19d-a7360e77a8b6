'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { campaignValidation } from '../../lib/campaigns'
import type { Campaign, CreateCampaignData } from '../../types/database'

interface CampaignFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: CreateCampaignData | { id: string } & Partial<CreateCampaignData>) => Promise<void>
  campaign?: Campaign | null
  loading?: boolean
}

export function CampaignForm({
  open,
  onClose,
  onSubmit,
  campaign,
  loading = false
}: CampaignFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    html_body: '',
    text_body: '',
    scheduled_at: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const isEditing = !!campaign

  // Update form data when campaign prop changes
  useEffect(() => {
    if (campaign) {
      setFormData({
        name: campaign.name,
        subject: campaign.subject,
        html_body: campaign.html_body,
        text_body: campaign.text_body || '',
        scheduled_at: campaign.scheduled_at ? 
          new Date(campaign.scheduled_at).toISOString().slice(0, 16) : ''
      })
    } else {
      setFormData({
        name: '',
        subject: '',
        html_body: '',
        text_body: '',
        scheduled_at: ''
      })
    }
  }, [campaign])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form data
    const validation = campaignValidation.validateCampaignData({
      user_id: 'temp', // Will be set by the API
      name: formData.name,
      subject: formData.subject,
      html_body: formData.html_body,
      text_body: formData.text_body || undefined,
      scheduled_at: formData.scheduled_at || undefined
    })

    if (!validation.isValid) {
      const newErrors: Record<string, string> = {}
      validation.errors.forEach(error => {
        if (error.includes('name')) {
          newErrors.name = error
        } else if (error.includes('Subject')) {
          newErrors.subject = error
        } else if (error.includes('HTML body')) {
          newErrors.html_body = error
        } else if (error.includes('Text body')) {
          newErrors.text_body = error
        } else if (error.includes('Scheduled')) {
          newErrors.scheduled_at = error
        }
      })
      setErrors(newErrors)
      return
    }

    setErrors({})

    try {
      if (isEditing) {
        await onSubmit({
          id: campaign.id,
          name: formData.name,
          subject: formData.subject,
          html_body: formData.html_body,
          text_body: formData.text_body || undefined,
          scheduled_at: formData.scheduled_at || undefined
        })
      } else {
        await onSubmit({
          user_id: 'temp', // Will be set by the API
          name: formData.name,
          subject: formData.subject,
          html_body: formData.html_body,
          text_body: formData.text_body || undefined,
          scheduled_at: formData.scheduled_at || undefined
        })
      }
      
      handleClose()
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      subject: '',
      html_body: '',
      text_body: '',
      scheduled_at: ''
    })
    setErrors({})
    onClose()
  }

  const generateTextFromHtml = () => {
    // Simple HTML to text conversion
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = formData.html_body
    const textContent = tempDiv.textContent || tempDiv.innerText || ''
    setFormData({ ...formData, text_body: textContent })
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Campaign' : 'Create New Campaign'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update your campaign details below.'
              : 'Create a new email campaign to send to your audience.'
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Campaign Name *</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Monthly Newsletter"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="scheduled_at">Schedule (Optional)</Label>
              <Input
                id="scheduled_at"
                type="datetime-local"
                value={formData.scheduled_at}
                onChange={(e) => setFormData({ ...formData, scheduled_at: e.target.value })}
                min={new Date().toISOString().slice(0, 16)}
                className={errors.scheduled_at ? 'border-red-500' : ''}
              />
              {errors.scheduled_at && (
                <p className="text-sm text-red-600">{errors.scheduled_at}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subject">Subject Line *</Label>
            <Input
              id="subject"
              type="text"
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              placeholder="Your amazing subject line"
              className={errors.subject ? 'border-red-500' : ''}
            />
            {errors.subject && (
              <p className="text-sm text-red-600">{errors.subject}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="html_body">HTML Content *</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  // TODO: Integrate with AI generation
                  console.log('AI generation will be implemented in task 7')
                }}
                disabled
              >
                Generate with AI
              </Button>
            </div>
            <Textarea
              id="html_body"
              value={formData.html_body}
              onChange={(e) => setFormData({ ...formData, html_body: e.target.value })}
              placeholder="<h1>Hello {{name}}!</h1><p>Your email content here...</p>"
              className={`min-h-[200px] font-mono text-sm ${errors.html_body ? 'border-red-500' : ''}`}
            />
            {errors.html_body && (
              <p className="text-sm text-red-600">{errors.html_body}</p>
            )}
            <p className="text-xs text-gray-500">
              Use HTML tags for formatting. Variables like {'{'}{'{'} name {'}'}{'}'}  will be replaced with contact data.
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="text_body">Plain Text Version (Optional)</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={generateTextFromHtml}
                disabled={!formData.html_body}
              >
                Generate from HTML
              </Button>
            </div>
            <Textarea
              id="text_body"
              value={formData.text_body}
              onChange={(e) => setFormData({ ...formData, text_body: e.target.value })}
              placeholder="Plain text version of your email..."
              className={`min-h-[120px] ${errors.text_body ? 'border-red-500' : ''}`}
            />
            {errors.text_body && (
              <p className="text-sm text-red-600">{errors.text_body}</p>
            )}
            <p className="text-xs text-gray-500">
              A plain text version is recommended for better deliverability.
            </p>
          </div>

          <DialogFooter className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                // TODO: Implement preview functionality
                console.log('Preview will be implemented')
              }}
              disabled={loading || !formData.html_body}
            >
              Preview
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {isEditing ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEditing ? 'Update Campaign' : 'Create Campaign'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}