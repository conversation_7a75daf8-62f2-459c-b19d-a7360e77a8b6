'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, <PERSON>rk<PERSON>, Copy, Check } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface AIGeneratedContent {
  subjects: string[]
  bodies: string[]
}

interface AIGeneratorProps {
  onContentSelect: (subject: string, body: string) => void
  isOpen: boolean
  onClose: () => void
}

export function AIGenerator({ onContentSelect, isOpen, onClose }: AIGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<AIGeneratedContent | null>(null)
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const [selectedBody, setSelectedBody] = useState<string>('')
  const [copiedItems, setCopiedItems] = useState<Set<string>>(new Set())
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    productOrOffer: '',
    goal: '',
    targetAudience: '',
    tone: 'professional' as 'professional' | 'casual' | 'friendly' | 'urgent'
  })

  const handleGenerate = async () => {
    if (!formData.productOrOffer.trim() || !formData.goal.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please provide both product/offer and goal information.',
        variant: 'destructive'
      })
      return
    }

    setIsGenerating(true)
    setGeneratedContent(null)

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to generate content')
      }

      setGeneratedContent(result.data)
      toast({
        title: 'Content Generated!',
        description: 'AI has generated email content options for you.',
      })
    } catch (error) {
      console.error('AI generation error:', error)
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate content. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCopy = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      const key = `${type}-${text.substring(0, 20)}`
      setCopiedItems(prev => new Set([...prev, key]))
      
      setTimeout(() => {
        setCopiedItems(prev => {
          const newSet = new Set(prev)
          newSet.delete(key)
          return newSet
        })
      }, 2000)

      toast({
        title: 'Copied!',
        description: `${type} copied to clipboard.`,
      })
    } catch (error) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy to clipboard.',
        variant: 'destructive'
      })
    }
  }

  const handleUseContent = () => {
    if (!selectedSubject || !selectedBody) {
      toast({
        title: 'Selection Required',
        description: 'Please select both a subject line and email body.',
        variant: 'destructive'
      })
      return
    }

    onContentSelect(selectedSubject, selectedBody)
    onClose()
    toast({
      title: 'Content Applied',
      description: 'AI-generated content has been added to your email.',
    })
  }

  const handleReset = () => {
    setGeneratedContent(null)
    setSelectedSubject('')
    setSelectedBody('')
    setFormData({
      productOrOffer: '',
      goal: '',
      targetAudience: '',
      tone: 'professional'
    })
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              <CardTitle>AI Email Generator</CardTitle>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
          <CardDescription>
            Generate compelling email content using AI. Provide details about your campaign and get multiple options to choose from.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Input Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="productOrOffer">Product/Offer *</Label>
              <Textarea
                id="productOrOffer"
                placeholder="Describe your product, service, or offer..."
                value={formData.productOrOffer}
                onChange={(e) => setFormData(prev => ({ ...prev, productOrOffer: e.target.value }))}
                rows={3}
                maxLength={500}
              />
              <div className="text-xs text-muted-foreground">
                {formData.productOrOffer.length}/500 characters
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="goal">Campaign Goal *</Label>
              <Textarea
                id="goal"
                placeholder="What do you want to achieve with this email?"
                value={formData.goal}
                onChange={(e) => setFormData(prev => ({ ...prev, goal: e.target.value }))}
                rows={3}
                maxLength={500}
              />
              <div className="text-xs text-muted-foreground">
                {formData.goal.length}/500 characters
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAudience">Target Audience</Label>
              <Input
                id="targetAudience"
                placeholder="e.g., Small business owners, Tech professionals..."
                value={formData.targetAudience}
                onChange={(e) => setFormData(prev => ({ ...prev, targetAudience: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tone">Tone</Label>
              <Select value={formData.tone} onValueChange={(value: any) => setFormData(prev => ({ ...prev, tone: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="casual">Casual</SelectItem>
                  <SelectItem value="friendly">Friendly</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Generate Button */}
          <div className="flex gap-2">
            <Button 
              onClick={handleGenerate} 
              disabled={isGenerating || !formData.productOrOffer.trim() || !formData.goal.trim()}
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Content
                </>
              )}
            </Button>
            {generatedContent && (
              <Button variant="outline" onClick={handleReset}>
                Reset
              </Button>
            )}
          </div>

          {/* Generated Content */}
          {generatedContent && (
            <div className="space-y-6 border-t pt-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Subject Lines</h3>
                <div className="space-y-2">
                  {generatedContent.subjects.map((subject, index) => (
                    <div
                      key={index}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedSubject === subject
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedSubject(subject)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="flex-1">{subject}</span>
                        <div className="flex items-center gap-2">
                          {selectedSubject === subject && (
                            <Badge variant="secondary">Selected</Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleCopy(subject, 'Subject')
                            }}
                          >
                            {copiedItems.has(`Subject-${subject.substring(0, 20)}`) ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Email Bodies</h3>
                <div className="space-y-4">
                  {generatedContent.bodies.map((body, index) => (
                    <div
                      key={index}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedBody === body
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedBody(body)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <Badge variant="outline">Version {index + 1}</Badge>
                        <div className="flex items-center gap-2">
                          {selectedBody === body && (
                            <Badge variant="secondary">Selected</Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleCopy(body, 'Email Body')
                            }}
                          >
                            {copiedItems.has(`Email Body-${body.substring(0, 20)}`) ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                      <div 
                        className="prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: body }}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Use Content Button */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleUseContent}
                  disabled={!selectedSubject || !selectedBody}
                >
                  Use Selected Content
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}