'use client'

import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { CampaignTable } from './campaign-table'
import { CampaignForm } from './campaign-form'
import { CampaignEditor } from './campaign-editor'
import type { Campaign, CampaignFilters, CampaignStats, CreateCampaignData } from '../../types/database'

interface CampaignManagerProps {
  onCreateCampaign: (data: CreateCampaignData) => Promise<Campaign>
  onUpdateCampaign: (data: { id: string } & Partial<CreateCampaignData>) => Promise<Campaign>
  onDeleteCampaign: (campaignId: string) => Promise<void>
  onDuplicateCampaign: (campaignId: string, newName?: string) => Promise<Campaign>
  onSendTestEmail?: (campaign: Campaign, email: string) => Promise<void>
  onLoadCampaigns: (filters?: CampaignFilters) => Promise<{ campaigns: Campaign[]; count: number }>
  onLoadCampaignStats?: (campaignIds: string[]) => Promise<{ [campaignId: string]: CampaignStats }>
}

export function CampaignManager({
  onCreateCampaign,
  onUpdateCampaign,
  onDeleteCampaign,
  onDuplicateCampaign,
  onSendTestEmail,
  onLoadCampaigns,
  onLoadCampaignStats
}: CampaignManagerProps) {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [campaignStats, setCampaignStats] = useState<{ [campaignId: string]: CampaignStats }>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Filters
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<Campaign['status'] | 'all'>('all')
  
  // UI State
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingCampaign, setEditingCampaign] = useState<Campaign | null>(null)
  const [showEditor, setShowEditor] = useState(false)

  // Load campaigns on mount and when filters change
  useEffect(() => {
    loadCampaigns()
  }, [searchQuery, statusFilter])

  // Load campaign stats when campaigns change
  useEffect(() => {
    if (campaigns.length > 0 && onLoadCampaignStats) {
      loadCampaignStats()
    }
  }, [campaigns])

  const loadCampaigns = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const filters: CampaignFilters = {}
      
      if (searchQuery) {
        filters.search = searchQuery
      }
      
      if (statusFilter !== 'all') {
        filters.status = statusFilter
      }
      
      const result = await onLoadCampaigns(filters)
      setCampaigns(result.campaigns)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load campaigns')
    } finally {
      setLoading(false)
    }
  }

  const loadCampaignStats = async () => {
    if (!onLoadCampaignStats) return
    
    try {
      const campaignIds = campaigns.map(c => c.id)
      const stats = await onLoadCampaignStats(campaignIds)
      setCampaignStats(stats)
    } catch (err) {
      console.error('Failed to load campaign stats:', err)
    }
  }

  const handleCreateCampaign = async (data: CreateCampaignData) => {
    try {
      const newCampaign = await onCreateCampaign(data)
      setCampaigns(prev => [newCampaign, ...prev])
      setShowCreateForm(false)
    } catch (err) {
      throw err // Let the form handle the error
    }
  }

  const handleUpdateCampaign = async (data: { id: string } & Partial<CreateCampaignData>) => {
    try {
      const updatedCampaign = await onUpdateCampaign(data)
      setCampaigns(prev => prev.map(c => c.id === data.id ? updatedCampaign : c))
      setEditingCampaign(null)
      setShowCreateForm(false)
      setShowEditor(false)
    } catch (err) {
      throw err // Let the form handle the error
    }
  }

  const handleDeleteCampaign = async (campaignId: string) => {
    try {
      await onDeleteCampaign(campaignId)
      setCampaigns(prev => prev.filter(c => c.id !== campaignId))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete campaign')
    }
  }

  const handleDuplicateCampaign = async (campaign: Campaign) => {
    try {
      const duplicatedCampaign = await onDuplicateCampaign(campaign.id, `${campaign.name} (Copy)`)
      setCampaigns(prev => [duplicatedCampaign, ...prev])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to duplicate campaign')
    }
  }

  const handleEditCampaign = (campaign: Campaign) => {
    setEditingCampaign(campaign)
    setShowEditor(true)
  }

  const handleCancelEdit = () => {
    setEditingCampaign(null)
    setShowCreateForm(false)
    setShowEditor(false)
  }

  if (showEditor) {
    return (
      <CampaignEditor
        campaign={editingCampaign}
        onSave={editingCampaign ? handleUpdateCampaign : handleCreateCampaign}
        onCancel={handleCancelEdit}
        loading={loading}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Campaigns</h1>
          <p className="text-gray-600">Create and manage your email campaigns</p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          Create Campaign
        </Button>
      </div>

      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 max-w-sm">
          <Input
            placeholder="Search campaigns..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as Campaign['status'] | 'all')}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="sending">Sending</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
          </SelectContent>
        </Select>
        
        <Button variant="outline" onClick={loadCampaigns} disabled={loading}>
          {loading ? 'Loading...' : 'Refresh'}
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Campaign Table */}
      <CampaignTable
        campaigns={campaigns}
        campaignStats={campaignStats}
        onEdit={handleEditCampaign}
        onDuplicate={handleDuplicateCampaign}
        onDelete={handleDeleteCampaign}
        onSendTest={onSendTestEmail}
        loading={loading}
      />

      {/* Create/Edit Form */}
      <CampaignForm
        open={showCreateForm}
        onClose={() => setShowCreateForm(false)}
        onSubmit={handleCreateCampaign}
        loading={loading}
      />
    </div>
  )
}