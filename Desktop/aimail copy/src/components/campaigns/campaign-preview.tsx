'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import type { Campaign } from '../../types/database'

interface CampaignPreviewProps {
  open: boolean
  onClose: () => void
  campaign: Campaign
  onSendTest?: (email: string) => Promise<void>
  loading?: boolean
}

export function CampaignPreview({
  open,
  onClose,
  campaign,
  onSendTest,
  loading = false
}: CampaignPreviewProps) {
  const [testEmail, setTestEmail] = useState('')
  const [activeTab, setActiveTab] = useState<'html' | 'text'>('html')

  const handleSendTest = async () => {
    if (!testEmail || !onSendTest) return
    
    try {
      await onSendTest(testEmail)
      setTestEmail('')
    } catch (error) {
      console.error('Error sending test email:', error)
    }
  }

  // Simple template variable replacement for preview
  const replaceTemplateVars = (content: string) => {
    return content
      .replace(/\{\{\s*name\s*\}\}/g, 'John Doe')
      .replace(/\{\{\s*email\s*\}\}/g, '<EMAIL>')
      .replace(/\{\{\s*company\s*\}\}/g, 'Example Company')
  }

  const previewHtml = replaceTemplateVars(campaign.html_body)
  const previewText = campaign.text_body ? replaceTemplateVars(campaign.text_body) : ''

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Campaign Preview</DialogTitle>
          <DialogDescription>
            Preview how your campaign will look to recipients
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Campaign Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Campaign Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <Label className="text-sm font-medium">Name:</Label>
                <p className="text-sm">{campaign.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Subject:</Label>
                <p className="text-lg font-semibold">{campaign.subject}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Status:</Label>
                <p className="text-sm capitalize">{campaign.status}</p>
              </div>
              {campaign.scheduled_at && (
                <div>
                  <Label className="text-sm font-medium">Scheduled:</Label>
                  <p className="text-sm">
                    {new Date(campaign.scheduled_at).toLocaleString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Content Tabs */}
          <div className="space-y-4">
            <div className="flex border-b">
              <button
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'html'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('html')}
              >
                HTML Preview
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'text'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab('text')}
              >
                Plain Text
              </button>
            </div>

            {activeTab === 'html' ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">HTML Email Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-6 bg-white min-h-[400px]">
                    <div 
                      dangerouslySetInnerHTML={{ __html: previewHtml }}
                      className="prose max-w-none"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Template variables like {'{'}{'{'} name {'}'}{'}'}  have been replaced with sample data for preview.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Plain Text Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-6 bg-gray-50 min-h-[400px]">
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {previewText || 'No plain text version available'}
                    </pre>
                  </div>
                  {previewText && (
                    <p className="text-xs text-gray-500 mt-2">
                      Template variables like {'{'}{'{'} name {'}'}{'}'}  have been replaced with sample data for preview.
                    </p>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Test Email Section */}
          {onSendTest && campaign.status === 'draft' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Send Test Email</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Label htmlFor="test-email">Test Email Address</Label>
                    <Input
                      id="test-email"
                      type="email"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button
                      onClick={handleSendTest}
                      disabled={!testEmail || loading}
                    >
                      {loading ? 'Sending...' : 'Send Test'}
                    </Button>
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  Send a test email to verify how your campaign looks in an actual email client.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}