import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { CSVImport } from '../csv-import'

// Mock the toast hook
jest.mock('../../ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('CSVImport', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    onImportComplete: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('File Selection', () => {
    it('should accept valid CSV files', () => {
      render(<CSVImport {...mockProps} />)

      const fileInput = screen.getByRole('button', { name: /browse/i })
      expect(fileInput).toBeInTheDocument()
    })

    it('should show file information when file is selected', async () => {
      render(<CSVImport {...mockProps} />)

      // Create a mock CSV file
      const csvFile = new File(['email,name\<EMAIL>,Test User'], 'contacts.csv', {
        type: 'text/csv'
      })

      // Find the hidden file input
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      
      // Simulate file selection
      Object.defineProperty(fileInput, 'files', {
        value: [csvFile],
        writable: false
      })

      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(screen.getByText('contacts.csv')).toBeInTheDocument()
      })
    })

    it('should reject non-CSV files', async () => {
      const mockToast = jest.fn()
      jest.mocked(require('../../ui/use-toast').useToast).mockReturnValue({
        toast: mockToast
      })

      render(<CSVImport {...mockProps} />)

      // Create a mock non-CSV file
      const txtFile = new File(['some text'], 'test.txt', {
        type: 'text/plain'
      })

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      
      Object.defineProperty(fileInput, 'files', {
        value: [txtFile],
        writable: false
      })

      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Invalid File Type',
          description: 'Please select a CSV file.',
          variant: 'destructive'
        })
      })
    })

    it('should reject files that are too large', async () => {
      const mockToast = jest.fn()
      jest.mocked(require('../../ui/use-toast').useToast).mockReturnValue({
        toast: mockToast
      })

      render(<CSVImport {...mockProps} />)

      // Create a mock large file (6MB)
      const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.csv', {
        type: 'text/csv'
      })

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      
      Object.defineProperty(fileInput, 'files', {
        value: [largeFile],
        writable: false
      })

      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'File Too Large',
          description: 'File size must be less than 5MB.',
          variant: 'destructive'
        })
      })
    })
  })

  describe('File Upload', () => {
    it('should upload file successfully', async () => {
      const mockResponse = {
        success: true,
        summary: {
          total_processed: 2,
          successful: 2,
          failed: 0,
          duplicates: 0
        },
        data: {
          successful: [],
          failed: [],
          duplicates: []
        },
        parsing_errors: []
      }

      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockResponse
      })

      render(<CSVImport {...mockProps} />)

      // Select a file
      const csvFile = new File(['email,name\<EMAIL>,Test User'], 'contacts.csv', {
        type: 'text/csv'
      })

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [csvFile],
        writable: false
      })
      fireEvent.change(fileInput)

      // Wait for file to be selected
      await waitFor(() => {
        expect(screen.getByText('contacts.csv')).toBeInTheDocument()
      })

      // Click import button
      const importButton = screen.getByText('Import Contacts')
      fireEvent.click(importButton)

      // Should show uploading state
      await waitFor(() => {
        expect(screen.getByText('Importing...')).toBeInTheDocument()
      })

      // Wait for completion
      await waitFor(() => {
        expect(screen.getByText('Import Complete')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Should show results
      expect(screen.getByText('2')).toBeInTheDocument() // Successful count
      expect(mockProps.onImportComplete).toHaveBeenCalled()
    })

    it('should handle upload errors', async () => {
      const mockToast = jest.fn()
      jest.mocked(require('../../ui/use-toast').useToast).mockReturnValue({
        toast: mockToast
      })

      mockFetch.mockResolvedValue({
        ok: false,
        json: async () => ({
          error: 'Upload Error',
          message: 'Failed to process file'
        })
      })

      render(<CSVImport {...mockProps} />)

      // Select a file
      const csvFile = new File(['email,name\<EMAIL>,Test User'], 'contacts.csv', {
        type: 'text/csv'
      })

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [csvFile],
        writable: false
      })
      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(screen.getByText('contacts.csv')).toBeInTheDocument()
      })

      // Click import button
      const importButton = screen.getByText('Import Contacts')
      fireEvent.click(importButton)

      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Import Failed',
          description: 'Failed to process file',
          variant: 'destructive'
        })
      })
    })
  })

  describe('Import Results', () => {
    it('should display import results with errors', async () => {
      const mockResponse = {
        success: true,
        summary: {
          total_processed: 3,
          successful: 1,
          failed: 1,
          duplicates: 1
        },
        data: {
          successful: [],
          failed: [
            { data: { email: 'invalid-email' }, error: 'Invalid email format' }
          ],
          duplicates: []
        },
        parsing_errors: ['Row 2: Missing email column']
      }

      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockResponse
      })

      render(<CSVImport {...mockProps} />)

      // Select and upload file
      const csvFile = new File(['email,name\<EMAIL>,Test User'], 'contacts.csv', {
        type: 'text/csv'
      })

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      Object.defineProperty(fileInput, 'files', {
        value: [csvFile],
        writable: false
      })
      fireEvent.change(fileInput)

      await waitFor(() => {
        expect(screen.getByText('contacts.csv')).toBeInTheDocument()
      })

      const importButton = screen.getByText('Import Contacts')
      fireEvent.click(importButton)

      // Wait for results
      await waitFor(() => {
        expect(screen.getByText('Import Complete')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Check result statistics
      expect(screen.getByText('1')).toBeInTheDocument() // Successful
      expect(screen.getByText('1')).toBeInTheDocument() // Failed
      expect(screen.getByText('1')).toBeInTheDocument() // Duplicates
      expect(screen.getByText('3')).toBeInTheDocument() // Total

      // Check error details
      expect(screen.getByText('Failed Imports (1)')).toBeInTheDocument()
      expect(screen.getByText('invalid-email')).toBeInTheDocument()
      expect(screen.getByText('Invalid email format')).toBeInTheDocument()

      // Check parsing errors
      expect(screen.getByText('Parsing Errors:')).toBeInTheDocument()
      expect(screen.getByText('Row 2: Missing email column')).toBeInTheDocument()
    })
  })

  describe('Drag and Drop', () => {
    it('should handle drag and drop', () => {
      render(<CSVImport {...mockProps} />)

      const dropZone = screen.getByText(/Drop your CSV file here/i).closest('div')
      
      // Simulate drag over
      fireEvent.dragOver(dropZone!, {
        dataTransfer: {
          files: []
        }
      })

      // Should show drag over state
      expect(dropZone).toHaveClass('border-blue-500')

      // Simulate drag leave
      fireEvent.dragLeave(dropZone!)

      // Should remove drag over state
      expect(dropZone).not.toHaveClass('border-blue-500')
    })

    it('should handle file drop', async () => {
      render(<CSVImport {...mockProps} />)

      const csvFile = new File(['email,name\<EMAIL>,Test User'], 'contacts.csv', {
        type: 'text/csv'
      })

      const dropZone = screen.getByText(/Drop your CSV file here/i).closest('div')
      
      // Simulate file drop
      fireEvent.drop(dropZone!, {
        dataTransfer: {
          files: [csvFile]
        }
      })

      await waitFor(() => {
        expect(screen.getByText('contacts.csv')).toBeInTheDocument()
      })
    })
  })

  describe('Dialog Controls', () => {
    it('should close dialog and reset state', () => {
      render(<CSVImport {...mockProps} />)

      const cancelButton = screen.getByText('Cancel')
      fireEvent.click(cancelButton)

      expect(mockProps.onClose).toHaveBeenCalled()
    })

    it('should disable upload button when no file selected', () => {
      render(<CSVImport {...mockProps} />)

      const importButton = screen.getByText('Import Contacts')
      expect(importButton).toBeDisabled()
    })
  })
})