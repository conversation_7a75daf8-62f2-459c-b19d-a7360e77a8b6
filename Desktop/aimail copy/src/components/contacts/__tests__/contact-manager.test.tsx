import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ContactManager } from '../contact-manager'
import type { Contact } from '../../../types/database'

// Mock the toast hook
jest.mock('../../ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

const mockContacts: Contact[] = [
  {
    id: 'contact-1',
    user_id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    status: 'active',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 'contact-2',
    user_id: 'user-123',
    email: '<EMAIL>',
    name: '<PERSON>',
    status: 'unsubscribed',
    created_at: '2023-01-02T00:00:00Z',
    updated_at: '2023-01-02T00:00:00Z'
  }
]

describe('ContactManager Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Default mock for fetching contacts
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        data: mockContacts,
        count: mockContacts.length,
        success: true
      })
    })
  })

  describe('Contact List Display', () => {
    it('should load and display contacts on mount', async () => {
      render(<ContactManager />)

      // Should show loading initially
      expect(screen.getByRole('generic')).toBeInTheDocument()

      // Wait for contacts to load
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Verify API was called correctly
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/contacts?limit=10&offset=0')
      )
    })

    it('should display contact information correctly', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Check contact details
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('Unsubscribed')).toBeInTheDocument()
    })

    it('should handle empty contact list', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          data: [],
          count: 0,
          success: true
        })
      })

      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('No contacts found')).toBeInTheDocument()
      })
    })

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        json: async () => ({
          error: 'Server Error',
          message: 'Failed to fetch contacts'
        })
      })

      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('No contacts found')).toBeInTheDocument()
      })
    })
  })

  describe('Search and Filter Functionality', () => {
    it('should filter contacts by search term', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Find and use search input
      const searchInput = screen.getByPlaceholderText('Search contacts...')
      fireEvent.change(searchInput, { target: { value: 'john' } })

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('search=john')
        )
      })
    })

    it('should filter contacts by status', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Find and click status filter
      const statusButton = screen.getByText('Status: All')
      fireEvent.click(statusButton)

      // Click on Active filter
      const activeFilter = screen.getByText('Active')
      fireEvent.click(activeFilter)

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('status=active')
        )
      })
    })
  })

  describe('Contact Creation', () => {
    it('should open create contact form', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('Add Contact')).toBeInTheDocument()
      })

      // Click add contact button
      const addButton = screen.getByText('Add Contact')
      fireEvent.click(addButton)

      // Should open form dialog
      expect(screen.getByText('Add New Contact')).toBeInTheDocument()
      expect(screen.getByLabelText('Email Address *')).toBeInTheDocument()
    })

    it('should create new contact successfully', async () => {
      // Mock successful creation
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: mockContacts, count: 2, success: true })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            data: {
              id: 'contact-3',
              user_id: 'user-123',
              email: '<EMAIL>',
              name: 'New Contact',
              status: 'active',
              created_at: '2023-01-03T00:00:00Z',
              updated_at: '2023-01-03T00:00:00Z'
            },
            success: true
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: [...mockContacts], count: 3, success: true })
        })

      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('Add Contact')).toBeInTheDocument()
      })

      // Open form
      fireEvent.click(screen.getByText('Add Contact'))

      // Fill form
      fireEvent.change(screen.getByLabelText('Email Address *'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Name'), {
        target: { value: 'New Contact' }
      })

      // Submit form
      fireEvent.click(screen.getByText('Add Contact'))

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/contacts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            user_id: 'temp',
            email: '<EMAIL>',
            name: 'New Contact',
            status: 'active'
          })
        })
      })
    })
  })

  describe('Contact Editing', () => {
    it('should open edit contact form', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Find and click edit button (first dropdown menu)
      const dropdownButtons = screen.getAllByRole('button')
      const editDropdown = dropdownButtons.find(button => 
        button.querySelector('svg') // Looking for the MoreHorizontal icon
      )
      
      if (editDropdown) {
        fireEvent.click(editDropdown)
        
        // Click edit option
        const editButton = screen.getByText('Edit')
        fireEvent.click(editButton)

        // Should open form dialog with existing data
        expect(screen.getByText('Edit Contact')).toBeInTheDocument()
        expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
        expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
      }
    })
  })

  describe('Contact Deletion', () => {
    it('should open delete confirmation dialog', async () => {
      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Find and click dropdown menu
      const dropdownButtons = screen.getAllByRole('button')
      const editDropdown = dropdownButtons.find(button => 
        button.querySelector('svg') // Looking for the MoreHorizontal icon
      )
      
      if (editDropdown) {
        fireEvent.click(editDropdown)
        
        // Click delete option
        const deleteButton = screen.getByText('Delete')
        fireEvent.click(deleteButton)

        // Should open confirmation dialog
        expect(screen.getByText('Delete Contact')).toBeInTheDocument()
        expect(screen.getByText('Are you sure you want to delete this contact?')).toBeInTheDocument()
      }
    })

    it('should delete contact successfully', async () => {
      // Mock successful deletion
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: mockContacts, count: 2, success: true })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, message: 'Contact deleted successfully' })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ data: [mockContacts[1]], count: 1, success: true })
        })

      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
      })

      // Open dropdown and click delete
      const dropdownButtons = screen.getAllByRole('button')
      const editDropdown = dropdownButtons.find(button => 
        button.querySelector('svg')
      )
      
      if (editDropdown) {
        fireEvent.click(editDropdown)
        fireEvent.click(screen.getByText('Delete'))

        // Confirm deletion
        const confirmButton = screen.getByText('Delete Contact')
        fireEvent.click(confirmButton)

        await waitFor(() => {
          expect(mockFetch).toHaveBeenCalledWith('/api/contacts/contact-1', {
            method: 'DELETE'
          })
        })
      }
    })
  })

  describe('Pagination', () => {
    it('should handle pagination correctly', async () => {
      // Mock response with pagination
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          data: mockContacts,
          count: 25, // More than one page
          success: true
        })
      })

      render(<ContactManager />)

      await waitFor(() => {
        expect(screen.getByText('Page 1 of 3')).toBeInTheDocument()
      })

      // Click next page
      const nextButton = screen.getByText('Next')
      fireEvent.click(nextButton)

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('offset=10')
        )
      })
    })
  })
})