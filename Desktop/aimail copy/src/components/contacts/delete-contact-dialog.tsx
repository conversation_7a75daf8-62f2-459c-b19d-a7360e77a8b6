'use client'

import { But<PERSON> } from '../ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import type { Contact } from '../../types/database'

interface DeleteContactDialogProps {
  open: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
  contact: Contact | null
  loading?: boolean
}

export function DeleteContactDialog({
  open,
  onClose,
  onConfirm,
  contact,
  loading = false
}: DeleteContactDialogProps) {
  const handleConfirm = async () => {
    await onConfirm()
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Contact</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this contact? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        
        {contact && (
          <div className="py-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="font-medium">{contact.email}</p>
              {contact.name && (
                <p className="text-sm text-gray-600">{contact.name}</p>
              )}
              <p className="text-sm text-gray-500 capitalize">
                Status: {contact.status}
              </p>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Deleting...
              </div>
            ) : (
              'Delete Contact'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}