'use client'

import { useState, useEffect, useCallback } from 'react'
import { ContactTable } from './contact-table'
import { ContactForm } from './contact-form'
import { DeleteContactDialog } from './delete-contact-dialog'
import { CSVImport } from './csv-import'
import { Button } from '../ui/button'
import { Upload } from 'lucide-react'
import { useToast } from '../ui/use-toast'
import type { Contact, ContactFilters, CreateContactData } from '../../types/database'

const CONTACTS_PER_PAGE = 10

export function ContactManager() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [filters, setFilters] = useState<ContactFilters>({})
  
  // Form states
  const [showForm, setShowForm] = useState(false)
  const [editingContact, setEditingContact] = useState<Contact | null>(null)
  const [formLoading, setFormLoading] = useState(false)
  
  // Delete states
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deletingContact, setDeletingContact] = useState<Contact | null>(null)
  const [deleteLoading, setDeleteLoading] = useState(false)
  
  // CSV Import states
  const [showCSVImport, setShowCSVImport] = useState(false)

  const { toast } = useToast()

  // Fetch contacts
  const fetchContacts = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      
      if (filters.status) {
        params.append('status', filters.status)
      }
      if (filters.search) {
        params.append('search', filters.search)
      }
      
      params.append('limit', CONTACTS_PER_PAGE.toString())
      params.append('offset', ((currentPage - 1) * CONTACTS_PER_PAGE).toString())

      const response = await fetch(`/api/contacts?${params.toString()}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch contacts')
      }

      setContacts(result.data)
      setTotalCount(result.count)
    } catch (error) {
      console.error('Error fetching contacts:', error)
      toast({
        title: 'Error',
        description: 'Failed to load contacts. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }, [currentPage, filters, toast])

  // Load contacts on mount and when dependencies change
  useEffect(() => {
    fetchContacts()
  }, [fetchContacts])

  // Handle search
  const handleSearch = useCallback((search: string) => {
    setFilters(prev => ({ ...prev, search }))
    setCurrentPage(1) // Reset to first page
  }, [])

  // Handle filter
  const handleFilter = useCallback((newFilters: ContactFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setCurrentPage(1) // Reset to first page
  }, [])

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  // Handle create new contact
  const handleCreateNew = useCallback(() => {
    setEditingContact(null)
    setShowForm(true)
  }, [])

  // Handle edit contact
  const handleEdit = useCallback((contact: Contact) => {
    setEditingContact(contact)
    setShowForm(true)
  }, [])

  // Handle delete contact
  const handleDelete = useCallback((contact: Contact) => {
    setDeletingContact(contact)
    setShowDeleteDialog(true)
  }, [])

  // Handle form submission
  const handleFormSubmit = async (data: CreateContactData | { id: string } & Partial<CreateContactData>) => {
    setFormLoading(true)
    try {
      const isEditing = 'id' in data
      const url = isEditing ? `/api/contacts/${data.id}` : '/api/contacts'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || `Failed to ${isEditing ? 'update' : 'create'} contact`)
      }

      toast({
        title: 'Success',
        description: `Contact ${isEditing ? 'updated' : 'created'} successfully`
      })

      // Refresh contacts list
      await fetchContacts()
    } catch (error) {
      console.error('Error submitting form:', error)
      toast({
        title: 'Error',
        description: (error as Error).message,
        variant: 'destructive'
      })
      throw error // Re-throw to prevent form from closing
    } finally {
      setFormLoading(false)
    }
  }

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!deletingContact) return

    setDeleteLoading(true)
    try {
      const response = await fetch(`/api/contacts/${deletingContact.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete contact')
      }

      toast({
        title: 'Success',
        description: 'Contact deleted successfully'
      })

      // Refresh contacts list
      await fetchContacts()
    } catch (error) {
      console.error('Error deleting contact:', error)
      toast({
        title: 'Error',
        description: (error as Error).message,
        variant: 'destructive'
      })
    } finally {
      setDeleteLoading(false)
    }
  }

  const totalPages = Math.ceil(totalCount / CONTACTS_PER_PAGE)

  return (
    <>
      {/* Import Button */}
      <div className="mb-4">
        <Button
          onClick={() => setShowCSVImport(true)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Upload className="h-4 w-4" />
          Import CSV
        </Button>
      </div>

      <ContactTable
        contacts={contacts}
        totalCount={totalCount}
        loading={loading}
        onSearch={handleSearch}
        onFilter={handleFilter}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onCreateNew={handleCreateNew}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />

      <ContactForm
        open={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        contact={editingContact}
        loading={formLoading}
      />

      <DeleteContactDialog
        open={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDeleteConfirm}
        contact={deletingContact}
        loading={deleteLoading}
      />

      <CSVImport
        open={showCSVImport}
        onClose={() => setShowCSVImport(false)}
        onImportComplete={fetchContacts}
      />
    </>
  )
}