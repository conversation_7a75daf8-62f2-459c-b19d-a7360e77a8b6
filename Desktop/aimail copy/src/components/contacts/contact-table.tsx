'use client'

import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { Search, MoreHorizontal, Edit, Trash2, UserPlus } from 'lucide-react'
import type { Contact, ContactFilters } from '../../types/database'

interface ContactTableProps {
  contacts: Contact[]
  totalCount: number
  loading?: boolean
  onSearch: (search: string) => void
  onFilter: (filters: ContactFilters) => void
  onEdit: (contact: Contact) => void
  onDelete: (contact: Contact) => void
  onCreateNew: () => void
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}

const statusColors = {
  active: 'bg-green-100 text-green-800',
  unsubscribed: 'bg-yellow-100 text-yellow-800',
  bounced: 'bg-red-100 text-red-800'
}

const statusLabels = {
  active: 'Active',
  unsubscribed: 'Unsubscribed',
  bounced: 'Bounced'
}

export function ContactTable({
  contacts,
  totalCount,
  loading = false,
  onSearch,
  onFilter,
  onEdit,
  onDelete,
  onCreateNew,
  currentPage,
  totalPages,
  onPageChange
}: ContactTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    onSearch(value)
  }

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status)
    const filters: ContactFilters = {}
    if (status !== 'all') {
      filters.status = status as 'active' | 'unsubscribed' | 'bounced'
    }
    onFilter(filters)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Contacts</CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              {totalCount} total contacts
            </p>
          </div>
          <Button onClick={onCreateNew} className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Add Contact
          </Button>
        </div>
        
        {/* Search and Filter Controls */}
        <div className="flex items-center gap-4 mt-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Status: {statusFilter === 'all' ? 'All' : statusLabels[statusFilter as keyof typeof statusLabels]}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleStatusFilter('all')}>
                All Statuses
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusFilter('active')}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusFilter('unsubscribed')}>
                Unsubscribed
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleStatusFilter('bounced')}>
                Bounced
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : !contacts || contacts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No contacts found</p>
            {searchTerm && (
              <p className="text-sm mt-2">
                Try adjusting your search or filters
              </p>
            )}
          </div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {contacts.map((contact) => (
                  <TableRow key={contact.id}>
                    <TableCell className="font-medium">
                      {contact.email}
                    </TableCell>
                    <TableCell>
                      {contact.name || (
                        <span className="text-gray-400 italic">No name</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="secondary" 
                        className={statusColors[contact.status]}
                      >
                        {statusLabels[contact.status]}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-gray-600">
                      {formatDate(contact.created_at)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem 
                            onClick={() => onEdit(contact)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => onDelete(contact)}
                            className="flex items-center gap-2 text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}