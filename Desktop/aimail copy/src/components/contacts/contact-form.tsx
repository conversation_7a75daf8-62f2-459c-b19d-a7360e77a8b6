'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select'
import { contactValidation } from '../../lib/contacts'
import type { Contact, CreateContactData } from '../../types/database'

interface ContactFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: CreateContactData | { id: string } & Partial<CreateContactData>) => Promise<void>
  contact?: Contact | null
  loading?: boolean
}

export function ContactForm({
  open,
  onClose,
  onSubmit,
  contact,
  loading = false
}: ContactFormProps) {
  const [formData, setFormData] = useState({
    email: contact?.email || '',
    name: contact?.name || '',
    status: contact?.status || 'active'
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const isEditing = !!contact

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form data
    const validation = contactValidation.validateContactData({
      user_id: 'temp', // Will be set by the API
      email: formData.email,
      name: formData.name || undefined,
      status: formData.status as 'active' | 'unsubscribed' | 'bounced'
    })

    if (!validation.isValid) {
      const newErrors: Record<string, string> = {}
      validation.errors.forEach(error => {
        if (error.includes('Email')) {
          newErrors.email = error
        } else if (error.includes('Name')) {
          newErrors.name = error
        } else if (error.includes('Status')) {
          newErrors.status = error
        }
      })
      setErrors(newErrors)
      return
    }

    setErrors({})

    try {
      if (isEditing) {
        await onSubmit({
          id: contact.id,
          email: formData.email,
          name: formData.name || undefined,
          status: formData.status as 'active' | 'unsubscribed' | 'bounced'
        })
      } else {
        await onSubmit({
          user_id: 'temp', // Will be set by the API
          email: formData.email,
          name: formData.name || undefined,
          status: formData.status as 'active' | 'unsubscribed' | 'bounced'
        })
      }
      
      // Reset form and close
      setFormData({ email: '', name: '', status: 'active' })
      onClose()
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const handleClose = () => {
    setFormData({ email: '', name: '', status: 'active' })
    setErrors({})
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Contact' : 'Add New Contact'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the contact information below.'
              : 'Enter the contact information below to add them to your audience.'
            }
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="<EMAIL>"
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="John Doe"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => setFormData({ ...formData, status: value })}
            >
              <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                <SelectItem value="bounced">Bounced</SelectItem>
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {isEditing ? 'Updating...' : 'Adding...'}
                </div>
              ) : (
                isEditing ? 'Update Contact' : 'Add Contact'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}