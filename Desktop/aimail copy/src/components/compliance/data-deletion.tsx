'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Trash2, AlertTriangle, Shield } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

export function DataDeletionComponent() {
  const [isOpen, setIsOpen] = useState(false)
  const [confirmation, setConfirmation] = useState('')
  const [reason, setReason] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const { toast } = useToast()

  const handleDelete = async () => {
    if (confirmation !== 'DELETE_MY_DATA') {
      toast({
        title: 'Invalid Confirmation',
        description: 'Please type "DELETE_MY_DATA" exactly to confirm.',
        variant: 'destructive'
      })
      return
    }

    setIsDeleting(true)

    try {
      const response = await fetch('/api/compliance/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
          confirmation,
          reason
        })
      })

      if (!response.ok) {
        throw new Error('Deletion failed')
      }

      toast({
        title: 'Account Deleted',
        description: 'Your account and all associated data have been permanently deleted.'
      })

      // Redirect to login or home page
      window.location.href = '/login'

    } catch (error) {
      console.error('Deletion error:', error)
      toast({
        title: 'Deletion Failed',
        description: 'There was an error deleting your account. Please contact support.',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Card className="border-red-200">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <Trash2 className="h-5 w-5" />
          Delete Account & Data
        </CardTitle>
        <CardDescription>
          Permanently delete your account and all associated data. This action cannot be undone.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Warning:</strong> This will permanently delete all your data including:
            <ul className="mt-2 ml-4 list-disc space-y-1">
              <li>All email campaigns and their analytics</li>
              <li>All contacts and mailing lists</li>
              <li>Account settings and preferences</li>
              <li>Activity logs and audit trails</li>
            </ul>
          </AlertDescription>
        </Alert>

        <Alert className="border-blue-200 bg-blue-50">
          <Shield className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <strong>Before you delete:</strong> Consider exporting your data first if you need 
            it for records or migration to another service.
          </AlertDescription>
        </Alert>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive" className="w-full">
              Delete My Account
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="text-red-600">Confirm Account Deletion</DialogTitle>
              <DialogDescription>
                This action is permanent and cannot be undone. All your data will be 
                immediately and permanently deleted from our servers.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="reason">Reason for deletion (optional)</Label>
                <Textarea
                  id="reason"
                  placeholder="Help us improve by telling us why you're leaving..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="confirmation">
                  Type <code className="bg-gray-100 px-1 rounded">DELETE_MY_DATA</code> to confirm
                </Label>
                <Input
                  id="confirmation"
                  value={confirmation}
                  onChange={(e) => setConfirmation(e.target.value)}
                  placeholder="DELETE_MY_DATA"
                  className="mt-1"
                />
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isDeleting || confirmation !== 'DELETE_MY_DATA'}
              >
                {isDeleting ? 'Deleting...' : 'Delete Forever'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}