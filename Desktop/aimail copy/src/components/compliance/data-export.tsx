'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Download, AlertTriangle, CheckCircle } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

interface ExportOptions {
  includeContacts: boolean
  includeCampaigns: boolean
  includeAnalytics: boolean
  includeAuditLogs: boolean
  format: 'json' | 'csv'
  dateRange?: {
    start: string
    end: string
  }
}

export function DataExportComponent() {
  const [options, setOptions] = useState<ExportOptions>({
    includeContacts: true,
    includeCampaigns: true,
    includeAnalytics: true,
    includeAuditLogs: false,
    format: 'json'
  })
  const [isExporting, setIsExporting] = useState(false)
  const [exportHistory, setExportHistory] = useState<any[]>([])
  const { toast } = useToast()

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      const response = await fetch('/api/compliance/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(options)
      })

      if (!response.ok) {
        throw new Error('Export failed')
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition')
      const filename = contentDisposition?.match(/filename="(.+)"/)?.[1] || 'export.json'

      // Download the file
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: 'Export Complete',
        description: 'Your data has been exported successfully.'
      })

      // Refresh export history
      fetchExportHistory()

    } catch (error) {
      console.error('Export error:', error)
      toast({
        title: 'Export Failed',
        description: 'There was an error exporting your data. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const fetchExportHistory = async () => {
    try {
      const response = await fetch('/api/compliance/export', {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setExportHistory(data.exportHistory)
      }
    } catch (error) {
      console.error('Failed to fetch export history:', error)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Your Data
          </CardTitle>
          <CardDescription>
            Download a copy of your data for backup or compliance purposes. 
            This includes all data associated with your account.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Exported data may contain sensitive information. Please handle it securely 
              and delete it when no longer needed.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Data to Include</Label>
              <div className="mt-2 space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="contacts"
                    checked={options.includeContacts}
                    onCheckedChange={(checked) =>
                      setOptions(prev => ({ ...prev, includeContacts: !!checked }))
                    }
                  />
                  <Label htmlFor="contacts">Contacts and mailing lists</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="campaigns"
                    checked={options.includeCampaigns}
                    onCheckedChange={(checked) =>
                      setOptions(prev => ({ ...prev, includeCampaigns: !!checked }))
                    }
                  />
                  <Label htmlFor="campaigns">Email campaigns and analytics</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="analytics"
                    checked={options.includeAnalytics}
                    onCheckedChange={(checked) =>
                      setOptions(prev => ({ ...prev, includeAnalytics: !!checked }))
                    }
                  />
                  <Label htmlFor="analytics">Performance analytics</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="audit"
                    checked={options.includeAuditLogs}
                    onCheckedChange={(checked) =>
                      setOptions(prev => ({ ...prev, includeAuditLogs: !!checked }))
                    }
                  />
                  <Label htmlFor="audit">Audit logs and activity history</Label>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="format" className="text-base font-medium">Export Format</Label>
              <Select
                value={options.format}
                onValueChange={(value: 'json' | 'csv') =>
                  setOptions(prev => ({ ...prev, format: value }))
                }
              >
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON (recommended)</SelectItem>
                  <SelectItem value="csv">CSV (spreadsheet compatible)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-base font-medium">Date Range (Optional)</Label>
              <div className="mt-2 grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start-date" className="text-sm">Start Date</Label>
                  <Input
                    id="start-date"
                    type="date"
                    onChange={(e) =>
                      setOptions(prev => ({
                        ...prev,
                        dateRange: {
                          start: e.target.value + 'T00:00:00Z',
                          end: prev.dateRange?.end || new Date().toISOString()
                        }
                      }))
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="end-date" className="text-sm">End Date</Label>
                  <Input
                    id="end-date"
                    type="date"
                    onChange={(e) =>
                      setOptions(prev => ({
                        ...prev,
                        dateRange: {
                          start: prev.dateRange?.start || '2020-01-01T00:00:00Z',
                          end: e.target.value + 'T23:59:59Z'
                        }
                      }))
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full"
          >
            {isExporting ? 'Exporting...' : 'Export Data'}
          </Button>
        </CardContent>
      </Card>

      {exportHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Export History</CardTitle>
            <CardDescription>
              Your recent data exports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {exportHistory.map((export_, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">
                      {new Date(export_.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {export_.details?.format?.toUpperCase() || 'JSON'}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}