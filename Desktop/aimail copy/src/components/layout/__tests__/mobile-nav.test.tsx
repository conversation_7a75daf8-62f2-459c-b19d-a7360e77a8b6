import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { MobileNav } from '../mobile-nav'
import { useAuth } from '../../../contexts/auth-context'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

// Mock auth context
jest.mock('../../../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}))

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, onClick, ...props }: any) {
    return (
      <a href={href} onClick={onClick} {...props}>
        {children}
      </a>
    )
  }
})

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('MobileNav', () => {
  const mockSignOut = jest.fn()
  const mockSetSidebarOpen = jest.fn()

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      signOut: mockSignOut,
      loading: false,
    })
    mockUsePathname.mockReturnValue('/dashboard')
    mockSignOut.mockClear()
    mockSetSidebarOpen.mockClear()
  })

  it('renders mobile header with app name', () => {
    render(
      <MobileNav 
        sidebarOpen={false} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    expect(screen.getByText('EmailFlow')).toBeInTheDocument()
  })

  it('renders menu button', () => {
    render(
      <MobileNav 
        sidebarOpen={false} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    const menuButton = screen.getByRole('button', { name: /open sidebar/i })
    expect(menuButton).toBeInTheDocument()
  })

  it('calls signOut when sign out is clicked in mobile menu', async () => {
    render(
      <MobileNav 
        sidebarOpen={true} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    const signOutButton = screen.getByText('Sign out')
    fireEvent.click(signOutButton)

    await waitFor(() => {
      expect(mockSignOut).toHaveBeenCalledTimes(1)
      expect(mockSetSidebarOpen).toHaveBeenCalledWith(false)
    })
  })

  it('closes sidebar when navigation link is clicked', () => {
    render(
      <MobileNav 
        sidebarOpen={true} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    const dashboardLink = screen.getByText('Dashboard')
    fireEvent.click(dashboardLink)

    expect(mockSetSidebarOpen).toHaveBeenCalledWith(false)
  })

  it('highlights active navigation item', () => {
    mockUsePathname.mockReturnValue('/campaigns')
    
    render(
      <MobileNav 
        sidebarOpen={true} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    const campaignsLink = screen.getByText('Campaigns').closest('a')
    expect(campaignsLink).toHaveClass('bg-gray-50', 'text-blue-600')
  })

  it('handles sign out errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    mockSignOut.mockRejectedValue(new Error('Sign out failed'))

    render(
      <MobileNav 
        sidebarOpen={true} 
        setSidebarOpen={mockSetSidebarOpen} 
        appName="EmailFlow" 
      />
    )

    const signOutButton = screen.getByText('Sign out')
    fireEvent.click(signOutButton)

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error signing out:', expect.any(Error))
    })

    consoleSpy.mockRestore()
  })
})