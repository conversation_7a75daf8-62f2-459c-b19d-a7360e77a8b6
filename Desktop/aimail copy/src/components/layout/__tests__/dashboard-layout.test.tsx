import { render, screen } from '@testing-library/react'
import { DashboardLayout } from '../dashboard-layout'
import { useAuth } from '../../../contexts/auth-context'

// Mock auth context
jest.mock('../../../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/dashboard'),
}))

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    )
  }
})

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('DashboardLayout', () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      signOut: jest.fn(),
      loading: false,
    })
  })

  it('renders children content', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    )

    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('renders the sidebar component', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Check if sidebar navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Audience')).toBeInTheDocument()
    expect(screen.getByText('Campaigns')).toBeInTheDocument()
  })

  it('renders mobile navigation', () => {
    render(
      <DashboardLayout>
        <div>Test Content</div>
      </DashboardLayout>
    )

    // Check if mobile menu trigger is present (hidden on desktop)
    const menuButton = screen.getByRole('button', { name: /open sidebar/i })
    expect(menuButton).toBeInTheDocument()
  })

  it('applies correct layout structure', () => {
    const { container } = render(
      <DashboardLayout>
        <div data-testid="content">Test Content</div>
      </DashboardLayout>
    )

    // Check if main content area has correct classes
    const mainContent = screen.getByTestId('content').closest('main')
    expect(mainContent).toHaveClass('py-6')

    // Check if layout has correct structure
    const layoutContainer = container.querySelector('.lg\\:pl-64')
    expect(layoutContainer).toBeInTheDocument()
  })
})