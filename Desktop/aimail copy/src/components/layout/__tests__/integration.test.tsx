import { render, screen } from '@testing-library/react'
import { DashboardLayout } from '../dashboard-layout'
import { useAuth } from '../../../contexts/auth-context'
import { it } from 'zod/locales'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

// Mock auth context
jest.mock('../../../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(() => '/dashboard'),
}))

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    )
  }
})

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('Layout Integration', () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      signOut: jest.fn(),
      loading: false,
    })
  })

  it('renders complete dashboard layout with navigation', () => {
    render(
      <DashboardLayout>
        <div data-testid="dashboard-content">Dashboard Content</div>
      </DashboardLayout>
    )

    // Check if content is rendered
    expect(screen.getByTestId('dashboard-content')).toBeInTheDocument()
    
    // Check if navigation is present (using getAllByText since EmailFlow appears twice)
    expect(screen.getAllByText('EmailFlow')).toHaveLength(2) // Mobile and desktop
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Audience')).toBeInTheDocument()
    expect(screen.getByText('Campaigns')).toBeInTheDocument()
    expect(screen.getByText('Analytics')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
  })
})