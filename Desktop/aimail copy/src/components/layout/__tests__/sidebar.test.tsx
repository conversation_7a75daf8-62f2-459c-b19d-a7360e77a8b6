import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { usePathname } from 'next/navigation'
import { Sidebar } from '../sidebar'
import { useAuth } from '../../../contexts/auth-context'

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}))

// Mock auth context
jest.mock('../../../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}))

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    )
  }
})

const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

describe('Sidebar', () => {
  const mockSignOut = jest.fn()

  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      signOut: mockSignOut,
      loading: false,
    })
    mockUsePathname.mockReturnValue('/dashboard')
    mockSignOut.mockClear()
  })

  it('renders the sidebar with navigation items', () => {
    render(<Sidebar />)

    // Check if app name is displayed
    expect(screen.getByText('EmailFlow')).toBeInTheDocument()

    // Check if navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Audience')).toBeInTheDocument()
    expect(screen.getByText('Campaigns')).toBeInTheDocument()
    expect(screen.getByText('Analytics')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
    expect(screen.getByText('Sign out')).toBeInTheDocument()
  })

  it('highlights the active navigation item', () => {
    mockUsePathname.mockReturnValue('/campaigns')
    render(<Sidebar />)

    const campaignsLink = screen.getByText('Campaigns').closest('a')
    expect(campaignsLink).toHaveClass('bg-gray-50', 'text-blue-600')
  })

  it('calls signOut when sign out button is clicked', async () => {
    render(<Sidebar />)

    const signOutButton = screen.getByText('Sign out')
    fireEvent.click(signOutButton)

    await waitFor(() => {
      expect(mockSignOut).toHaveBeenCalledTimes(1)
    })
  })

  it('handles sign out errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    mockSignOut.mockRejectedValue(new Error('Sign out failed'))

    render(<Sidebar />)

    const signOutButton = screen.getByText('Sign out')
    fireEvent.click(signOutButton)

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error signing out:', expect.any(Error))
    })

    consoleSpy.mockRestore()
  })

  it('renders navigation links with correct hrefs', () => {
    render(<Sidebar />)

    expect(screen.getByText('Dashboard').closest('a')).toHaveAttribute('href', '/dashboard')
    expect(screen.getByText('Audience').closest('a')).toHaveAttribute('href', '/audience')
    expect(screen.getByText('Campaigns').closest('a')).toHaveAttribute('href', '/campaigns')
    expect(screen.getByText('Analytics').closest('a')).toHaveAttribute('href', '/analytics')
    expect(screen.getByText('Settings').closest('a')).toHaveAttribute('href', '/settings')
  })
})