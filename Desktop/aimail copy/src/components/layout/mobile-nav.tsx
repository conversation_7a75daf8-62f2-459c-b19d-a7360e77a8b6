'use client'

import { Fragment } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Menu, X, LogOut } from 'lucide-react'
import { 
  LayoutDashboard, 
  Users, 
  Mail, 
  BarChart3, 
  Settings
} from 'lucide-react'
import { Sheet, SheetContent, SheetTrigger } from '../ui/sheet'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import { brandingConfig } from '../../config/branding'
import { useAuth } from '../../contexts/auth-context'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Audience', href: '/audience', icon: Users },
  { name: 'Campaigns', href: '/campaigns', icon: Mail },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
]

interface MobileNavProps {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  appName: string
}

export function MobileNav({ sidebarOpen, setSidebarOpen, appName }: MobileNavProps) {
  const pathname = usePathname()
  const { signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
      setSidebarOpen(false)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <>
      {/* Mobile header */}
      <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-white px-4 py-4 shadow-sm sm:px-6 lg:hidden">
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm" className="-m-2.5 p-2.5">
              <span className="sr-only">Open sidebar</span>
              <Menu className="h-6 w-6" aria-hidden="true" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
              {/* Logo */}
              <div className="flex h-16 shrink-0 items-center">
                <div className="flex items-center space-x-3">
                  <div 
                    className="h-8 w-8 rounded-lg flex items-center justify-center text-white font-bold text-sm"
                    style={{ backgroundColor: brandingConfig.colors.primary }}
                  >
                    {appName.charAt(0)}
                  </div>
                  <span className="text-xl font-semibold text-gray-900">
                    {appName}
                  </span>
                </div>
              </div>

              {/* Navigation */}
              <nav className="flex flex-1 flex-col">
                <ul role="list" className="flex flex-1 flex-col gap-y-7">
                  <li>
                    <ul role="list" className="-mx-2 space-y-1">
                      {navigation.map((item) => {
                        const isActive = pathname === item.href
                        return (
                          <li key={item.name}>
                            <Link
                              href={item.href}
                              onClick={() => setSidebarOpen(false)}
                              className={cn(
                                isActive
                                  ? 'bg-gray-50 text-blue-600'
                                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50',
                                'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors'
                              )}
                            >
                              <item.icon
                                className={cn(
                                  isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-600',
                                  'h-6 w-6 shrink-0'
                                )}
                                aria-hidden="true"
                              />
                              {item.name}
                            </Link>
                          </li>
                        )
                      })}
                    </ul>
                  </li>

                  {/* Sign out button */}
                  <li className="mt-auto">
                    <button
                      onClick={handleSignOut}
                      className="group -mx-2 flex w-full gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors"
                    >
                      <LogOut
                        className="h-6 w-6 shrink-0 text-gray-400 group-hover:text-red-600"
                        aria-hidden="true"
                      />
                      Sign out
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </SheetContent>
        </Sheet>

        {/* Mobile header title */}
        <div className="flex-1 text-sm font-semibold leading-6 text-gray-900">
          {appName}
        </div>
      </div>
    </>
  )
}