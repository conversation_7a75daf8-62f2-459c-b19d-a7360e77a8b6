'use client'

import { useState } from 'react'
import { Sidebar } from './sidebar'
import { MobileNav } from './mobile-nav'
import { brandingConfig } from '../../config/branding'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile navigation */}
      <MobileNav 
        sidebarOpen={sidebarOpen} 
        setSidebarOpen={setSidebarOpen}
        appName={brandingConfig.appName}
      />

      {/* Desktop sidebar */}
      <Sidebar />

      {/* Main content */}
      <div className="lg:pl-64">
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}