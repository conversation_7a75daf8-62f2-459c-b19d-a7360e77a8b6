'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Palette, Eye } from 'lucide-react'
import { BrandingConfig } from '../../config/branding'

interface BrandingSettingsProps {
  initialConfig?: BrandingConfig
  onConfigChange?: (config: BrandingConfig) => void
}

export function BrandingSettings({ initialConfig, onConfigChange }: BrandingSettingsProps) {
  const [config, setConfig] = useState<BrandingConfig>({
    appName: initialConfig?.appName || 'EmailFlow',
    logo: initialConfig?.logo || '/logo.png',
    colors: {
      primary: initialConfig?.colors.primary || '#3b82f6',
      secondary: initialConfig?.colors.secondary || '#64748b',
      accent: initialConfig?.colors.accent || '#f59e0b',
    },
    theme: {
      borderRadius: initialConfig?.theme.borderRadius || '0.5rem',
      fontFamily: initialConfig?.theme.fontFamily || 'Inter',
    },
  })

  const [previewMode, setPreviewMode] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    const keys = field.split('.')
    const newConfig = { ...config }
    
    if (keys.length === 1) {
      ;(newConfig as any)[keys[0]] = value
    } else if (keys.length === 2) {
      ;(newConfig as any)[keys[0]][keys[1]] = value
    }
    
    setConfig(newConfig)
    onConfigChange?.(newConfig)
  }

  const applyPreview = () => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      
      root.style.setProperty('--brand-primary', config.colors.primary)
      root.style.setProperty('--brand-secondary', config.colors.secondary)
      root.style.setProperty('--brand-accent', config.colors.accent)
      root.style.setProperty('--brand-radius', config.theme.borderRadius)
      root.style.setProperty('--brand-font', config.theme.fontFamily)
      
      document.title = config.appName
    }
  }

  const resetPreview = () => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      
      root.style.removeProperty('--brand-primary')
      root.style.removeProperty('--brand-secondary')
      root.style.removeProperty('--brand-accent')
      root.style.removeProperty('--brand-radius')
      root.style.removeProperty('--brand-font')
      
      document.title = 'EmailFlow'
    }
  }

  useEffect(() => {
    if (previewMode) {
      applyPreview()
    } else {
      resetPreview()
    }

    return () => {
      if (previewMode) {
        resetPreview()
      }
    }
  }, [previewMode, config])

  const fontOptions = [
    'Inter',
    'Roboto',
    'Open Sans',
    'Lato',
    'Montserrat',
    'Poppins',
    'Source Sans Pro',
    'Nunito',
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Branding Configuration
        </CardTitle>
        <CardDescription>
          Customize the appearance and branding of your platform
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="appName">Application Name</Label>
            <Input
              id="appName"
              value={config.appName}
              onChange={(e) => handleInputChange('appName', e.target.value)}
              placeholder="EmailFlow"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="logo">Logo URL</Label>
            <Input
              id="logo"
              value={config.logo}
              onChange={(e) => handleInputChange('logo', e.target.value)}
              placeholder="/logo.png"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-sm font-medium">Colors</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primaryColor">Primary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="primaryColor"
                  type="color"
                  value={config.colors.primary}
                  onChange={(e) => handleInputChange('colors.primary', e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.colors.primary}
                  onChange={(e) => handleInputChange('colors.primary', e.target.value)}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="secondaryColor">Secondary Color</Label>
              <div className="flex gap-2">
                <Input
                  id="secondaryColor"
                  type="color"
                  value={config.colors.secondary}
                  onChange={(e) => handleInputChange('colors.secondary', e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.colors.secondary}
                  onChange={(e) => handleInputChange('colors.secondary', e.target.value)}
                  placeholder="#64748b"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accentColor">Accent Color</Label>
              <div className="flex gap-2">
                <Input
                  id="accentColor"
                  type="color"
                  value={config.colors.accent}
                  onChange={(e) => handleInputChange('colors.accent', e.target.value)}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={config.colors.accent}
                  onChange={(e) => handleInputChange('colors.accent', e.target.value)}
                  placeholder="#f59e0b"
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-sm font-medium">Theme</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="borderRadius">Border Radius</Label>
              <Input
                id="borderRadius"
                value={config.theme.borderRadius}
                onChange={(e) => handleInputChange('theme.borderRadius', e.target.value)}
                placeholder="0.5rem"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fontFamily">Font Family</Label>
              <select
                id="fontFamily"
                value={config.theme.fontFamily}
                onChange={(e) => handleInputChange('theme.fontFamily', e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {fontOptions.map((font) => (
                  <option key={font} value={font}>
                    {font}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="flex gap-2 pt-4 border-t">
          <Button
            onClick={() => setPreviewMode(!previewMode)}
            variant={previewMode ? 'default' : 'outline'}
          >
            <Eye className="mr-2 h-4 w-4" />
            {previewMode ? 'Exit Preview' : 'Preview Changes'}
          </Button>
        </div>

        {previewMode && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              Preview mode is active. The changes are temporarily applied to see how they look.
              Save your settings to make them permanent.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}