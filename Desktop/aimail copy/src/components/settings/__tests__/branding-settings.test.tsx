import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrandingSettings } from '../branding-settings'

// Mock document for preview functionality
Object.defineProperty(document, 'documentElement', {
  value: {
    style: {
      setProperty: jest.fn(),
      removeProperty: jest.fn(),
    },
  },
  writable: true,
})

Object.defineProperty(document, 'title', {
  value: 'EmailFlow',
  writable: true,
})

describe('BrandingSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render branding configuration form', () => {
    render(<BrandingSettings />)

    expect(screen.getByText('Branding Configuration')).toBeInTheDocument()
    expect(screen.getByLabelText('Application Name')).toBeInTheDocument()
    expect(screen.getByLabelText('Logo URL')).toBeInTheDocument()
    expect(screen.getByText('Colors')).toBeInTheDocument()
    expect(screen.getByLabelText('Primary Color')).toBeInTheDocument()
    expect(screen.getByLabelText('Secondary Color')).toBeInTheDocument()
    expect(screen.getByLabelText('Accent Color')).toBeInTheDocument()
    expect(screen.getByText('Theme')).toBeInTheDocument()
    expect(screen.getByLabelText('Border Radius')).toBeInTheDocument()
    expect(screen.getByLabelText('Font Family')).toBeInTheDocument()
    expect(screen.getByText('Preview Changes')).toBeInTheDocument()
  })

  it('should populate form with initial config', () => {
    const initialConfig = {
      appName: 'Test App',
      logo: '/test-logo.png',
      colors: {
        primary: '#ff0000',
        secondary: '#00ff00',
        accent: '#0000ff',
      },
      theme: {
        borderRadius: '1rem',
        fontFamily: 'Roboto',
      },
    }

    render(<BrandingSettings initialConfig={initialConfig} />)

    expect(screen.getByDisplayValue('Test App')).toBeInTheDocument()
    expect(screen.getByDisplayValue('/test-logo.png')).toBeInTheDocument()
    
    // Check color values (there are multiple inputs with same values)
    const primaryColorInputs = screen.getAllByDisplayValue('#ff0000')
    expect(primaryColorInputs).toHaveLength(2) // Color picker and text input
    
    const secondaryColorInputs = screen.getAllByDisplayValue('#00ff00')
    expect(secondaryColorInputs).toHaveLength(2)
    
    const accentColorInputs = screen.getAllByDisplayValue('#0000ff')
    expect(accentColorInputs).toHaveLength(2)
    
    expect(screen.getByDisplayValue('1rem')).toBeInTheDocument()
    
    // Check select element value
    const fontSelect = screen.getByLabelText('Font Family') as HTMLSelectElement
    expect(fontSelect.value).toBe('Roboto')
  })

  it('should call onConfigChange when form values change', () => {
    const onConfigChange = jest.fn()
    render(<BrandingSettings onConfigChange={onConfigChange} />)

    const appNameInput = screen.getByLabelText('Application Name')
    fireEvent.change(appNameInput, { target: { value: 'New App Name' } })

    expect(onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        appName: 'New App Name',
      })
    )
  })

  it('should handle color input changes', () => {
    const onConfigChange = jest.fn()
    render(<BrandingSettings onConfigChange={onConfigChange} />)

    const primaryColorInput = screen.getAllByDisplayValue('#3b82f6')[0] // Color picker
    fireEvent.change(primaryColorInput, { target: { value: '#ff0000' } })

    expect(onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        colors: expect.objectContaining({
          primary: '#ff0000',
        }),
      })
    )
  })

  it('should handle nested theme property changes', () => {
    const onConfigChange = jest.fn()
    render(<BrandingSettings onConfigChange={onConfigChange} />)

    const borderRadiusInput = screen.getByLabelText('Border Radius')
    fireEvent.change(borderRadiusInput, { target: { value: '1rem' } })

    expect(onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        theme: expect.objectContaining({
          borderRadius: '1rem',
        }),
      })
    )
  })

  it('should toggle preview mode', () => {
    render(<BrandingSettings />)

    const previewButton = screen.getByText('Preview Changes')
    fireEvent.click(previewButton)

    expect(screen.getByText('Exit Preview')).toBeInTheDocument()
    expect(screen.getByText(/Preview mode is active/)).toBeInTheDocument()

    // Should apply CSS custom properties
    expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--brand-primary',
      '#3b82f6'
    )
    expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--brand-secondary',
      '#64748b'
    )
    expect(document.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--brand-accent',
      '#f59e0b'
    )
  })

  it('should exit preview mode and reset styles', () => {
    render(<BrandingSettings />)

    const previewButton = screen.getByText('Preview Changes')
    fireEvent.click(previewButton)

    const exitButton = screen.getByText('Exit Preview')
    fireEvent.click(exitButton)

    expect(screen.getByText('Preview Changes')).toBeInTheDocument()
    expect(screen.queryByText(/Preview mode is active/)).not.toBeInTheDocument()

    // Should remove CSS custom properties
    expect(document.documentElement.style.removeProperty).toHaveBeenCalledWith(
      '--brand-primary'
    )
    expect(document.documentElement.style.removeProperty).toHaveBeenCalledWith(
      '--brand-secondary'
    )
    expect(document.documentElement.style.removeProperty).toHaveBeenCalledWith(
      '--brand-accent'
    )
  })

  it('should update preview when config changes in preview mode', async () => {
    render(<BrandingSettings />)

    // Enter preview mode
    const previewButton = screen.getByText('Preview Changes')
    fireEvent.click(previewButton)

    // Change app name
    const appNameInput = screen.getByLabelText('Application Name')
    fireEvent.change(appNameInput, { target: { value: 'New App' } })

    // Should update document title
    await waitFor(() => {
      expect(document.title).toBe('New App')
    })
  })

  it('should render font family options', () => {
    render(<BrandingSettings />)

    const fontSelect = screen.getByLabelText('Font Family')
    fireEvent.click(fontSelect)

    // Check that common font options are available
    expect(screen.getByText('Inter')).toBeInTheDocument()
    expect(screen.getByText('Roboto')).toBeInTheDocument()
    expect(screen.getByText('Open Sans')).toBeInTheDocument()
    expect(screen.getByText('Montserrat')).toBeInTheDocument()
  })

  it('should handle color picker and text input synchronization', () => {
    const onConfigChange = jest.fn()
    render(<BrandingSettings onConfigChange={onConfigChange} />)

    // Change color via text input
    const primaryColorTextInput = screen.getAllByDisplayValue('#3b82f6')[1] // Text input
    fireEvent.change(primaryColorTextInput, { target: { value: '#123456' } })

    expect(onConfigChange).toHaveBeenCalledWith(
      expect.objectContaining({
        colors: expect.objectContaining({
          primary: '#123456',
        }),
      })
    )
  })
})