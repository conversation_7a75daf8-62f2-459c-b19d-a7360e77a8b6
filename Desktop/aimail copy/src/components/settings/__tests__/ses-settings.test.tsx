import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SESSettings } from '../ses-settings'

// Mock fetch
global.fetch = jest.fn()

describe('SESSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render SES configuration form', () => {
    render(<SESSettings />)

    expect(screen.getByText('Amazon SES Configuration')).toBeInTheDocument()
    expect(screen.getByLabelText('AWS Access Key ID')).toBeInTheDocument()
    expect(screen.getByLabelText('AWS Secret Access Key')).toBeInTheDocument()
    expect(screen.getByLabelText('AWS Region')).toBeInTheDocument()
    expect(screen.getByLabelText('From Email Address')).toBeInTheDocument()
    expect(screen.getByText('Test Connection')).toBeInTheDocument()
    expect(screen.getByText('Verify Email')).toBeInTheDocument()
  })

  it('should populate form with initial config', () => {
    const initialConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-west-2',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={initialConfig} />)

    expect(screen.getByDisplayValue('test-key')).toBeInTheDocument()
    expect(screen.getByDisplayValue('test-secret')).toBeInTheDocument()
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    
    // Check select element value
    const regionSelect = screen.getByLabelText('AWS Region') as HTMLSelectElement
    expect(regionSelect.value).toBe('us-west-2')
  })

  it('should call onConfigChange when form values change', () => {
    const onConfigChange = jest.fn()
    render(<SESSettings onConfigChange={onConfigChange} />)

    const accessKeyInput = screen.getByLabelText('AWS Access Key ID')
    fireEvent.change(accessKeyInput, { target: { value: 'new-key' } })

    expect(onConfigChange).toHaveBeenCalledWith({
      accessKeyId: 'new-key',
      secretAccessKey: '',
      region: 'us-east-1',
      fromEmail: '',
    })
  })

  it('should disable test buttons when config is incomplete', () => {
    render(<SESSettings />)

    const testButton = screen.getByText('Test Connection')
    const verifyButton = screen.getByText('Verify Email')

    expect(testButton).toBeDisabled()
    expect(verifyButton).toBeDisabled()
  })

  it('should enable test connection button when config is complete', () => {
    const completeConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    const testButton = screen.getByText('Test Connection')
    expect(testButton).not.toBeDisabled()
  })

  it('should test SES connection successfully', async () => {
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        valid: true,
        quota: {
          max24HourSend: 200,
          maxSendRate: 1,
          sentLast24Hours: 0,
        },
      }),
    } as Response)

    const completeConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    const testButton = screen.getByText('Test Connection')
    fireEvent.click(testButton)

    expect(screen.getByText('Testing...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText('SES connection successful!')).toBeInTheDocument()
      expect(screen.getByText('24h Limit: 200')).toBeInTheDocument()
      expect(screen.getByText('Rate: 1/sec')).toBeInTheDocument()
      expect(screen.getByText('Sent Today: 0')).toBeInTheDocument()
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'ses',
        config: completeConfig,
      }),
    })
  })

  it('should handle SES connection test failure', async () => {
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        valid: false,
        error: 'Invalid credentials',
      }),
    } as Response)

    const completeConfig = {
      accessKeyId: 'invalid-key',
      secretAccessKey: 'invalid-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    const testButton = screen.getByText('Test Connection')
    fireEvent.click(testButton)

    await waitFor(() => {
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
    })
  })

  it('should verify email identity successfully', async () => {
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        valid: true,
      }),
    } as Response)

    const completeConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    const verifyButton = screen.getByText('Verify Email')
    fireEvent.click(verifyButton)

    expect(screen.getByText('Verifying...')).toBeInTheDocument()

    await waitFor(() => {
      expect(screen.getByText(/Email identity verification initiated/)).toBeInTheDocument()
    })

    expect(mockFetch).toHaveBeenCalledWith('/api/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'ses-email',
        config: completeConfig,
      }),
    })
  })

  it('should handle email verification failure', async () => {
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        valid: false,
        error: 'Email not verified',
      }),
    } as Response)

    const completeConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    const verifyButton = screen.getByText('Verify Email')
    fireEvent.click(verifyButton)

    await waitFor(() => {
      expect(screen.getByText('Email not verified')).toBeInTheDocument()
    })
  })

  it('should clear test results when config changes', async () => {
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ valid: true }),
    } as Response)

    const completeConfig = {
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      region: 'us-east-1',
      fromEmail: '<EMAIL>',
    }

    render(<SESSettings initialConfig={completeConfig} />)

    // Test connection first
    const testButton = screen.getByText('Test Connection')
    fireEvent.click(testButton)

    await waitFor(() => {
      expect(screen.getByText('SES connection successful!')).toBeInTheDocument()
    })

    // Change config
    const accessKeyInput = screen.getByLabelText('AWS Access Key ID')
    fireEvent.change(accessKeyInput, { target: { value: 'new-key' } })

    // Test result should be cleared
    expect(screen.queryByText('SES connection successful!')).not.toBeInTheDocument()
  })
})