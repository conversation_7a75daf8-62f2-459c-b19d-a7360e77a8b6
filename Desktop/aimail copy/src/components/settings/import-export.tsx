'use client'

import { useState, useRef } from 'react'
import { <PERSON><PERSON> } from '../ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Download, Upload, FileText, CheckCircle, XCircle } from 'lucide-react'

interface ImportExportProps {
  onImport?: (settings: any) => void
}

export function ImportExport({ onImport }: ImportExportProps) {
  const [importing, setImporting] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [importResult, setImportResult] = useState<{
    type: 'success' | 'error'
    message: string
    settings?: any
  } | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleExport = async () => {
    setExporting(true)
    
    try {
      const response = await fetch('/api/settings/export')
      
      if (!response.ok) {
        throw new Error('Failed to export settings')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `emailflow-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setExporting(false)
    }
  }

  const handleImportClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImporting(true)
    setImportResult(null)

    try {
      const text = await file.text()
      
      const response = await fetch('/api/settings/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data: text }),
      })

      const result = await response.json()

      if (response.ok) {
        setImportResult({
          type: 'success',
          message: result.message,
          settings: result.settings,
        })
        
        // Call the onImport callback with the imported settings
        onImport?.(result.settings)
      } else {
        setImportResult({
          type: 'error',
          message: result.error || 'Import failed',
        })
      }
    } catch (error) {
      setImportResult({
        type: 'error',
        message: 'Failed to read or parse the settings file',
      })
    } finally {
      setImporting(false)
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Import / Export Settings
        </CardTitle>
        <CardDescription>
          Backup your settings or import configuration from another instance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            onClick={handleExport}
            disabled={exporting}
            variant="outline"
          >
            <Download className="mr-2 h-4 w-4" />
            {exporting ? 'Exporting...' : 'Export Settings'}
          </Button>

          <Button
            onClick={handleImportClick}
            disabled={importing}
            variant="outline"
          >
            <Upload className="mr-2 h-4 w-4" />
            {importing ? 'Importing...' : 'Import Settings'}
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        {importResult && (
          <Alert>
            <div className="flex items-center gap-2">
              {importResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>{importResult.message}</AlertDescription>
            </div>
          </Alert>
        )}

        <div className="text-sm text-muted-foreground space-y-2">
          <p>
            <strong>Export:</strong> Downloads a JSON file containing your current settings 
            (excluding sensitive API keys for security).
          </p>
          <p>
            <strong>Import:</strong> Uploads and validates a settings file. You'll need to 
            re-enter API keys after importing for security reasons.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}