'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Badge } from '../ui/badge'
import { Loader2, CheckCircle, XCircle, Mail } from 'lucide-react'

interface SESConfig {
  accessKeyId: string
  secretAccessKey: string
  region: string
  fromEmail: string
}

interface SESSettingsProps {
  initialConfig?: Partial<SESConfig>
  onConfigChange?: (config: SESConfig) => void
}

export function SESSettings({ initialConfig, onConfigChange }: SESSettingsProps) {
  const [config, setConfig] = useState<SESConfig>({
    accessKeyId: initialConfig?.accessKeyId || '',
    secretAccessKey: initialConfig?.secretAccessKey || '',
    region: initialConfig?.region || 'us-east-1',
    fromEmail: initialConfig?.fromEmail || '',
  })

  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{
    type: 'success' | 'error'
    message: string
    quota?: any
  } | null>(null)

  const [verifyingEmail, setVerifyingEmail] = useState(false)
  const [emailVerifyResult, setEmailVerifyResult] = useState<{
    type: 'success' | 'error'
    message: string
  } | null>(null)

  const handleInputChange = (field: keyof SESConfig, value: string) => {
    const newConfig = { ...config, [field]: value }
    setConfig(newConfig)
    onConfigChange?.(newConfig)
    setTestResult(null) // Clear test results when config changes
    setEmailVerifyResult(null)
  }

  const testConnection = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'ses',
          config,
        }),
      })

      const result = await response.json()

      if (result.valid) {
        setTestResult({
          type: 'success',
          message: 'SES connection successful!',
          quota: result.quota,
        })
      } else {
        setTestResult({
          type: 'error',
          message: result.error || 'Connection failed',
        })
      }
    } catch (error) {
      setTestResult({
        type: 'error',
        message: 'Failed to test connection',
      })
    } finally {
      setTesting(false)
    }
  }

  const verifyEmailIdentity = async () => {
    setVerifyingEmail(true)
    setEmailVerifyResult(null)

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'ses-email',
          config,
        }),
      })

      const result = await response.json()

      if (result.valid) {
        setEmailVerifyResult({
          type: 'success',
          message: 'Email identity verification initiated. Check your email for verification link.',
        })
      } else {
        setEmailVerifyResult({
          type: 'error',
          message: result.error || 'Email verification failed',
        })
      }
    } catch (error) {
      setEmailVerifyResult({
        type: 'error',
        message: 'Failed to verify email identity',
      })
    } finally {
      setVerifyingEmail(false)
    }
  }

  const isConfigComplete = config.accessKeyId && config.secretAccessKey && config.region && config.fromEmail

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Amazon SES Configuration
        </CardTitle>
        <CardDescription>
          Configure Amazon Simple Email Service for sending emails
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="accessKeyId">AWS Access Key ID</Label>
            <Input
              id="accessKeyId"
              type="password"
              value={config.accessKeyId}
              onChange={(e) => handleInputChange('accessKeyId', e.target.value)}
              placeholder="AKIA..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="secretAccessKey">AWS Secret Access Key</Label>
            <Input
              id="secretAccessKey"
              type="password"
              value={config.secretAccessKey}
              onChange={(e) => handleInputChange('secretAccessKey', e.target.value)}
              placeholder="Enter secret key"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="region">AWS Region</Label>
            <select
              id="region"
              value={config.region}
              onChange={(e) => handleInputChange('region', e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="us-east-1">US East (N. Virginia)</option>
              <option value="us-west-2">US West (Oregon)</option>
              <option value="eu-west-1">Europe (Ireland)</option>
              <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="fromEmail">From Email Address</Label>
            <Input
              id="fromEmail"
              type="email"
              value={config.fromEmail}
              onChange={(e) => handleInputChange('fromEmail', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={testConnection}
            disabled={!isConfigComplete || testing}
            variant="outline"
          >
            {testing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Testing...
              </>
            ) : (
              'Test Connection'
            )}
          </Button>

          <Button
            onClick={verifyEmailIdentity}
            disabled={!config.fromEmail || verifyingEmail}
            variant="outline"
          >
            {verifyingEmail ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              'Verify Email'
            )}
          </Button>
        </div>

        {testResult && (
          <Alert>
            <div className="flex items-center gap-2">
              {testResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>
                {testResult.message}
                {testResult.quota && (
                  <div className="mt-2 space-y-1">
                    <div className="flex gap-4 text-sm">
                      <Badge variant="outline">
                        24h Limit: {testResult.quota.max24HourSend}
                      </Badge>
                      <Badge variant="outline">
                        Rate: {testResult.quota.maxSendRate}/sec
                      </Badge>
                      <Badge variant="outline">
                        Sent Today: {testResult.quota.sentLast24Hours}
                      </Badge>
                    </div>
                  </div>
                )}
              </AlertDescription>
            </div>
          </Alert>
        )}

        {emailVerifyResult && (
          <Alert>
            <div className="flex items-center gap-2">
              {emailVerifyResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>{emailVerifyResult.message}</AlertDescription>
            </div>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}