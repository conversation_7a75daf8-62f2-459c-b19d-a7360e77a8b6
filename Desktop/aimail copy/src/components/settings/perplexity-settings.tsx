'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Loader2, CheckCircle, XCircle, Zap } from 'lucide-react'

interface PerplexityConfig {
  apiKey: string
}

interface PerplexitySettingsProps {
  initialConfig?: Partial<PerplexityConfig>
  onConfigChange?: (config: PerplexityConfig) => void
}

export function PerplexitySettings({ initialConfig, onConfigChange }: PerplexitySettingsProps) {
  const [config, setConfig] = useState<PerplexityConfig>({
    apiKey: initialConfig?.apiKey || '',
  })

  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<{
    type: 'success' | 'error'
    message: string
  } | null>(null)

  const handleInputChange = (field: keyof PerplexityConfig, value: string) => {
    const newConfig = { ...config, [field]: value }
    setConfig(newConfig)
    onConfigChange?.(newConfig)
    setTestResult(null) // Clear test results when config changes
  }

  const testConnection = async () => {
    setTesting(true)
    setTestResult(null)

    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'perplexity',
          config,
        }),
      })

      const result = await response.json()

      if (result.valid) {
        setTestResult({
          type: 'success',
          message: 'Perplexity API connection successful!',
        })
      } else {
        setTestResult({
          type: 'error',
          message: result.error || 'Connection failed',
        })
      }
    } catch (error) {
      setTestResult({
        type: 'error',
        message: 'Failed to test connection',
      })
    } finally {
      setTesting(false)
    }
  }

  const isConfigComplete = config.apiKey.length > 0

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Perplexity AI Configuration
        </CardTitle>
        <CardDescription>
          Configure Perplexity AI for email content generation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="apiKey">Perplexity API Key</Label>
          <Input
            id="apiKey"
            type="password"
            value={config.apiKey}
            onChange={(e) => handleInputChange('apiKey', e.target.value)}
            placeholder="pplx-..."
          />
          <p className="text-sm text-muted-foreground">
            Get your API key from{' '}
            <a
              href="https://www.perplexity.ai/settings/api"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              Perplexity AI Settings
            </a>
          </p>
        </div>

        <Button
          onClick={testConnection}
          disabled={!isConfigComplete || testing}
          variant="outline"
        >
          {testing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Testing...
            </>
          ) : (
            'Test Connection'
          )}
        </Button>

        {testResult && (
          <Alert>
            <div className="flex items-center gap-2">
              {testResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>{testResult.message}</AlertDescription>
            </div>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}