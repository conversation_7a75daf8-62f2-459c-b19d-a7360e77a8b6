import { render, screen } from '@testing-library/react'
import { AuthWrapper } from '../auth-wrapper'
import { useAuth } from '../../../contexts/auth-context'

// Mock the auth context
jest.mock('../../../contexts/auth-context')
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>

// Mock the LoginForm component
jest.mock('../login-form', () => ({
  LoginForm: () => <div data-testid="login-form">Login Form</div>,
}))

describe('AuthWrapper', () => {
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00Z',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('shows loading spinner when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: true,
      signIn: jest.fn(),
      signOut: jest.fn(),
    })

    render(
      <AuthWrapper>
        <div>Protected Content</div>
      </AuthWrapper>
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument()
  })

  it('shows login form when not authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      loading: false,
      signIn: jest.fn(),
      signOut: jest.fn(),
    })

    render(
      <AuthWrapper>
        <div>Protected Content</div>
      </AuthWrapper>
    )

    expect(screen.getByTestId('login-form')).toBeInTheDocument()
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument()
  })

  it('shows protected content when authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: mockUser as any,
      loading: false,
      signIn: jest.fn(),
      signOut: jest.fn(),
    })

    render(
      <AuthWrapper>
        <div>Protected Content</div>
      </AuthWrapper>
    )

    expect(screen.getByText('Protected Content')).toBeInTheDocument()
    expect(screen.queryByTestId('login-form')).not.toBeInTheDocument()
  })
})