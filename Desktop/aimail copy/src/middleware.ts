import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { securityHeaders } from './lib/security'

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : req.ip || 'unknown'
  return `${ip}:${req.nextUrl.pathname}`
}

function checkRateLimit(key: string, limit: number, windowMs: number): boolean {
  const now = Date.now()
  const record = rateLimitStore.get(key)
  
  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= limit) {
    return false
  }
  
  record.count++
  return true
}

// CSRF token validation
function validateCSRFToken(req: NextRequest): boolean {
  if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {
    return true
  }
  
  const token = req.headers.get('x-csrf-token') || req.headers.get('x-requested-with')
  const cookie = req.cookies.get('csrf-token')?.value
  
  // For API routes, require either CSRF token or XMLHttpRequest header
  if (req.nextUrl.pathname.startsWith('/api/')) {
    return token === cookie || req.headers.get('x-requested-with') === 'XMLHttpRequest'
  }
  
  return true
}

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  
  // Add security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    res.headers.set(key, value)
  })
  
  // Rate limiting for API routes
  if (req.nextUrl.pathname.startsWith('/api/')) {
    const rateLimitKey = getRateLimitKey(req)
    
    // Different limits for different endpoints
    let limit = 100 // Default: 100 requests per minute
    let windowMs = 60 * 1000 // 1 minute
    
    if (req.nextUrl.pathname.includes('/auth/')) {
      limit = 5 // Auth endpoints: 5 requests per minute
    } else if (req.nextUrl.pathname.includes('/send')) {
      limit = 10 // Send endpoints: 10 requests per minute
    } else if (req.nextUrl.pathname.includes('/track/')) {
      limit = 1000 // Tracking endpoints: 1000 requests per minute
    }
    
    if (!checkRateLimit(rateLimitKey, limit, windowMs)) {
      return new NextResponse('Too Many Requests', { 
        status: 429,
        headers: {
          'Retry-After': '60',
          ...Object.fromEntries(Object.entries(securityHeaders))
        }
      })
    }
  }
  
  // CSRF protection
  if (!validateCSRFToken(req)) {
    return new NextResponse('CSRF token validation failed', { 
      status: 403,
      headers: Object.fromEntries(Object.entries(securityHeaders))
    })
  }
  
  // Generate CSRF token for new sessions
  if (!req.cookies.get('csrf-token')) {
    const csrfToken = crypto.randomUUID()
    res.cookies.set('csrf-token', csrfToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 // 24 hours
    })
  }
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          res.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          res.cookies.set({
            name,
            value: '',
            ...options,
          })
        },
      },
    }
  )

  // Refresh session if expired - required for Server Components
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/campaigns', '/contacts', '/settings']
  const isProtectedRoute = protectedRoutes.some(route => 
    req.nextUrl.pathname.startsWith(route)
  )

  // If accessing a protected route without authentication, redirect to login
  if (isProtectedRoute && !session) {
    const redirectUrl = req.nextUrl.clone()
    redirectUrl.pathname = '/login'
    redirectUrl.searchParams.set('redirectedFrom', req.nextUrl.pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // If authenticated and trying to access login page, redirect to dashboard
  if (session && req.nextUrl.pathname === '/login') {
    const redirectUrl = req.nextUrl.clone()
    redirectUrl.pathname = '/dashboard'
    return NextResponse.redirect(redirectUrl)
  }

  return res
}

export const config = {
  matcher: [
    // Temporarily disable middleware for debugging
    // '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}