import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { useAuth } from '../../../contexts/auth-context'
import DashboardPage from '../page'

// Mock the auth context
jest.mock('../../../contexts/auth-context', () => ({
  useAuth: jest.fn(),
}))

// Mock the dashboard layout
jest.mock('../../../components/layout/dashboard-layout', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dashboard-layout">{children}</div>
  ),
}))

// Mock the dashboard components
jest.mock('../../../components/dashboard/dashboard-widgets', () => ({
  DashboardWidgets: ({ metrics, loading }: any) => (
    <div data-testid="dashboard-widgets">
      {loading ? 'Loading widgets...' : `Widgets with ${metrics.totalCampaigns} campaigns`}
    </div>
  ),
}))

jest.mock('../../../components/dashboard/welcome-widget', () => ({
  WelcomeWidget: ({ userName, totalCampaigns, totalContacts }: any) => (
    <div data-testid="welcome-widget">
      Welcome {userName} - {totalCampaigns} campaigns, {totalContacts} contacts
    </div>
  ),
}))

jest.mock('../../../components/dashboard/performance-overview', () => ({
  PerformanceOverview: ({ metrics, loading }: any) => (
    <div data-testid="performance-overview">
      {loading ? 'Loading performance...' : `Performance: ${metrics.totalSends} sends`}
    </div>
  ),
}))

// Mock Next.js Link component
jest.mock('next/link', () => {
  const MockLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  )
  MockLink.displayName = 'MockLink'
  return MockLink
})

const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
}

const mockMetrics = {
  totalCampaigns: 5,
  totalContacts: 150,
  totalSends: 1000,
  totalOpens: 250,
  totalClicks: 50,
  averageOpenRate: 25.0,
  averageClickRate: 5.0,
  recentCampaigns: [],
}

// Mock fetch
global.fetch = jest.fn()

describe('DashboardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useAuth as jest.Mock).mockReturnValue({ user: mockUser })
  })

  it('renders dashboard layout and header', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Welcome back, test')).toBeInTheDocument()
    })
  })

  it('shows loading state initially', () => {
    ;(global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {}))

    render(<DashboardPage />)

    const loadingElements = screen.getAllByRole('generic')
    const animatedElements = loadingElements.filter(el => 
      el.className.includes('animate-pulse')
    )
    expect(animatedElements.length).toBeGreaterThan(0)
  })

  it('fetches and displays dashboard metrics', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByTestId('dashboard-widgets')).toBeInTheDocument()
      expect(screen.getByText('Widgets with 5 campaigns')).toBeInTheDocument()
    })

    expect(global.fetch).toHaveBeenCalledWith('/api/analytics/dashboard')
  })

  it('displays welcome widget with user information', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByTestId('welcome-widget')).toBeInTheDocument()
      expect(screen.getByText('Welcome <EMAIL> - 5 campaigns, 150 contacts')).toBeInTheDocument()
    })
  })

  it('shows performance overview when user has sent campaigns', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByTestId('performance-overview')).toBeInTheDocument()
      expect(screen.getByText('Performance: 1000 sends')).toBeInTheDocument()
    })
  })

  it('hides performance overview when user has no sends', async () => {
    const metricsWithoutSends = { ...mockMetrics, totalSends: 0 }
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: metricsWithoutSends }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByTestId('dashboard-widgets')).toBeInTheDocument()
    })

    expect(screen.queryByTestId('performance-overview')).not.toBeInTheDocument()
  })

  it('handles API error gracefully', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500,
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Unable to load dashboard')).toBeInTheDocument()
      expect(screen.getByText('Failed to fetch dashboard metrics')).toBeInTheDocument()
    })
  })

  it('shows retry button on error and allows retry', async () => {
    ;(global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: false,
        status: 500,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockMetrics }),
      })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByText('Try Again'))

    await waitFor(() => {
      expect(screen.getByTestId('dashboard-widgets')).toBeInTheDocument()
    })

    expect(global.fetch).toHaveBeenCalledTimes(2)
  })

  it('shows refresh button in header', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('🔄 Refresh')).toBeInTheDocument()
    })
  })

  it('allows manual refresh via refresh button', async () => {
    ;(global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockMetrics }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: { ...mockMetrics, totalCampaigns: 6 } }),
      })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('🔄 Refresh')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByText('🔄 Refresh'))

    await waitFor(() => {
      expect(screen.getByText('Widgets with 6 campaigns')).toBeInTheDocument()
    })

    expect(global.fetch).toHaveBeenCalledTimes(2)
  })

  it('handles network error gracefully', async () => {
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Unable to load dashboard')).toBeInTheDocument()
      expect(screen.getByText('Network error')).toBeInTheDocument()
    })
  })

  it('uses safe default metrics when API returns null', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: null }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByTestId('welcome-widget')).toBeInTheDocument()
      expect(screen.getByText('Welcome <EMAIL> - 0 campaigns, 0 contacts')).toBeInTheDocument()
    })
  })

  it('shows pro tip section', async () => {
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('💡 Pro Tip')).toBeInTheDocument()
    })
  })

  it('displays appropriate pro tip based on performance', async () => {
    const lowPerformanceMetrics = {
      ...mockMetrics,
      averageOpenRate: 15.0,
      averageClickRate: 2.0,
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: lowPerformanceMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText(/try a\/b testing your subject lines/i)).toBeInTheDocument()
    })
  })

  it('shows positive pro tip for good performance', async () => {
    const goodPerformanceMetrics = {
      ...mockMetrics,
      averageOpenRate: 30.0,
      averageClickRate: 6.0,
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: goodPerformanceMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText(/your campaigns are performing great/i)).toBeInTheDocument()
    })
  })

  it('handles missing user email gracefully', async () => {
    ;(useAuth as jest.Mock).mockReturnValue({ user: { id: 'user-1', email: null } })
    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, data: mockMetrics }),
    })

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Welcome back, there')).toBeInTheDocument()
    })
  })
})