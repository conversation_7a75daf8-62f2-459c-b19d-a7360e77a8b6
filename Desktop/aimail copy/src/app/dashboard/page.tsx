'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '../../contexts/auth-context'
import { DashboardLayout } from '../../components/layout/dashboard-layout'
import { Button } from '../../components/ui/button'
import { DashboardMetrics } from '../../lib/analytics'
import { DashboardWidgets, WelcomeWidget, PerformanceOverview } from '../../components/dashboard'

export default function DashboardPage() {
  const { user } = useAuth()
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardMetrics()
  }, [])

  const fetchDashboardMetrics = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/analytics/dashboard')
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard metrics')
      }

      const data = await response.json()
      setMetrics(data.data)
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error)
      setError(error instanceof Error ? error.message : 'Failed to load dashboard')
    } finally {
      setLoading(false)
    }
  }

  // Show loading state
  if (loading && !metrics) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <div className="h-8 w-48 bg-gray-200 animate-pulse rounded mb-2"></div>
            <div className="h-4 w-64 bg-gray-200 animate-pulse rounded"></div>
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="p-6 bg-white border rounded-lg">
                <div className="h-4 w-24 bg-gray-200 animate-pulse rounded mb-4"></div>
                <div className="h-8 w-16 bg-gray-200 animate-pulse rounded mb-2"></div>
                <div className="h-3 w-32 bg-gray-200 animate-pulse rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Show error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Welcome back, {user?.email}
            </p>
          </div>
          
          <div className="text-center py-12">
            <div className="text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to load dashboard</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={fetchDashboardMetrics}>
              Try Again
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  // Default metrics for safety
  const safeMetrics = metrics || {
    totalCampaigns: 0,
    totalContacts: 0,
    totalSends: 0,
    totalOpens: 0,
    totalClicks: 0,
    averageOpenRate: 0,
    averageClickRate: 0,
    recentCampaigns: [],
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Welcome back, {user?.email?.split('@')[0] || 'there'}
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button onClick={fetchDashboardMetrics} variant="outline" size="sm">
              🔄 Refresh
            </Button>
          </div>
        </div>

        {/* Welcome Widget for new users or Progress for existing users */}
        <WelcomeWidget 
          userName={user?.email}
          totalCampaigns={safeMetrics.totalCampaigns}
          totalContacts={safeMetrics.totalContacts}
        />

        {/* Main Dashboard Widgets */}
        <DashboardWidgets metrics={safeMetrics} loading={loading} />

        {/* Performance Overview - only show if user has sent campaigns */}
        {safeMetrics.totalSends > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <PerformanceOverview metrics={safeMetrics} loading={loading} />
            </div>
            <div className="space-y-4">
              {/* Additional widgets can go here */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg">
                <h3 className="font-medium text-purple-900 mb-2">💡 Pro Tip</h3>
                <p className="text-sm text-purple-700">
                  {safeMetrics.averageOpenRate < 20 
                    ? "Try A/B testing your subject lines to improve open rates!"
                    : safeMetrics.averageClickRate < 3
                    ? "Add more compelling call-to-action buttons to boost clicks!"
                    : "Your campaigns are performing great! Keep it up!"
                  }
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}