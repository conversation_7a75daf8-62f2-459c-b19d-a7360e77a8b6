'use client'

import { useEffect, useState } from 'react'
import { DashboardLayout } from '../../components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { DashboardMetrics, TimeSeriesData, CampaignAnalytics } from '../../lib/analytics'

interface AnalyticsData {
  dashboard: DashboardMetrics | null
  timeSeries: TimeSeriesData[]
  topCampaigns: CampaignAnalytics[]
  loading: boolean
  error: string | null
}

export default function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData>({
    dashboard: null,
    timeSeries: [],
    topCampaigns: [],
    loading: true,
    error: null,
  })

  useEffect(() => {
    fetchAnalyticsData()
  }, [])

  const fetchAnalyticsData = async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }))

      // Fetch all analytics data in parallel
      const [dashboardRes, timeSeriesRes, topCampaignsRes] = await Promise.all([
        fetch('/api/analytics/dashboard'),
        fetch('/api/analytics/timeseries?days=30'),
        fetch('/api/analytics/top-campaigns?limit=5'),
      ])

      if (!dashboardRes.ok || !timeSeriesRes.ok || !topCampaignsRes.ok) {
        throw new Error('Failed to fetch analytics data')
      }

      const [dashboardData, timeSeriesData, topCampaignsData] = await Promise.all([
        dashboardRes.json(),
        timeSeriesRes.json(),
        topCampaignsRes.json(),
      ])

      setData({
        dashboard: dashboardData.data,
        timeSeries: timeSeriesData.data,
        topCampaigns: topCampaignsData.data,
        loading: false,
        error: null,
      })
    } catch (error) {
      console.error('Error fetching analytics data:', error)
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load analytics',
      }))
    }
  }

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatNumber = (value: number) => value.toLocaleString()

  if (data.loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600 mt-2">
              Track your email campaign performance
            </p>
          </div>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading analytics...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (data.error) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600 mt-2">
              Track your email campaign performance
            </p>
          </div>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <p className="text-red-500 mb-4">{data.error}</p>
                <button
                  onClick={fetchAnalyticsData}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Retry
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  const { dashboard } = data

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-2">
            Track your email campaign performance
          </p>
        </div>

        {/* Overview Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Campaigns</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(dashboard?.totalCampaigns || 0)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Contacts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(dashboard?.totalContacts || 0)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Emails Sent</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatNumber(dashboard?.totalSends || 0)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Average Open Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatPercentage(dashboard?.averageOpenRate || 0)}</div>
            </CardContent>
          </Card>
        </div>

        {/* Engagement Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Email Engagement</CardTitle>
              <CardDescription>Total opens and clicks across all campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Opens</span>
                  <span className="text-lg font-bold">{formatNumber(dashboard?.totalOpens || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Clicks</span>
                  <span className="text-lg font-bold">{formatNumber(dashboard?.totalClicks || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Average Click Rate</span>
                  <span className="text-lg font-bold">{formatPercentage(dashboard?.averageClickRate || 0)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>Last 30 days activity summary</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.timeSeries.length > 0 ? (
                  <div className="text-sm text-gray-600">
                    <p>Total activity over {data.timeSeries.length} days</p>
                    <div className="mt-2 space-y-1">
                      <div>Opens: {formatNumber(data.timeSeries.reduce((sum, day) => sum + day.opens, 0))}</div>
                      <div>Clicks: {formatNumber(data.timeSeries.reduce((sum, day) => sum + day.clicks, 0))}</div>
                      <div>Sends: {formatNumber(data.timeSeries.reduce((sum, day) => sum + day.sends, 0))}</div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">No recent activity</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Performing Campaigns */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Campaigns</CardTitle>
            <CardDescription>Campaigns with the highest open rates</CardDescription>
          </CardHeader>
          <CardContent>
            {data.topCampaigns.length > 0 ? (
              <div className="space-y-4">
                {data.topCampaigns.map((campaign) => (
                  <div key={campaign.campaignId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-medium">{campaign.campaignName}</h3>
                      <p className="text-sm text-gray-600 truncate">{campaign.subject}</p>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                        <span>Sent: {formatNumber(campaign.totalSends)}</span>
                        <span>Opens: {formatNumber(campaign.opens)}</span>
                        <span>Clicks: {formatNumber(campaign.clicks)}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className="mb-1">
                        {formatPercentage(campaign.openRate)} open rate
                      </Badge>
                      <div className="text-sm text-gray-500">
                        {formatPercentage(campaign.clickRate)} click rate
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No campaigns found. Send your first campaign to see analytics.
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Campaigns */}
        {dashboard?.recentCampaigns && dashboard.recentCampaigns.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Campaigns</CardTitle>
              <CardDescription>Latest campaign performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboard.recentCampaigns.map((campaign) => (
                  <div key={campaign.campaignId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h3 className="font-medium">{campaign.campaignName}</h3>
                      <p className="text-sm text-gray-600 truncate">{campaign.subject}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={campaign.status === 'sent' ? 'default' : 'secondary'}>
                          {campaign.status}
                        </Badge>
                        {campaign.sentAt && (
                          <span className="text-sm text-gray-500">
                            {new Date(campaign.sentAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500 space-y-1">
                        <div>Sent: {formatNumber(campaign.totalSends)}</div>
                        <div>Open Rate: {formatPercentage(campaign.openRate)}</div>
                        <div>Click Rate: {formatPercentage(campaign.clickRate)}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}