import { NextRequest, NextResponse } from 'next/server'
import { SettingsService } from '../../../../lib/settings'

// POST /api/settings/import - Import and validate settings configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { data } = body

    if (!data) {
      return NextResponse.json(
        { error: 'Missing settings data' },
        { status: 400 }
      )
    }

    const result = SettingsService.importSettings(data)

    if (!result.valid) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      settings: result.settings,
      message: 'Settings imported successfully. Please review and save.',
    })
  } catch (error) {
    console.error('Error importing settings:', error)
    return NextResponse.json(
      { error: 'Failed to import settings' },
      { status: 500 }
    )
  }
}