import { GET, POST } from '../route'
import { NextRequest } from 'next/server'

// Mock the settings service
jest.mock('../../../../lib/settings', () => ({
  SettingsService: {
    validateSESConfig: jest.fn(),
    testSESEmailIdentity: jest.fn(),
    validatePerplexityConfig: jest.fn(),
  },
}))

describe('/api/settings', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.AWS_REGION = 'us-east-1'
    process.env.SES_FROM_EMAIL = '<EMAIL>'
    process.env.AWS_ACCESS_KEY_ID = 'test-key'
    process.env.AWS_SECRET_ACCESS_KEY = 'test-secret'
    process.env.PERPLEXITY_API_KEY = 'test-perplexity-key'
    process.env.NEXT_PUBLIC_APP_NAME = 'Test App'
  })

  describe('GET', () => {
    it('should return current settings without sensitive data', async () => {
      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.ses.region).toBe('us-east-1')
      expect(data.ses.fromEmail).toBe('<EMAIL>')
      expect(data.ses.configured).toBe(true)
      expect(data.perplexity.configured).toBe(true)
      expect(data.branding.appName).toBe('Test App')
      
      // Sensitive data should not be returned
      expect(data.ses.accessKeyId).toBeUndefined()
      expect(data.ses.secretAccessKey).toBeUndefined()
    })

    it('should handle missing environment variables', async () => {
      delete process.env.AWS_ACCESS_KEY_ID
      delete process.env.AWS_SECRET_ACCESS_KEY
      delete process.env.PERPLEXITY_API_KEY

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.ses.configured).toBe(false)
      expect(data.perplexity.configured).toBe(false)
    })
  })

  describe('POST', () => {
    it('should validate SES configuration', async () => {
      const { SettingsService } = require('../../../../lib/settings')
      SettingsService.validateSESConfig.mockResolvedValue({
        valid: true,
        quota: { max24HourSend: 200, maxSendRate: 1, sentLast24Hours: 0 },
      })

      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({
          type: 'ses',
          config: {
            accessKeyId: 'test-key',
            secretAccessKey: 'test-secret',
            region: 'us-east-1',
            fromEmail: '<EMAIL>',
          },
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.valid).toBe(true)
      expect(data.quota).toBeDefined()
      expect(SettingsService.validateSESConfig).toHaveBeenCalledWith({
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        region: 'us-east-1',
        fromEmail: '<EMAIL>',
      })
    })

    it('should validate SES email identity', async () => {
      const { SettingsService } = require('../../../../lib/settings')
      SettingsService.testSESEmailIdentity.mockResolvedValue({
        valid: true,
      })

      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({
          type: 'ses-email',
          config: {
            accessKeyId: 'test-key',
            secretAccessKey: 'test-secret',
            region: 'us-east-1',
            fromEmail: '<EMAIL>',
          },
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.valid).toBe(true)
      expect(SettingsService.testSESEmailIdentity).toHaveBeenCalled()
    })

    it('should validate Perplexity configuration', async () => {
      const { SettingsService } = require('../../../../lib/settings')
      SettingsService.validatePerplexityConfig.mockResolvedValue({
        valid: true,
      })

      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({
          type: 'perplexity',
          config: {
            apiKey: 'test-api-key',
          },
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.valid).toBe(true)
      expect(SettingsService.validatePerplexityConfig).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
      })
    })

    it('should return 400 for missing request data', async () => {
      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({}),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Missing type or config in request body')
    })

    it('should return 400 for invalid validation type', async () => {
      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({
          type: 'invalid-type',
          config: {},
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid validation type')
    })

    it('should handle validation service errors', async () => {
      const { SettingsService } = require('../../../../lib/settings')
      SettingsService.validateSESConfig.mockRejectedValue(new Error('Service error'))

      const request = new NextRequest('http://localhost:3000/api/settings', {
        method: 'POST',
        body: JSON.stringify({
          type: 'ses',
          config: {
            accessKeyId: 'test-key',
            secretAccessKey: 'test-secret',
            region: 'us-east-1',
            fromEmail: '<EMAIL>',
          },
        }),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to validate settings')
    })
  })
})