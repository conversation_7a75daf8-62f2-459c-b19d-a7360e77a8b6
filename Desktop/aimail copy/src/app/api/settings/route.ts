import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { SettingsService, settingsSchema } from '../../../lib/settings'

// GET /api/settings - Get current settings (without sensitive data)
export async function GET() {
  try {
    // Return current configuration without sensitive keys
    const settings = {
      ses: {
        region: process.env.AWS_REGION || '',
        fromEmail: process.env.SES_FROM_EMAIL || '',
        configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
      },
      perplexity: {
        configured: !!process.env.PERPLEXITY_API_KEY,
      },
      branding: {
        // Branding config is not sensitive, can be returned
        appName: process.env.NEXT_PUBLIC_APP_NAME || 'EmailFlow',
        logo: process.env.NEXT_PUBLIC_LOGO_URL || '/logo.png',
        colors: {
          primary: process.env.NEXT_PUBLIC_PRIMARY_COLOR || '#3b82f6',
          secondary: process.env.NEXT_PUBLIC_SECONDARY_COLOR || '#64748b',
          accent: process.env.NEXT_PUBLIC_ACCENT_COLOR || '#f59e0b',
        },
        theme: {
          borderRadius: process.env.NEXT_PUBLIC_BORDER_RADIUS || '0.5rem',
          fontFamily: process.env.NEXT_PUBLIC_FONT_FAMILY || 'Inter',
        },
      },
    }

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

// POST /api/settings/validate - Validate settings configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, config } = body

    if (!type || !config) {
      return NextResponse.json(
        { error: 'Missing type or config in request body' },
        { status: 400 }
      )
    }

    let result: { valid: boolean; error?: string; quota?: any }

    switch (type) {
      case 'ses':
        result = await SettingsService.validateSESConfig(config)
        break
      case 'ses-email':
        result = await SettingsService.testSESEmailIdentity(config)
        break
      case 'perplexity':
        result = await SettingsService.validatePerplexityConfig(config)
        break
      default:
        return NextResponse.json(
          { error: 'Invalid validation type' },
          { status: 400 }
        )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error validating settings:', error)
    return NextResponse.json(
      { error: 'Failed to validate settings' },
      { status: 500 }
    )
  }
}