import { NextResponse } from 'next/server'
import { SettingsService } from '../../../../lib/settings'

// GET /api/settings/export - Export settings configuration
export async function GET() {
  try {
    const settings = {
      ses: {
        region: process.env.AWS_REGION || '',
        fromEmail: process.env.SES_FROM_EMAIL || '',
      },
      perplexity: {
        // Don't export API key for security
      },
      branding: {
        appName: process.env.NEXT_PUBLIC_APP_NAME || 'EmailFlow',
        logo: process.env.NEXT_PUBLIC_LOGO_URL || '/logo.png',
        colors: {
          primary: process.env.NEXT_PUBLIC_PRIMARY_COLOR || '#3b82f6',
          secondary: process.env.NEXT_PUBLIC_SECONDARY_COLOR || '#64748b',
          accent: process.env.NEXT_PUBLIC_ACCENT_COLOR || '#f59e0b',
        },
        theme: {
          borderRadius: process.env.NEXT_PUBLIC_BORDER_RADIUS || '0.5rem',
          fontFamily: process.env.NEXT_PUBLIC_FONT_FAMILY || 'Inter',
        },
      },
    }

    const exportData = SettingsService.exportSettings(settings as any)

    return new NextResponse(exportData, {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="emailflow-settings-${new Date().toISOString().split('T')[0]}.json"`,
      },
    })
  } catch (error) {
    console.error('Error exporting settings:', error)
    return NextResponse.json(
      { error: 'Failed to export settings' },
      { status: 500 }
    )
  }
}