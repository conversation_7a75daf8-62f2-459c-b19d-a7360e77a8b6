import { NextRequest, NextResponse } from 'next/server'
import { aiService, AIGenerationRequest } from '@/lib/ai'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body: AIGenerationRequest = await request.json()
    
    // Validate required fields
    if (!body.productOrOffer || !body.goal) {
      return NextResponse.json(
        { 
          error: 'Validation Error', 
          message: 'productOrOffer and goal are required fields' 
        },
        { status: 400 }
      )
    }

    // Validate field lengths
    if (body.productOrOffer.length > 500) {
      return NextResponse.json(
        { 
          error: 'Validation Error', 
          message: 'productOrOffer must be less than 500 characters' 
        },
        { status: 400 }
      )
    }

    if (body.goal.length > 500) {
      return NextResponse.json(
        { 
          error: 'Validation Error', 
          message: 'goal must be less than 500 characters' 
        },
        { status: 400 }
      )
    }

    // Generate AI content
    const aiContent = await aiService.generateEmailContent(body)
    
    return NextResponse.json({
      success: true,
      data: aiContent
    })

  } catch (error) {
    console.error('AI generation API error:', error)
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('PERPLEXITY_API_KEY')) {
        return NextResponse.json(
          { 
            error: 'Configuration Error', 
            message: 'AI service is not properly configured' 
          },
          { status: 500 }
        )
      }
      
      if (error.message.includes('Perplexity API error')) {
        return NextResponse.json(
          { 
            error: 'External Service Error', 
            message: 'AI service is temporarily unavailable' 
          },
          { status: 502 }
        )
      }
    }
    
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        message: 'An unexpected error occurred' 
      },
      { status: 500 }
    )
  }
}