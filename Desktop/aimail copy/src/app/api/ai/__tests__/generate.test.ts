/**
 * @jest-environment node
 */
import { NextRequest } from 'next/server'
import { POST } from '../generate/route'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { it } from 'zod/locales'
import { beforeEach } from 'node:test'
import { describe } from 'node:test'

// Mock dependencies
jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createRouteHandlerClient: jest.fn()
}))

jest.mock('next/headers', () => ({
  cookies: jest.fn()
}))

jest.mock('@/lib/ai', () => ({
  aiService: {
    generateEmailContent: jest.fn()
  }
}))

const { createRouteHandlerClient } = require('@supabase/auth-helpers-nextjs')
const mockCreateRouteHandlerClient = createRouteHandlerClient as jest.Mock
const mockAiService = require('@/lib/ai').aiService

describe('/api/ai/generate', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should generate AI content successfully', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    // Mock AI service response
    mockAiService.generateEmailContent.mockResolvedValue({
      subjects: ['Great Subject Line'],
      bodies: ['<p>Amazing email content</p>']
    })

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'New product launch',
        goal: 'Drive sales',
        targetAudience: 'Tech enthusiasts',
        tone: 'professional'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.success).toBe(true)
    expect(data.data).toEqual({
      subjects: ['Great Subject Line'],
      bodies: ['<p>Amazing email content</p>']
    })

    expect(mockAiService.generateEmailContent).toHaveBeenCalledWith({
      productOrOffer: 'New product launch',
      goal: 'Drive sales',
      targetAudience: 'Tech enthusiasts',
      tone: 'professional'
    })
  })

  it('should return 401 for unauthenticated requests', async () => {
    // Mock no session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: null }
        })
      }
    })

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'Test product',
        goal: 'Test goal'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(401)
    expect(data.error).toBe('Unauthorized')
  })

  it('should validate required fields', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'Test product'
        // Missing goal
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Validation Error')
    expect(data.message).toContain('productOrOffer and goal are required')
  })

  it('should validate field lengths', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    const longString = 'a'.repeat(501)
    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: longString,
        goal: 'Test goal'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Validation Error')
    expect(data.message).toContain('productOrOffer must be less than 500 characters')
  })

  it('should handle AI service errors', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    // Mock AI service error
    mockAiService.generateEmailContent.mockRejectedValue(
      new Error('Perplexity API error: 429 Too Many Requests')
    )

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'Test product',
        goal: 'Test goal'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(502)
    expect(data.error).toBe('External Service Error')
    expect(data.message).toBe('AI service is temporarily unavailable')
  })

  it('should handle configuration errors', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    // Mock configuration error
    mockAiService.generateEmailContent.mockRejectedValue(
      new Error('PERPLEXITY_API_KEY environment variable is required')
    )

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'Test product',
        goal: 'Test goal'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Configuration Error')
    expect(data.message).toBe('AI service is not properly configured')
  })

  it('should handle unexpected errors', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    // Mock unexpected error
    mockAiService.generateEmailContent.mockRejectedValue(
      new Error('Unexpected error')
    )

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: JSON.stringify({
        productOrOffer: 'Test product',
        goal: 'Test goal'
      })
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal Server Error')
    expect(data.message).toBe('An unexpected error occurred')
  })

  it('should handle malformed JSON', async () => {
    // Mock authenticated session
    mockCreateRouteHandlerClient.mockReturnValue({
      auth: {
        getSession: () => Promise.resolve({
          data: { session: { user: { id: 'user-123' } } }
        })
      }
    })

    const request = new NextRequest('http://localhost:3000/api/ai/generate', {
      method: 'POST',
      body: 'invalid json'
    })

    const response = await POST(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal Server Error')
  })
})