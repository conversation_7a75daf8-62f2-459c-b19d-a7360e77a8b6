import { NextRequest, NextResponse } from 'next/server'
import { campaignService } from '@/lib/campaigns'
import { authService } from '@/lib/auth'
import { sanitizeText, sanitizeHtml, sanitizedTextSchema, sanitizedHtmlSchema } from '@/lib/security'
import { logAuditEvent, AuditAction, ResourceType, getRequestMetadata } from '@/lib/audit'
import { z } from 'zod'
import type { CreateCampaignData, CampaignFilters } from '@/types/database'

const createCampaignSchema = z.object({
  name: sanitizedTextSchema.min(1).max(255),
  subject: sanitizedTextSchema.min(1).max(255),
  html_body: sanitizedHtmlSchema.min(1),
  text_body: sanitizedTextSchema.optional(),
  scheduled_at: z.string().datetime().optional()
})

// GET /api/campaigns - Get campaigns with optional filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const filters: CampaignFilters = {}
    
    if (searchParams.get('status')) {
      const status = searchParams.get('status') as 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
      if (['draft', 'scheduled', 'sending', 'sent', 'failed'].includes(status)) {
        filters.status = status
      }
    }
    
    if (searchParams.get('search')) {
      filters.search = searchParams.get('search')!
    }
    
    if (searchParams.get('limit')) {
      const limit = parseInt(searchParams.get('limit')!)
      if (!isNaN(limit) && limit > 0 && limit <= 100) {
        filters.limit = limit
      }
    }
    
    if (searchParams.get('offset')) {
      const offset = parseInt(searchParams.get('offset')!)
      if (!isNaN(offset) && offset >= 0) {
        filters.offset = offset
      }
    }

    // Get campaigns
    const result = await campaignService.getCampaigns(authResult.user.id, filters)
    
    if (result.error) {
      return NextResponse.json(
        { error: 'Database Error', message: result.error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      campaigns: result.data,
      count: result.count,
      total: result.count
    })
  } catch (error) {
    console.error('Error fetching campaigns:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to fetch campaigns' },
      { status: 500 }
    )
  }
}

// POST /api/campaigns - Create a new campaign
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createCampaignSchema.parse(body)

    // Prepare campaign data
    const campaignData: CreateCampaignData = {
      user_id: authResult.user.id,
      ...validatedData
    }

    // Create campaign
    const result = await campaignService.createCampaign(campaignData)
    
    if (result.error) {
      return NextResponse.json(
        { error: 'Validation Error', message: result.error.message },
        { status: 400 }
      )
    }

    // Log audit event
    const metadata = getRequestMetadata(request)
    await logAuditEvent({
      user_id: authResult.user.id,
      action: AuditAction.CAMPAIGN_CREATED,
      resource_type: ResourceType.CAMPAIGN,
      resource_id: result.data.id,
      details: {
        campaign_name: validatedData.name,
        has_schedule: !!validatedData.scheduled_at
      },
      ...metadata
    })

    return NextResponse.json(
      { campaign: result.data },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating campaign:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to create campaign' },
      { status: 500 }
    )
  }
}