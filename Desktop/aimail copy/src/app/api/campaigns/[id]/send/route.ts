import { NextRequest, NextResponse } from 'next/server'
import { campaignService } from '@/lib/campaigns'
import { contactService } from '@/lib/contacts'
import { authService } from '@/lib/auth'
import { emailService } from '@/lib/email'
import { supabase } from '@/lib/supabase'
import { getSuppressionList } from '@/lib/email-webhooks'
import { generateTrackedEmailHtml } from '@/lib/tracking'

interface SendCampaignRequest {
  sendNow?: boolean
  scheduledAt?: string
}

interface SendResult {
  campaignId: string
  totalContacts: number
  totalSent: number
  totalFailed: number
  errors: string[]
}

// POST /api/campaigns/[id]/send - Send a campaign
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id
    const body: SendCampaignRequest = await request.json()

    // Get campaign
    const campaignResult = await campaignService.getCampaignById(campaignId, authResult.user.id)
    if (campaignResult.error || !campaignResult.data) {
      return NextResponse.json(
        { error: 'Campaign Error', message: 'Campaign not found' },
        { status: 404 }
      )
    }

    const campaign = campaignResult.data

    // Check if campaign is in draft status
    if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
      return NextResponse.json(
        { error: 'Campaign Error', message: 'Campaign cannot be sent in current status' },
        { status: 400 }
      )
    }

    // Handle scheduling
    if (!body.sendNow && body.scheduledAt) {
      const scheduledDate = new Date(body.scheduledAt)
      if (scheduledDate <= new Date()) {
        return NextResponse.json(
          { error: 'Validation Error', message: 'Scheduled time must be in the future' },
          { status: 400 }
        )
      }

      // Update campaign status to scheduled
      await campaignService.updateCampaign({
        id: campaignId,
        status: 'scheduled',
        scheduled_at: body.scheduledAt,
      }, authResult.user.id)

      return NextResponse.json({
        success: true,
        message: 'Campaign scheduled successfully',
        scheduledAt: body.scheduledAt,
      })
    }

    // Send campaign immediately
    const sendResult = await sendCampaignNow(campaignId, authResult.user.id)

    return NextResponse.json({
      success: true,
      message: 'Campaign sent successfully',
      result: sendResult,
    })

  } catch (error) {
    console.error('Error sending campaign:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to send campaign' },
      { status: 500 }
    )
  }
}

async function sendCampaignNow(campaignId: string, userId: string): Promise<SendResult> {
  const errors: string[] = []

  try {
    // Update campaign status to sending
    await campaignService.updateCampaign({
      id: campaignId,
      status: 'sending',
    }, userId)

    // Get campaign details
    const campaignResult = await campaignService.getCampaignById(campaignId, userId)
    if (campaignResult.error || !campaignResult.data) {
      throw new Error('Campaign not found')
    }
    const campaign = campaignResult.data

    // Get active contacts (excluding suppressed ones)
    const contactsResult = await contactService.getActiveContacts(userId)
    if (contactsResult.error || !contactsResult.data) {
      throw new Error('Failed to fetch contacts')
    }

    const activeContacts = contactsResult.data
    if (activeContacts.length === 0) {
      await campaignService.updateCampaign({
        id: campaignId,
        status: 'failed',
      }, userId)
      
      return {
        campaignId,
        totalContacts: 0,
        totalSent: 0,
        totalFailed: 0,
        errors: ['No active contacts found'],
      }
    }

    // Filter out suppressed contacts
    const suppressionList = await getSuppressionList(userId)
    const suppressedEmails = new Set(suppressionList.map(item => item.email.toLowerCase()))
    const eligibleContacts = activeContacts.filter(
      contact => !suppressedEmails.has(contact.email.toLowerCase())
    )

    if (eligibleContacts.length === 0) {
      await campaignService.updateCampaign({
        id: campaignId,
        status: 'failed',
      }, userId)
      
      return {
        campaignId,
        totalContacts: activeContacts.length,
        totalSent: 0,
        totalFailed: 0,
        errors: ['All contacts are suppressed'],
      }
    }

    // Prepare email template
    const emailTemplate = {
      subject: campaign.subject,
      htmlBody: campaign.html_body,
      textBody: campaign.text_body || undefined,
    }

    // Convert contacts to email recipients
    const recipients = eligibleContacts.map(contact => ({
      email: contact.email,
      name: contact.name || undefined,
    }))

    // Send emails in batches
    let totalSent = 0
    let totalFailed = 0
    const batchSize = 50
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'

    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize)
      
      // Process each email in the batch
      for (const recipient of batch) {
        try {
          // Create send record
          const sendRecord = await createSendRecord(campaignId, recipient.email, userId)
          
          // Add tracking to email
          const trackedHtmlBody = generateTrackedEmailHtml(
            emailTemplate.htmlBody,
            baseUrl,
            sendRecord.id
          )

          // Render template with personalization
          const personalizedTemplate = emailService.renderTemplate({
            ...emailTemplate,
            htmlBody: trackedHtmlBody,
          }, {
            name: recipient.name || recipient.email.split('@')[0],
            email: recipient.email,
          })

          // Send email with user ID for suppression checking
          const sendResult = await emailService.sendEmail(recipient, personalizedTemplate, undefined, userId)

          if (sendResult.success) {
            // Update send record with success
            await updateSendRecord(sendRecord.id, 'sent', sendResult.messageId)
            totalSent++
          } else {
            // Update send record with failure
            await updateSendRecord(sendRecord.id, 'failed', undefined, sendResult.error)
            totalFailed++
            errors.push(`Failed to send to ${recipient.email}: ${sendResult.error}`)
          }
        } catch (error) {
          totalFailed++
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          errors.push(`Failed to send to ${recipient.email}: ${errorMessage}`)
        }
      }

      // Add delay between batches to respect rate limits
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // Update campaign status
    const finalStatus = totalFailed === 0 ? 'sent' : (totalSent === 0 ? 'failed' : 'sent')
    await campaignService.updateCampaign({
      id: campaignId,
      status: finalStatus,
      sent_at: new Date().toISOString(),
    }, userId)

    return {
      campaignId,
      totalContacts: eligibleContacts.length,
      totalSent,
      totalFailed,
      errors: errors.slice(0, 10), // Limit error messages
    }

  } catch (error) {
    // Update campaign status to failed
    await campaignService.updateCampaign({
      id: campaignId,
      status: 'failed',
    }, userId)

    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    errors.push(errorMessage)

    return {
      campaignId,
      totalContacts: 0,
      totalSent: 0,
      totalFailed: 0,
      errors,
    }
  }
}



async function createSendRecord(campaignId: string, email: string, userId: string) {
  // Get contact ID
  const { data: contact } = await supabase
    .from('contacts')
    .select('id')
    .eq('user_id', userId)
    .eq('email', email)
    .single()

  if (!contact) {
    throw new Error(`Contact not found for email: ${email}`)
  }

  // Create send record
  const { data, error } = await supabase
    .from('sends')
    .insert({
      campaign_id: campaignId,
      contact_id: contact.id,
      status: 'pending',
    })
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to create send record: ${error.message}`)
  }

  return data
}

async function updateSendRecord(
  sendId: string,
  status: 'sent' | 'failed' | 'bounced',
  sesMessageId?: string,
  errorMessage?: string
) {
  const updateData: any = {
    status,
    sent_at: new Date().toISOString(),
  }

  if (sesMessageId) {
    updateData.ses_message_id = sesMessageId
  }

  if (errorMessage) {
    updateData.error_message = errorMessage
  }

  const { error } = await supabase
    .from('sends')
    .update(updateData)
    .eq('id', sendId)

  if (error) {
    console.error('Error updating send record:', error)
  }
}