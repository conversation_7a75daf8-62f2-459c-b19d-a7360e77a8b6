import { NextRequest } from 'next/server'
import { POST } from '../route'

// Mock dependencies
jest.mock('@/lib/campaigns')
jest.mock('@/lib/contacts')
jest.mock('@/lib/auth')
jest.mock('@/lib/email')
jest.mock('@/lib/supabase')

import { campaignService } from '@/lib/campaigns'
import { contactService } from '@/lib/contacts'
import { authService } from '@/lib/auth'
import { emailService } from '@/lib/email'
import { supabase } from '@/lib/supabase'

const mockCampaignService = campaignService as jest.Mocked<typeof campaignService>
const mockContactService = contactService as jest.Mocked<typeof contactService>
const mockAuthService = authService as jest.Mocked<typeof authService>
const mockEmailService = emailService as jest.Mocked<typeof emailService>
const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('/api/campaigns/[id]/send', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
  }

  const mockCampaign = {
    id: 'campaign-123',
    user_id: 'user-123',
    name: 'Test Campaign',
    subject: 'Test Subject',
    html_body: '<h1>Hello {{name}}!</h1>',
    text_body: 'Hello {{name}}!',
    status: 'draft' as const,
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  }

  const mockContacts = [
    {
      id: 'contact-1',
      user_id: 'user-123',
      email: '<EMAIL>',
      name: 'User 1',
      status: 'active' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
    {
      id: 'contact-2',
      user_id: 'user-123',
      email: '<EMAIL>',
      name: 'User 2',
      status: 'active' as const,
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    },
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock auth
    mockAuthService.getCurrentUser.mockResolvedValue({
      user: mockUser,
      error: null,
    })

    // Mock campaign service
    mockCampaignService.getCampaignById.mockResolvedValue({
      data: mockCampaign,
      error: null,
    })

    mockCampaignService.updateCampaign.mockResolvedValue({
      data: mockCampaign,
      error: null,
    })

    // Mock contact service
    mockContactService.getActiveContacts.mockResolvedValue({
      data: mockContacts,
      error: null,
      count: mockContacts.length,
    })

    // Mock email service
    mockEmailService.generateEmailWithTracking.mockReturnValue('<h1>Hello User!</h1><img src="tracking-pixel" />')
    mockEmailService.renderTemplate.mockReturnValue({
      subject: 'Test Subject',
      htmlBody: '<h1>Hello User!</h1>',
      textBody: 'Hello User!',
    })
    mockEmailService.sendEmail.mockResolvedValue({
      messageId: 'ses-message-123',
      success: true,
    })

    // Mock Supabase
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: { id: 'contact-1' },
        error: null,
      }),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
    } as any)
  })

  describe('POST', () => {
    it('should send campaign immediately when sendNow is true', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Campaign sent successfully')
      expect(mockCampaignService.updateCampaign).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'sending' }),
        'user-123'
      )
    })

    it('should schedule campaign when scheduledAt is provided', async () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ scheduledAt: futureDate }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.message).toBe('Campaign scheduled successfully')
      expect(mockCampaignService.updateCampaign).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'scheduled',
          scheduled_at: futureDate,
        }),
        'user-123'
      )
    })

    it('should return error for past scheduled date', async () => {
      const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ scheduledAt: pastDate }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Validation Error')
      expect(data.message).toBe('Scheduled time must be in the future')
    })

    it('should return error when campaign is not found', async () => {
      mockCampaignService.getCampaignById.mockResolvedValue({
        data: null,
        error: new Error('Campaign not found'),
      })

      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Campaign Error')
      expect(data.message).toBe('Campaign not found')
    })

    it('should return error when campaign cannot be sent', async () => {
      mockCampaignService.getCampaignById.mockResolvedValue({
        data: { ...mockCampaign, status: 'sent' },
        error: null,
      })

      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Campaign Error')
      expect(data.message).toBe('Campaign cannot be sent in current status')
    })

    it('should return error when user is not authenticated', async () => {
      mockAuthService.getCurrentUser.mockResolvedValue({
        user: null,
        error: new Error('Not authenticated'),
      })

      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
      expect(data.message).toBe('Authentication required')
    })

    it('should handle case when no active contacts exist', async () => {
      mockContactService.getActiveContacts.mockResolvedValue({
        data: [],
        error: null,
        count: 0,
      })

      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.totalSent).toBe(0)
      expect(data.result.errors).toContain('No active contacts found')
    })

    it('should handle email sending failures gracefully', async () => {
      mockEmailService.sendEmail
        .mockResolvedValueOnce({
          messageId: 'ses-message-123',
          success: true,
        })
        .mockResolvedValueOnce({
          messageId: '',
          success: false,
          error: 'SES Error',
        })

      const request = new NextRequest('http://localhost:3000/api/campaigns/campaign-123/send', {
        method: 'POST',
        body: JSON.stringify({ sendNow: true }),
      })

      const response = await POST(request, { params: { id: 'campaign-123' } })
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.totalSent).toBe(1)
      expect(data.result.totalFailed).toBe(1)
      expect(data.result.errors.length).toBeGreaterThan(0)
    })
  })
})