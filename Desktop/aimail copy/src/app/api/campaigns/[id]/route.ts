import { NextRequest, NextResponse } from 'next/server'
import { campaignService } from '@/lib/campaigns'
import { authService } from '@/lib/auth'
import type { UpdateCampaignData } from '@/types/database'

// GET /api/campaigns/[id] - Get a specific campaign
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Get campaign
    const result = await campaignService.getCampaignById(campaignId, authResult.user.id)
    
    if (result.error) {
      const status = result.error.message.includes('not found') ? 404 : 500
      return NextResponse.json(
        { error: 'Campaign Error', message: result.error.message },
        { status }
      )
    }

    return NextResponse.json({ campaign: result.data })
  } catch (error) {
    console.error('Error fetching campaign:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to fetch campaign' },
      { status: 500 }
    )
  }
}

// PUT /api/campaigns/[id] - Update a specific campaign
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Parse request body
    const body = await request.json()
    
    // Prepare update data
    const updateData: UpdateCampaignData = {
      id: campaignId,
      ...body
    }

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateCampaignData] === undefined) {
        delete updateData[key as keyof UpdateCampaignData]
      }
    })

    // Update campaign
    const result = await campaignService.updateCampaign(updateData, authResult.user.id)
    
    if (result.error) {
      const status = result.error.message.includes('not found') ? 404 : 400
      return NextResponse.json(
        { error: 'Campaign Error', message: result.error.message },
        { status }
      )
    }

    return NextResponse.json({ campaign: result.data })
  } catch (error) {
    console.error('Error updating campaign:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to update campaign' },
      { status: 500 }
    )
  }
}

// DELETE /api/campaigns/[id] - Delete a specific campaign
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Delete campaign
    const result = await campaignService.deleteCampaign(campaignId, authResult.user.id)
    
    if (result.error) {
      const status = result.error.message.includes('not found') ? 404 : 400
      return NextResponse.json(
        { error: 'Campaign Error', message: result.error.message },
        { status }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting campaign:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to delete campaign' },
      { status: 500 }
    )
  }
}