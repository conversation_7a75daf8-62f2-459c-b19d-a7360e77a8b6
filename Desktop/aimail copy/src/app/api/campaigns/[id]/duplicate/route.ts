import { NextRequest, NextResponse } from 'next/server'
import { campaignService } from '@/lib/campaigns'
import { authService } from '@/lib/auth'

// POST /api/campaigns/[id]/duplicate - Duplicate a campaign
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Parse request body for optional new name
    const body = await request.json().catch(() => ({}))
    const newName = body.name

    // Duplicate campaign
    const result = await campaignService.duplicateCampaign(campaignId, authResult.user.id, newName)
    
    if (result.error) {
      const status = result.error.message.includes('not found') ? 404 : 400
      return NextResponse.json(
        { error: 'Campaign Error', message: result.error.message },
        { status }
      )
    }

    return NextResponse.json(
      { campaign: result.data },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error duplicating campaign:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to duplicate campaign' },
      { status: 500 }
    )
  }
}