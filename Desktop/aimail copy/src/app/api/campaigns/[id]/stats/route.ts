import { NextRequest, NextResponse } from 'next/server'
import { campaignService } from '@/lib/campaigns'
import { authService } from '@/lib/auth'

// GET /api/campaigns/[id]/stats - Get campaign statistics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Get campaign statistics
    const result = await campaignService.getCampaignStats(campaignId, authResult.user.id)
    
    if (result.error) {
      const status = result.error.message.includes('not found') ? 404 : 500
      return NextResponse.json(
        { error: 'Campaign Error', message: result.error.message },
        { status }
      )
    }

    return NextResponse.json({ stats: result.data })
  } catch (error) {
    console.error('Error fetching campaign stats:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to fetch campaign statistics' },
      { status: 500 }
    )
  }
}