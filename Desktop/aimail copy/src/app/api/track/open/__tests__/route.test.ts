import { GET } from '../route'
import { NextRequest } from 'next/server'
import { logTrackingEvent, validateSendId } from '@/lib/tracking'

// Mock the tracking utilities
jest.mock('@/lib/tracking', () => ({
  logTrackingEvent: jest.fn(),
  validateSendId: jest.fn(),
}))

describe('/api/track/open', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return tracking pixel for valid send_id', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const request = new NextRequest('https://example.com/api/track/open?send_id=test-send-id', {
      headers: {
        'user-agent': 'Mozilla/5.0',
        'x-forwarded-for': '***********',
      },
    })

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(response.headers.get('content-type')).toBe('image/png')
    expect(response.headers.get('cache-control')).toBe('no-cache, no-store, must-revalidate')
    
    expect(validateSendId).toHaveBeenCalledWith('test-send-id')
    expect(logTrackingEvent).toHaveBeenCalledWith('test-send-id', 'open', {
      userAgent: 'Mozilla/5.0',
      referer: '',
      ip: '***********',
      timestamp: expect.any(String),
    })
  })

  it('should return tracking pixel even without send_id', async () => {
    const request = new NextRequest('https://example.com/api/track/open')

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(response.headers.get('content-type')).toBe('image/png')
    expect(validateSendId).not.toHaveBeenCalled()
    expect(logTrackingEvent).not.toHaveBeenCalled()
  })

  it('should return tracking pixel for invalid send_id', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(false)

    const request = new NextRequest('https://example.com/api/track/open?send_id=invalid-id')

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(response.headers.get('content-type')).toBe('image/png')
    expect(validateSendId).toHaveBeenCalledWith('invalid-id')
    expect(logTrackingEvent).not.toHaveBeenCalled()
  })

  it('should handle multiple IP addresses in x-forwarded-for', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const request = new NextRequest('https://example.com/api/track/open?send_id=test-send-id', {
      headers: {
        'x-forwarded-for': '***********, ********, **********',
      },
    })

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(logTrackingEvent).toHaveBeenCalledWith('test-send-id', 'open', {
      userAgent: '',
      referer: '',
      ip: '***********', // Should take the first IP
      timestamp: expect.any(String),
    })
  })

  it('should use x-real-ip if x-forwarded-for is not available', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const request = new NextRequest('https://example.com/api/track/open?send_id=test-send-id', {
      headers: {
        'x-real-ip': '***********',
      },
    })

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(logTrackingEvent).toHaveBeenCalledWith('test-send-id', 'open', {
      userAgent: '',
      referer: '',
      ip: '***********',
      timestamp: expect.any(String),
    })
  })

  it('should handle tracking errors gracefully', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockRejectedValue(new Error('Database error'))

    const request = new NextRequest('https://example.com/api/track/open?send_id=test-send-id')

    const response = await GET(request)

    // Should still return tracking pixel even if logging fails
    expect(response.status).toBe(200)
    expect(response.headers.get('content-type')).toBe('image/png')
  })

  it('should include referer in metadata when available', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const request = new NextRequest('https://example.com/api/track/open?send_id=test-send-id', {
      headers: {
        'referer': 'https://gmail.com',
      },
    })

    const response = await GET(request)

    expect(response.status).toBe(200)
    expect(logTrackingEvent).toHaveBeenCalledWith('test-send-id', 'open', {
      userAgent: '',
      referer: 'https://gmail.com',
      ip: 'unknown',
      timestamp: expect.any(String),
    })
  })
})