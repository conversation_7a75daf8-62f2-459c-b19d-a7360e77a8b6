import { NextRequest, NextResponse } from 'next/server'
import { logTrackingEvent, validateSendId } from '@/lib/tracking'

// 1x1 transparent pixel image data
const TRACKING_PIXEL = Buffer.from(
  'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
  'base64'
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sendId = searchParams.get('send_id')

    if (!sendId) {
      // Return tracking pixel even if send_id is missing to avoid broken images
      return new NextResponse(TRACKING_PIXEL, {
        status: 200,
        headers: {
          'Content-Type': 'image/png',
          'Content-Length': TRACKING_PIXEL.length.toString(),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      })
    }

    // Validate send ID exists
    const isValidSend = await validateSendId(sendId)
    if (!isValidSend) {
      // Return tracking pixel even if send_id is invalid to avoid broken images
      return new NextResponse(TRACKING_PIXEL, {
        status: 200,
        headers: {
          'Content-Type': 'image/png',
          'Content-Length': TRACKING_PIXEL.length.toString(),
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      })
    }

    // Extract metadata from request
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'

    const metadata = {
      userAgent,
      referer,
      ip: ip.split(',')[0].trim(), // Take first IP if multiple
      timestamp: new Date().toISOString(),
    }

    // Log the open event
    await logTrackingEvent(sendId, 'open', metadata)

    // Return the tracking pixel
    return new NextResponse(TRACKING_PIXEL, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
        'Content-Length': TRACKING_PIXEL.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })
  } catch (error) {
    console.error('Error tracking email open:', error)
    
    // Always return tracking pixel to avoid broken images
    return new NextResponse(TRACKING_PIXEL, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
        'Content-Length': TRACKING_PIXEL.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })
  }
}