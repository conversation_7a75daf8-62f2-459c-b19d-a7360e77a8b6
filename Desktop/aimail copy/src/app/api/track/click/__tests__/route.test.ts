import { GET } from '../route'
import { NextRequest } from 'next/server'
import { logTrackingEvent, validateSendId } from '@/lib/tracking'

// Mock the tracking utilities
jest.mock('@/lib/tracking', () => ({
  logTrackingEvent: jest.fn(),
  validateSendId: jest.fn(),
}))

describe('/api/track/click', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should track click and redirect for valid parameters', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const originalUrl = 'https://example.com/page'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`,
      {
        headers: {
          'user-agent': 'Mozilla/5.0',
          'x-forwarded-for': '***********',
        },
      }
    )

    const response = await GET(request)

    expect(response.status).toBe(307) // Temporary redirect
    expect(response.headers.get('location')).toBe(originalUrl)
    
    expect(validateSendId).toHaveBeenCalledWith(sendId)
    expect(logTrackingEvent).toHaveBeenCalledWith(sendId, 'click', {
      originalUrl,
      userAgent: 'Mozilla/5.0',
      referer: '',
      ip: '***********',
      timestamp: expect.any(String),
    })
  })

  it('should return error for missing send_id', async () => {
    const request = new NextRequest('https://example.com/api/track/click?url=https://example.com')

    const response = await GET(request)

    expect(response.status).toBe(400)
    const data = await response.json()
    expect(data.error).toBe('Missing required parameters')
    
    expect(validateSendId).not.toHaveBeenCalled()
    expect(logTrackingEvent).not.toHaveBeenCalled()
  })

  it('should return error for missing url', async () => {
    const request = new NextRequest('https://example.com/api/track/click?send_id=test-send-id')

    const response = await GET(request)

    expect(response.status).toBe(400)
    const data = await response.json()
    expect(data.error).toBe('Missing required parameters')
    
    expect(validateSendId).not.toHaveBeenCalled()
    expect(logTrackingEvent).not.toHaveBeenCalled()
  })

  it('should redirect even for invalid send_id', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(false)

    const originalUrl = 'https://example.com/page'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=invalid-id&url=${encodeURIComponent(originalUrl)}`
    )

    const response = await GET(request)

    expect(response.status).toBe(307)
    expect(response.headers.get('location')).toBe(originalUrl)
    
    expect(validateSendId).toHaveBeenCalledWith('invalid-id')
    expect(logTrackingEvent).not.toHaveBeenCalled()
  })

  it('should handle URLs with special characters', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const originalUrl = 'https://example.com/page?param=value&other=test#section'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`
    )

    const response = await GET(request)

    expect(response.status).toBe(307)
    expect(response.headers.get('location')).toBe(originalUrl)
    
    expect(logTrackingEvent).toHaveBeenCalledWith(sendId, 'click', {
      originalUrl,
      userAgent: '',
      referer: '',
      ip: 'unknown',
      timestamp: expect.any(String),
    })
  })

  it('should handle multiple IP addresses in x-forwarded-for', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const originalUrl = 'https://example.com/page'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`,
      {
        headers: {
          'x-forwarded-for': '***********, ********, **********',
        },
      }
    )

    const response = await GET(request)

    expect(response.status).toBe(307)
    expect(logTrackingEvent).toHaveBeenCalledWith(sendId, 'click', {
      originalUrl,
      userAgent: '',
      referer: '',
      ip: '***********', // Should take the first IP
      timestamp: expect.any(String),
    })
  })

  it('should handle tracking errors gracefully', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockRejectedValue(new Error('Database error'))

    const originalUrl = 'https://example.com/page'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`
    )

    const response = await GET(request)

    // Should still redirect even if tracking fails
    expect(response.status).toBe(307)
    expect(response.headers.get('location')).toBe(originalUrl)
  })

  it('should handle general errors and try to redirect', async () => {
    ;(validateSendId as jest.Mock).mockRejectedValue(new Error('Database connection error'))

    const originalUrl = 'https://example.com/page'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`
    )

    const response = await GET(request)

    // Should still redirect even if validation fails
    expect(response.status).toBe(307)
    expect(response.headers.get('location')).toBe(originalUrl)
  })

  it('should return error if no URL available during error handling', async () => {
    const request = new NextRequest('https://example.com/api/track/click?send_id=test-send-id')

    const response = await GET(request)

    // Should return 400 because URL parameter is missing
    expect(response.status).toBe(400)
    const data = await response.json()
    expect(data.error).toBe('Missing required parameters')
  })

  it('should include referer in metadata when available', async () => {
    ;(validateSendId as jest.Mock).mockResolvedValue(true)
    ;(logTrackingEvent as jest.Mock).mockResolvedValue(undefined)

    const originalUrl = 'https://example.com/page'
    const sendId = 'test-send-id'
    const request = new NextRequest(
      `https://example.com/api/track/click?send_id=${sendId}&url=${encodeURIComponent(originalUrl)}`,
      {
        headers: {
          'referer': 'https://gmail.com',
        },
      }
    )

    const response = await GET(request)

    expect(response.status).toBe(307)
    expect(logTrackingEvent).toHaveBeenCalledWith(sendId, 'click', {
      originalUrl,
      userAgent: '',
      referer: 'https://gmail.com',
      ip: 'unknown',
      timestamp: expect.any(String),
    })
  })
})