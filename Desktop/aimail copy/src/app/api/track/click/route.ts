import { NextRequest, NextResponse } from 'next/server'
import { logTrackingEvent, validateSendId } from '@/lib/tracking'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sendId = searchParams.get('send_id')
    const originalUrl = searchParams.get('url')

    if (!sendId || !originalUrl) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Validate send ID exists
    const isValidSend = await validateSendId(sendId)
    if (!isValidSend) {
      // Redirect to original URL even if send_id is invalid
      return NextResponse.redirect(originalUrl)
    }

    // Extract metadata from request
    const userAgent = request.headers.get('user-agent') || ''
    const referer = request.headers.get('referer') || ''
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown'

    const metadata = {
      originalUrl,
      userAgent,
      referer,
      ip: ip.split(',')[0].trim(), // Take first IP if multiple
      timestamp: new Date().toISOString(),
    }

    // Log the click event
    await logTrackingEvent(sendId, 'click', metadata)

    // Redirect to the original URL
    return NextResponse.redirect(originalUrl)
  } catch (error) {
    console.error('Error tracking email click:', error)
    
    // Try to redirect to original URL even if tracking fails
    const { searchParams } = new URL(request.url)
    const originalUrl = searchParams.get('url')
    
    if (originalUrl) {
      return NextResponse.redirect(originalUrl)
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}