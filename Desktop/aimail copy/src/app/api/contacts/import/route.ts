import { NextRequest, NextResponse } from 'next/server'
import { contactService } from '@/lib/contacts'
import { getUser } from '@/lib/auth'
import type { CreateContactData } from '@/types/database'

// CSV parsing utility
function parseCSV(csvText: string): { headers: string[]; rows: string[][] } {
  const lines = csvText.trim().split('\n')
  if (lines.length === 0) {
    throw new Error('CSV file is empty')
  }

  // Parse headers
  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  
  // Parse rows
  const rows: string[][] = []
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (line) {
      // Simple CSV parsing - handles basic cases
      const row = line.split(',').map(cell => cell.trim().replace(/"/g, ''))
      rows.push(row)
    }
  }

  return { headers, rows }
}

// Convert CSV rows to contact data
function csvRowsToContacts(
  headers: string[],
  rows: string[][],
  userId: string
): { contacts: CreateContactData[]; errors: string[] } {
  const contacts: CreateContactData[] = []
  const errors: string[] = []

  // Find email column index
  const emailIndex = headers.findIndex(h => 
    h.toLowerCase().includes('email') || h.toLowerCase() === 'e-mail'
  )
  
  if (emailIndex === -1) {
    errors.push('No email column found. Expected column names: email, e-mail')
    return { contacts, errors }
  }

  // Find name column index (optional)
  const nameIndex = headers.findIndex(h => 
    h.toLowerCase().includes('name') || 
    h.toLowerCase().includes('first') ||
    h.toLowerCase().includes('full')
  )

  // Find status column index (optional)
  const statusIndex = headers.findIndex(h => 
    h.toLowerCase().includes('status') || h.toLowerCase().includes('state')
  )

  // Process each row
  rows.forEach((row, index) => {
    const rowNumber = index + 2 // +2 because index starts at 0 and we skip header row

    if (row.length < headers.length) {
      errors.push(`Row ${rowNumber}: Insufficient columns`)
      return
    }

    const email = row[emailIndex]?.trim()
    if (!email) {
      errors.push(`Row ${rowNumber}: Email is required`)
      return
    }

    const name = nameIndex >= 0 ? row[nameIndex]?.trim() : undefined
    const status = statusIndex >= 0 ? row[statusIndex]?.trim().toLowerCase() : 'active'

    // Validate status
    let validStatus: 'active' | 'unsubscribed' | 'bounced' = 'active'
    if (status && ['active', 'unsubscribed', 'bounced'].includes(status)) {
      validStatus = status as 'active' | 'unsubscribed' | 'bounced'
    }

    contacts.push({
      user_id: userId,
      email,
      name: name || undefined,
      status: validStatus
    })
  })

  return { contacts, errors }
}

// POST /api/contacts/import - Import contacts from CSV
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const user = await getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'CSV file is required' },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv') && file.type !== 'text/csv') {
      return NextResponse.json(
        { error: 'Validation Error', message: 'File must be a CSV file' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'File size must be less than 5MB' },
        { status: 400 }
      )
    }

    // Read file content
    const csvText = await file.text()
    
    // Parse CSV
    let headers: string[]
    let rows: string[][]
    
    try {
      const parsed = parseCSV(csvText)
      headers = parsed.headers
      rows = parsed.rows
    } catch (error) {
      return NextResponse.json(
        { error: 'Validation Error', message: `CSV parsing failed: ${(error as Error).message}` },
        { status: 400 }
      )
    }

    // Validate row count
    if (rows.length === 0) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'CSV file contains no data rows' },
        { status: 400 }
      )
    }

    if (rows.length > 10000) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'CSV file contains too many rows (max 10,000)' },
        { status: 400 }
      )
    }

    // Convert CSV to contact data
    const { contacts, errors: parseErrors } = csvRowsToContacts(headers, rows, user.id)
    
    if (parseErrors.length > 0 && contacts.length === 0) {
      return NextResponse.json(
        { 
          error: 'Validation Error', 
          message: 'CSV parsing failed',
          details: parseErrors
        },
        { status: 400 }
      )
    }

    // Bulk create contacts
    const result = await contactService.bulkCreateContacts(contacts, user.id)

    // Prepare response
    const response = {
      success: true,
      summary: {
        total_processed: contacts.length,
        successful: result.successful.length,
        failed: result.failed.length,
        duplicates: result.duplicates.length
      },
      data: {
        successful: result.successful,
        failed: result.failed,
        duplicates: result.duplicates
      },
      parsing_errors: parseErrors
    }

    return NextResponse.json(response, { status: 201 })

  } catch (error) {
    console.error('POST /api/contacts/import error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}