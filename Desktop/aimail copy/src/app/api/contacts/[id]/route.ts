import { NextRequest, NextResponse } from 'next/server'
import { contactService } from '@/lib/contacts'
import { getUser } from '@/lib/auth'
import type { UpdateContactData } from '@/types/database'

// GET /api/contacts/[id] - Get a specific contact
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user
    const user = await getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const contactId = params.id
    if (!contactId) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Contact ID is required' },
        { status: 400 }
      )
    }

    // Get contact
    const result = await contactService.getContactById(contactId, user.id)

    if (result.error) {
      if (result.error.message.includes('not found')) {
        return NextResponse.json(
          { error: 'Not Found', message: 'Contact not found' },
          { status: 404 }
        )
      }

      return NextResponse.json(
        { error: 'Database Error', message: result.error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: result.data,
      success: true
    })

  } catch (error) {
    console.error('GET /api/contacts/[id] error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// PUT /api/contacts/[id] - Update a specific contact
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user
    const user = await getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const contactId = params.id
    if (!contactId) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Contact ID is required' },
        { status: 400 }
      )
    }

    // Parse request body
    const body = await request.json()
    
    // Prepare update data
    const updateData: UpdateContactData = {
      id: contactId
    }

    if (body.email !== undefined) {
      updateData.email = body.email
    }
    if (body.name !== undefined) {
      updateData.name = body.name
    }
    if (body.status !== undefined) {
      updateData.status = body.status
    }

    // Update contact
    const result = await contactService.updateContact(updateData, user.id)

    if (result.error) {
      if (result.error.message.includes('not found') || 
          result.error.message.includes('access denied')) {
        return NextResponse.json(
          { error: 'Not Found', message: 'Contact not found' },
          { status: 404 }
        )
      }

      if (result.error.message.includes('already exists') || 
          result.error.message.includes('Invalid') ||
          result.error.message.includes('must be')) {
        return NextResponse.json(
          { error: 'Validation Error', message: result.error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Database Error', message: result.error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: result.data,
      success: true
    })

  } catch (error) {
    console.error('PUT /api/contacts/[id] error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// DELETE /api/contacts/[id] - Delete a specific contact
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get authenticated user
    const user = await getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const contactId = params.id
    if (!contactId) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Contact ID is required' },
        { status: 400 }
      )
    }

    // Delete contact
    const result = await contactService.deleteContact(contactId, user.id)

    if (result.error) {
      if (result.error.message.includes('not found') || 
          result.error.message.includes('access denied')) {
        return NextResponse.json(
          { error: 'Not Found', message: 'Contact not found' },
          { status: 404 }
        )
      }

      return NextResponse.json(
        { error: 'Database Error', message: result.error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Contact deleted successfully'
    })

  } catch (error) {
    console.error('DELETE /api/contacts/[id] error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}