import { NextRequest, NextResponse } from 'next/server'
import { contactService } from '@/lib/contacts'
import { getUser } from '@/lib/auth'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthenticationError, ValidationError } from '@/lib/error-handler'
import { contactSchema } from '@/lib/validation'
import type { CreateContactData, ContactFilters } from '@/types/database'

// GET /api/contacts - Get contacts with optional filtering
async function handleGetContacts(request: NextRequest) {
  // Get authenticated user
  const user = await getUser()
  if (!user) {
    throw new AuthenticationError()
  }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters: ContactFilters = {}

    const status = searchParams.get('status')
    if (status && ['active', 'unsubscribed', 'bounced'].includes(status)) {
      filters.status = status as 'active' | 'unsubscribed' | 'bounced'
    }

    const search = searchParams.get('search')
    if (search) {
      filters.search = search
    }

    const limit = searchParams.get('limit')
    if (limit) {
      const parsedLimit = parseInt(limit, 10)
      if (!isNaN(parsedLimit) && parsedLimit > 0 && parsedLimit <= 100) {
        filters.limit = parsedLimit
      }
    }

    const offset = searchParams.get('offset')
    if (offset) {
      const parsedOffset = parseInt(offset, 10)
      if (!isNaN(parsedOffset) && parsedOffset >= 0) {
        filters.offset = parsedOffset
      }
    }

  // Get contacts
  const result = await contactService.getContacts(user.id, filters)

  if (result.error) {
    throw new Error(result.error.message)
  }

  return NextResponse.json({
    data: result.data,
    count: result.count,
    success: true
  })
}

// POST /api/contacts - Create a new contact
async function handleCreateContact(request: NextRequest) {
  // Get authenticated user
  const user = await getUser()
  if (!user) {
    throw new AuthenticationError()
  }

  // Parse and validate request body
  const body = await request.json()
  const validatedData = contactSchema.parse(body)

  // Prepare contact data
  const contactData: CreateContactData = {
    user_id: user.id,
    email: validatedData.email,
    name: validatedData.name,
    status: validatedData.status || 'active'
  }

  // Create contact
  const result = await contactService.createContact(contactData)

  if (result.error) {
    // Check if it's a validation error or duplicate
    if (result.error.message.includes('already exists')) {
      throw new ValidationError('Contact with this email already exists')
    }
    throw new Error(result.error.message)
  }

  return NextResponse.json({
    data: result.data,
    success: true
  }, { status: 201 })
}

export const GET = withErrorHandler(handleGetContacts)
export const POST = withErrorHandler(handleCreateContact)