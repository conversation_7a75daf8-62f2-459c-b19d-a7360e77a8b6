import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { getTopPerformingCampaigns } from '@/lib/analytics'

// GET /api/analytics/top-campaigns - Get top performing campaigns
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limitParam = searchParams.get('limit')
    const limit = limitParam ? parseInt(limitParam, 10) : 10

    // Validate limit parameter
    if (isNaN(limit) || limit < 1 || limit > 50) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Limit parameter must be between 1 and 50' },
        { status: 400 }
      )
    }

    // Get top performing campaigns
    const topCampaigns = await getTopPerformingCampaigns(authResult.user.id, limit)

    return NextResponse.json({
      success: true,
      data: topCampaigns,
    })
  } catch (error) {
    console.error('Error getting top performing campaigns:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to get top performing campaigns' },
      { status: 500 }
    )
  }
}