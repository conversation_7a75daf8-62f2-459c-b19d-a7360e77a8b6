import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { getTimeSeriesData } from '@/lib/analytics'

// GET /api/analytics/timeseries - Get time series data for charts
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const daysParam = searchParams.get('days')
    const days = daysParam ? parseInt(daysParam, 10) : 30

    // Validate days parameter
    if (isNaN(days) || days < 1 || days > 365) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Days parameter must be between 1 and 365' },
        { status: 400 }
      )
    }

    // Get time series data
    const timeSeriesData = await getTimeSeriesData(authResult.user.id, days)

    return NextResponse.json({
      success: true,
      data: timeSeriesData,
    })
  } catch (error) {
    console.error('Error getting time series data:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to get time series data' },
      { status: 500 }
    )
  }
}