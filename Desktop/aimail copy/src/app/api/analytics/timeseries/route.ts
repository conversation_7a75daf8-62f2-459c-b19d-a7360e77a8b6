import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { getTimeSeriesData } from '@/lib/analytics'

// GET /api/analytics/timeseries - Get time series data for charts
export async function GET(request: NextRequest) {
  try {
    // Get current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      // Return empty time series data for unauthenticated users
      return NextResponse.json({
        success: true,
        data: [],
      })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const daysParam = searchParams.get('days')
    const days = daysParam ? parseInt(daysParam, 10) : 30

    // Validate days parameter
    if (isNaN(days) || days < 1 || days > 365) {
      return NextResponse.json(
        { error: 'Validation Error', message: 'Days parameter must be between 1 and 365' },
        { status: 400 }
      )
    }

    // Get time series data
    const timeSeriesData = await getTimeSeriesData(user.id, days)

    return NextResponse.json({
      success: true,
      data: timeSeriesData,
    })
  } catch (error) {
    console.error('Error getting time series data:', error)
    return NextResponse.json({
      success: true,
      data: [],
    })
  }
}