import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { getDashboardMetrics } from '@/lib/analytics'

// GET /api/analytics/dashboard - Get dashboard overview metrics
export async function GET(request: NextRequest) {
  try {
    // Get current user session
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      // Return empty metrics for unauthenticated users
      const emptyMetrics = {
        totalCampaigns: 0,
        totalContacts: 0,
        totalSends: 0,
        totalOpens: 0,
        totalClicks: 0,
        averageOpenRate: 0,
        averageClickRate: 0,
        recentCampaigns: []
      }

      return NextResponse.json({
        success: true,
        data: emptyMetrics,
      })
    }

    // Get dashboard metrics for authenticated user
    const metrics = await getDashboardMetrics(user.id)

    return NextResponse.json({
      success: true,
      data: metrics,
    })
  } catch (error) {
    console.error('Error getting dashboard metrics:', error)

    // Return empty metrics on error
    const emptyMetrics = {
      totalCampaigns: 0,
      totalContacts: 0,
      totalSends: 0,
      totalOpens: 0,
      totalClicks: 0,
      averageOpenRate: 0,
      averageClickRate: 0,
      recentCampaigns: []
    }

    return NextResponse.json({
      success: true,
      data: emptyMetrics,
    })
  }
}