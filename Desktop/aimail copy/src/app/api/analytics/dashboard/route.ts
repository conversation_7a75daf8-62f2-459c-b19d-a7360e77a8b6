import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { getDashboardMetrics } from '@/lib/analytics'

// GET /api/analytics/dashboard - Get dashboard overview metrics
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get dashboard metrics
    const metrics = await getDashboardMetrics(authResult.user.id)

    return NextResponse.json({
      success: true,
      data: metrics,
    })
  } catch (error) {
    console.error('Error getting dashboard metrics:', error)
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to get dashboard metrics' },
      { status: 500 }
    )
  }
}