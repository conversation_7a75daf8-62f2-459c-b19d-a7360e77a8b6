import { GET } from '../route'
import { NextRequest } from 'next/server'
import { authService } from '@/lib/auth'
import { getDashboardMetrics } from '@/lib/analytics'

// Mock dependencies
jest.mock('@/lib/auth', () => ({
  authService: {
    getCurrentUser: jest.fn(),
  },
}))

jest.mock('@/lib/analytics', () => ({
  getDashboardMetrics: jest.fn(),
}))

describe('/api/analytics/dashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return dashboard metrics for authenticated user', async () => {
    const mockUser = { id: 'test-user-id', email: '<EMAIL>' }
    const mockMetrics = {
      totalCampaigns: 5,
      totalContacts: 100,
      totalSends: 500,
      totalOpens: 150,
      totalClicks: 50,
      averageOpenRate: 30,
      averageClickRate: 10,
      recentCampaigns: [],
    }

    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: mockUser })
    ;(getDashboardMetrics as jest.Mock).mockResolvedValue(mockMetrics)

    const request = new NextRequest('https://example.com/api/analytics/dashboard')
    const response = await GET(request)

    expect(response.status).toBe(200)
    const data = await response.json()
    expect(data.success).toBe(true)
    expect(data.data).toEqual(mockMetrics)

    expect(authService.getCurrentUser).toHaveBeenCalledWith(request)
    expect(getDashboardMetrics).toHaveBeenCalledWith(mockUser.id)
  })

  it('should return 401 for unauthenticated user', async () => {
    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: null })

    const request = new NextRequest('https://example.com/api/analytics/dashboard')
    const response = await GET(request)

    expect(response.status).toBe(401)
    const data = await response.json()
    expect(data.error).toBe('Unauthorized')
    expect(data.message).toBe('Authentication required')

    expect(getDashboardMetrics).not.toHaveBeenCalled()
  })

  it('should handle analytics service errors', async () => {
    const mockUser = { id: 'test-user-id', email: '<EMAIL>' }

    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: mockUser })
    ;(getDashboardMetrics as jest.Mock).mockRejectedValue(new Error('Database error'))

    const request = new NextRequest('https://example.com/api/analytics/dashboard')
    const response = await GET(request)

    expect(response.status).toBe(500)
    const data = await response.json()
    expect(data.error).toBe('Internal Server Error')
    expect(data.message).toBe('Failed to get dashboard metrics')
  })

  it('should handle authentication service errors', async () => {
    ;(authService.getCurrentUser as jest.Mock).mockRejectedValue(new Error('Auth error'))

    const request = new NextRequest('https://example.com/api/analytics/dashboard')
    const response = await GET(request)

    expect(response.status).toBe(500)
    const data = await response.json()
    expect(data.error).toBe('Internal Server Error')
  })
})