import { GET } from '../route'
import { NextRequest } from 'next/server'
import { authService } from '@/lib/auth'
import { getCampaignAnalytics } from '@/lib/analytics'

// Mock dependencies
jest.mock('@/lib/auth', () => ({
  authService: {
    getCurrentUser: jest.fn(),
  },
}))

jest.mock('@/lib/analytics', () => ({
  getCampaignAnalytics: jest.fn(),
}))

describe('/api/analytics/campaigns/[id]', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return campaign analytics for authenticated user', async () => {
    const mockUser = { id: 'test-user-id', email: '<EMAIL>' }
    const campaignId = 'test-campaign-id'
    const mockAnalytics = {
      campaignId,
      campaignName: 'Test Campaign',
      subject: 'Test Subject',
      status: 'sent',
      sentAt: '2023-01-01T00:00:00Z',
      totalSends: 100,
      opens: 30,
      clicks: 10,
      bounces: 2,
      complaints: 1,
      openRate: 30,
      clickRate: 10,
      bounceRate: 2,
      complaintRate: 1,
      uniqueOpens: 25,
      uniqueClicks: 8,
    }

    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: mockUser })
    ;(getCampaignAnalytics as jest.Mock).mockResolvedValue(mockAnalytics)

    const request = new NextRequest(`https://example.com/api/analytics/campaigns/${campaignId}`)
    const response = await GET(request, { params: { id: campaignId } })

    expect(response.status).toBe(200)
    const data = await response.json()
    expect(data.success).toBe(true)
    expect(data.data).toEqual(mockAnalytics)

    expect(authService.getCurrentUser).toHaveBeenCalledWith(request)
    expect(getCampaignAnalytics).toHaveBeenCalledWith(campaignId, mockUser.id)
  })

  it('should return 401 for unauthenticated user', async () => {
    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: null })

    const campaignId = 'test-campaign-id'
    const request = new NextRequest(`https://example.com/api/analytics/campaigns/${campaignId}`)
    const response = await GET(request, { params: { id: campaignId } })

    expect(response.status).toBe(401)
    const data = await response.json()
    expect(data.error).toBe('Unauthorized')
    expect(data.message).toBe('Authentication required')

    expect(getCampaignAnalytics).not.toHaveBeenCalled()
  })

  it('should return 404 for non-existent campaign', async () => {
    const mockUser = { id: 'test-user-id', email: '<EMAIL>' }
    const campaignId = 'non-existent-campaign'

    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: mockUser })
    ;(getCampaignAnalytics as jest.Mock).mockRejectedValue(new Error('Campaign not found'))

    const request = new NextRequest(`https://example.com/api/analytics/campaigns/${campaignId}`)
    const response = await GET(request, { params: { id: campaignId } })

    expect(response.status).toBe(404)
    const data = await response.json()
    expect(data.error).toBe('Not Found')
    expect(data.message).toBe('Campaign not found')
  })

  it('should handle analytics service errors', async () => {
    const mockUser = { id: 'test-user-id', email: '<EMAIL>' }
    const campaignId = 'test-campaign-id'

    ;(authService.getCurrentUser as jest.Mock).mockResolvedValue({ user: mockUser })
    ;(getCampaignAnalytics as jest.Mock).mockRejectedValue(new Error('Database error'))

    const request = new NextRequest(`https://example.com/api/analytics/campaigns/${campaignId}`)
    const response = await GET(request, { params: { id: campaignId } })

    expect(response.status).toBe(500)
    const data = await response.json()
    expect(data.error).toBe('Internal Server Error')
    expect(data.message).toBe('Failed to get campaign analytics')
  })
})