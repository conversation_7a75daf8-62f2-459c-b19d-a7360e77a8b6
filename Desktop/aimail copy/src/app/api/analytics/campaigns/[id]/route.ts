import { NextRequest, NextResponse } from 'next/server'
import { authService } from '@/lib/auth'
import { getCampaignAnalytics } from '@/lib/analytics'

// GET /api/analytics/campaigns/[id] - Get analytics for a specific campaign
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const authResult = await authService.getCurrentUser(request)
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Authentication required' },
        { status: 401 }
      )
    }

    const campaignId = params.id

    // Get campaign analytics
    const analytics = await getCampaignAnalytics(campaignId, authResult.user.id)

    return NextResponse.json({
      success: true,
      data: analytics,
    })
  } catch (error) {
    console.error('Error getting campaign analytics:', error)
    
    if (error instanceof Error && error.message === 'Campaign not found') {
      return NextResponse.json(
        { error: 'Not Found', message: 'Campaign not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: 'Internal Server Error', message: 'Failed to get campaign analytics' },
      { status: 500 }
    )
  }
}