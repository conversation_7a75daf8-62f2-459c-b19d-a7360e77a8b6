import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import crypto from 'crypto'
import { handleBounceNotification, handleComplaintNotification } from '@/lib/email-webhooks'

/**
 * SES webhook endpoint for handling SNS notifications
 * Handles bounce and complaint notifications from Amazon SES
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const headersList = headers()
    
    // Verify SNS signature for security
    const isValidSignature = await verifySnSSignature(body, headersList)
    if (!isValidSignature) {
      console.error('Invalid SNS signature')
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    const message = JSON.parse(body)

    // Handle SNS subscription confirmation
    if (message.Type === 'SubscriptionConfirmation') {
      console.log('SNS subscription confirmation received')
      
      // In production, you would typically confirm the subscription
      // by making a GET request to the SubscribeURL
      // For now, we'll just log it
      console.log('SubscribeURL:', message.SubscribeURL)
      
      return NextResponse.json({ message: 'Subscription confirmation received' })
    }

    // Handle SNS notifications
    if (message.Type === 'Notification') {
      const sesMessage = JSON.parse(message.Message)
      
      // Handle bounce notifications
      if (sesMessage.notificationType === 'Bounce') {
        await handleBounceNotification(sesMessage)
        console.log('Bounce notification processed')
      }
      
      // Handle complaint notifications
      else if (sesMessage.notificationType === 'Complaint') {
        await handleComplaintNotification(sesMessage)
        console.log('Complaint notification processed')
      }
      
      // Handle delivery notifications (optional)
      else if (sesMessage.notificationType === 'Delivery') {
        console.log('Delivery notification received (not processed)')
      }
      
      else {
        console.warn('Unknown SES notification type:', sesMessage.notificationType)
      }
    }

    return NextResponse.json({ message: 'Webhook processed successfully' })
    
  } catch (error) {
    console.error('Error processing SES webhook:', error)
    
    // Log error details for monitoring
    const errorDetails = {
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    }
    
    console.error('SES webhook error details:', errorDetails)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Verify SNS signature for security
 * This ensures the webhook is actually from AWS SNS
 */
async function verifySnSSignature(
  body: string,
  headersList: Headers
): Promise<boolean> {
  try {
    const message = JSON.parse(body)
    
    // For subscription confirmations and notifications, verify signature
    if (message.Type === 'SubscriptionConfirmation' || message.Type === 'Notification') {
      const signature = message.Signature
      const signingCertURL = message.SigningCertURL
      
      // In a production environment, you should:
      // 1. Download the certificate from signingCertURL
      // 2. Verify the certificate is from AWS
      // 3. Use the certificate to verify the signature
      
      // For now, we'll do basic validation
      if (!signature || !signingCertURL) {
        return false
      }
      
      // Verify the signing cert URL is from AWS
      const certUrl = new URL(signingCertURL)
      if (!certUrl.hostname.endsWith('.amazonaws.com')) {
        return false
      }
      
      // In production, implement full signature verification
      // For development/testing, we'll accept valid-looking messages
      return true
    }
    
    return true
  } catch (error) {
    console.error('Error verifying SNS signature:', error)
    return false
  }
}

/**
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    endpoint: 'SES webhook handler'
  })
}