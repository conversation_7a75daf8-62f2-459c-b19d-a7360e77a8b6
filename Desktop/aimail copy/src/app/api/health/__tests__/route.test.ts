import { GET, HEAD } from '../route';
import { NextRequest } from 'next/server';

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        limit: jest.fn(() => Promise.resolve({ data: [], error: null }))
      }))
    }))
  }))
}));

describe('/api/health', () => {
  beforeEach(() => {
    // Set required environment variables for tests
    process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
    process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/health', () => {
    it('should return healthy status when all checks pass', async () => {
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await GET(request);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('status', 'healthy');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('checks');
      expect(data.checks).toHaveProperty('database');
      expect(data.checks).toHaveProperty('environment');
      expect(data.checks).toHaveProperty('services');
    });

    it('should return unhealthy status when environment variables are missing', async () => {
      // Remove required environment variable
      delete process.env.NEXT_PUBLIC_SUPABASE_URL;
      
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await GET(request);
      
      expect(response.status).toBe(503);
      
      const data = await response.json();
      expect(data).toHaveProperty('status', 'unhealthy');
      expect(data.checks.environment.status).toBe('error');
    });

    it('should include deployment information', async () => {
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await GET(request);
      
      const data = await response.json();
      expect(data).toHaveProperty('deployment');
      expect(data.deployment).toHaveProperty('vercel');
      expect(data.deployment).toHaveProperty('region');
      expect(data.deployment).toHaveProperty('environment');
    });

    it('should include version information', async () => {
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await GET(request);
      
      const data = await response.json();
      expect(data).toHaveProperty('version');
    });
  });

  describe('HEAD /api/health', () => {
    it('should return 200 status for ping check', async () => {
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await HEAD(request);
      
      expect(response.status).toBe(200);
    });
  });
});