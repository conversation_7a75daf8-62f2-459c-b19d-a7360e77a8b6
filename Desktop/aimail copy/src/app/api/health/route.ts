import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

/**
 * Health check endpoint for deployment verification
 * 
 * This endpoint checks:
 * - Database connectivity
 * - Environment variables
 * - External service availability
 */
export async function GET(request: NextRequest) {
  const checks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {
      database: { status: 'unknown', message: '' },
      environment: { status: 'unknown', message: '' },
      services: { status: 'unknown', message: '' }
    },
    version: process.env.npm_package_version || '1.0.0',
    deployment: {
      vercel: !!process.env.VERCEL,
      region: process.env.VERCEL_REGION || 'unknown',
      environment: process.env.NODE_ENV || 'development'
    }
  };

  let overallStatus = 'healthy';

  try {
    // Check database connectivity
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      checks.checks.database = {
        status: 'error',
        message: `Database connection failed: ${error.message}`
      };
      overallStatus = 'unhealthy';
    } else {
      checks.checks.database = {
        status: 'healthy',
        message: 'Database connection successful'
      };
    }
  } catch (error) {
    checks.checks.database = {
      status: 'error',
      message: `Database check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
    overallStatus = 'unhealthy';
  }

  // Check environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingEnvVars.length > 0) {
    checks.checks.environment = {
      status: 'error',
      message: `Missing environment variables: ${missingEnvVars.join(', ')}`
    };
    overallStatus = 'unhealthy';
  } else {
    checks.checks.environment = {
      status: 'healthy',
      message: 'All required environment variables are set'
    };
  }

  // Check external services (optional services don't fail health check)
  const serviceChecks = [];
  
  // Check AWS SES configuration
  if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
    serviceChecks.push('AWS SES configured');
  }
  
  // Check Perplexity API configuration
  if (process.env.PERPLEXITY_API_KEY) {
    serviceChecks.push('Perplexity API configured');
  }

  checks.checks.services = {
    status: 'healthy',
    message: serviceChecks.length > 0 
      ? `Services configured: ${serviceChecks.join(', ')}`
      : 'No optional services configured'
  };

  checks.status = overallStatus;

  // Return appropriate HTTP status code
  const statusCode = overallStatus === 'healthy' ? 200 : 503;
  
  return NextResponse.json(checks, { status: statusCode });
}

/**
 * Simple ping endpoint for basic connectivity checks
 */
export async function HEAD(request: NextRequest) {
  return new NextResponse(null, { status: 200 });
}