import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { deleteUserData } from '@/lib/data-export'
import { sanitizedTextSchema } from '@/lib/security'
import { z } from 'zod'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

const deleteRequestSchema = z.object({
  confirmation: z.literal('DELETE_MY_DATA'),
  reason: sanitizedTextSchema.optional()
})

export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = deleteRequestSchema.parse(body)

    // Additional security check - require exact confirmation text
    if (validatedData.confirmation !== 'DELETE_MY_DATA') {
      return NextResponse.json(
        { error: 'Invalid confirmation text' },
        { status: 400 }
      )
    }

    // Delete user data
    await deleteUserData(user.id)

    return NextResponse.json({
      message: 'User data has been permanently deleted',
      deletedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Data deletion error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to delete user data' },
      { status: 500 }
    )
  }
}