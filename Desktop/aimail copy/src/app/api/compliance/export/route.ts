import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { exportUserData } from '@/lib/data-export'
import { sanitizedTextSchema } from '@/lib/security'
import { z } from 'zod'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

const exportRequestSchema = z.object({
  includeContacts: z.boolean().optional().default(true),
  includeCampaigns: z.boolean().optional().default(true),
  includeAnalytics: z.boolean().optional().default(true),
  includeAuditLogs: z.boolean().optional().default(false),
  format: z.enum(['json', 'csv']).optional().default('json'),
  dateRange: z.object({
    start: z.string().datetime(),
    end: z.string().datetime()
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = exportRequestSchema.parse(body)

    // Export user data
    const exportResult = await exportUserData({
      userId: user.id,
      ...validatedData
    })

    // Return the export as a downloadable file
    const response = new NextResponse(exportResult.data, {
      status: 200,
      headers: {
        'Content-Type': validatedData.format === 'csv' 
          ? 'text/csv' 
          : 'application/json',
        'Content-Disposition': `attachment; filename="${exportResult.filename}"`,
        'Content-Length': exportResult.size.toString(),
        'X-Export-Timestamp': exportResult.exportedAt
      }
    })

    return response

  } catch (error) {
    console.error('Export error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to export data' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get user from session
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get export history for the user
    const { data: auditLogs } = await supabase
      .from('audit_logs')
      .select('created_at, details')
      .eq('user_id', user.id)
      .eq('action', 'data_exported')
      .order('created_at', { ascending: false })
      .limit(10)

    return NextResponse.json({
      exportHistory: auditLogs || []
    })

  } catch (error) {
    console.error('Export history error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch export history' },
      { status: 500 }
    )
  }
}