import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '../../../../lib/auth'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Basic password validation
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      )
    }

    const result = await AuthService.signUp({ email, password })

    return NextResponse.json({
      message: 'Account created successfully',
      user: result.user,
      // Note: Supabase may require email confirmation depending on your settings
      requiresConfirmation: !result.session
    })
  } catch (error) {
    console.error('Signup error:', error)
    
    // Handle specific Supabase errors
    let errorMessage = 'Account creation failed'
    if (error instanceof Error) {
      if (error.message.includes('already registered')) {
        errorMessage = 'An account with this email already exists'
      } else if (error.message.includes('password')) {
        errorMessage = 'Password does not meet requirements'
      } else {
        errorMessage = error.message
      }
    }
    
    return NextResponse.json(
      { 
        error: 'Registration failed',
        message: errorMessage
      },
      { status: 400 }
    )
  }
}
