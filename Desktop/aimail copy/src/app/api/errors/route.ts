import { NextRequest, NextResponse } from 'next/server'
import { withError<PERSON><PERSON><PERSON> } from '@/lib/error-handler'
import { z } from 'zod'

const errorEventSchema = z.object({
  id: z.string(),
  error: z.string(),
  message: z.string(),
  stack: z.string().optional(),
  level: z.enum(['error', 'warning', 'info']),
  context: z.object({
    userId: z.string().optional(),
    userAgent: z.string().optional(),
    url: z.string().optional(),
    method: z.string().optional(),
    timestamp: z.string(),
    sessionId: z.string().optional(),
    buildVersion: z.string().optional()
  }).passthrough(),
  fingerprint: z.string().optional()
})

async function handleErrorReport(request: NextRequest) {
  const body = await request.json()
  const errorEvent = errorEventSchema.parse(body)

  // Log the error
  console.error('Client Error Report:', {
    id: errorEvent.id,
    error: errorEvent.error,
    message: errorEvent.message,
    level: errorEvent.level,
    context: errorEvent.context,
    fingerprint: errorEvent.fingerprint
  })

  // In a real application, you would:
  // 1. Store in database for analysis
  // 2. Send to external monitoring service (Sentry, LogRocket, etc.)
  // 3. Alert on critical errors
  // 4. Aggregate similar errors by fingerprint

  // For now, we'll just acknowledge receipt
  return NextResponse.json({ 
    success: true, 
    message: 'Error report received',
    id: errorEvent.id 
  })
}

export const POST = withErrorHandler(handleErrorReport)