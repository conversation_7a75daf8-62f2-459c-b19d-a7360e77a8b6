'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '../../components/layout/dashboard-layout'
import { CampaignManager } from '../../components/campaigns/campaign-manager'
import { useAuth } from '../../contexts/auth-context'
import type { Campaign, CampaignFilters, CampaignStats, CreateCampaignData } from '../../types/database'

export default function CampaignsPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)

  const handleCreateCampaign = async (data: CreateCampaignData): Promise<Campaign> => {
    setLoading(true)
    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to create campaign')
      }

      const result = await response.json()
      return result.campaign
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateCampaign = async (data: { id: string } & Partial<CreateCampaignData>): Promise<Campaign> => {
    setLoading(true)
    try {
      const { id, ...updateData } = data
      const response = await fetch(`/api/campaigns/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update campaign')
      }

      const result = await response.json()
      return result.campaign
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCampaign = async (campaignId: string): Promise<void> => {
    const response = await fetch(`/api/campaigns/${campaignId}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to delete campaign')
    }
  }

  const handleDuplicateCampaign = async (campaignId: string, newName?: string): Promise<Campaign> => {
    const response = await fetch(`/api/campaigns/${campaignId}/duplicate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: newName }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to duplicate campaign')
    }

    const result = await response.json()
    return result.campaign
  }

  const handleLoadCampaigns = async (filters?: CampaignFilters): Promise<{ campaigns: Campaign[]; count: number }> => {
    const params = new URLSearchParams()
    
    if (filters?.status) {
      params.append('status', filters.status)
    }
    
    if (filters?.search) {
      params.append('search', filters.search)
    }
    
    if (filters?.limit) {
      params.append('limit', filters.limit.toString())
    }
    
    if (filters?.offset) {
      params.append('offset', filters.offset.toString())
    }

    const response = await fetch(`/api/campaigns?${params.toString()}`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to load campaigns')
    }

    const result = await response.json()
    return {
      campaigns: result.campaigns,
      count: result.count
    }
  }

  const handleLoadCampaignStats = async (campaignIds: string[]): Promise<{ [campaignId: string]: CampaignStats }> => {
    const stats: { [campaignId: string]: CampaignStats } = {}
    
    // Load stats for each campaign
    for (const campaignId of campaignIds) {
      try {
        const response = await fetch(`/api/campaigns/${campaignId}/stats`)
        if (response.ok) {
          const result = await response.json()
          stats[campaignId] = result.stats
        }
      } catch (error) {
        console.error(`Failed to load stats for campaign ${campaignId}:`, error)
      }
    }
    
    return stats
  }

  const handleSendTestEmail = async (campaign: Campaign, email: string): Promise<void> => {
    // TODO: Implement test email sending in task 8
    console.log('Test email sending will be implemented in task 8')
    throw new Error('Test email sending not yet implemented')
  }

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Please log in to view campaigns.</p>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <CampaignManager
        onCreateCampaign={handleCreateCampaign}
        onUpdateCampaign={handleUpdateCampaign}
        onDeleteCampaign={handleDeleteCampaign}
        onDuplicateCampaign={handleDuplicateCampaign}
        onSendTestEmail={handleSendTestEmail}
        onLoadCampaigns={handleLoadCampaigns}
        onLoadCampaignStats={handleLoadCampaignStats}
      />
    </DashboardLayout>
  )
}