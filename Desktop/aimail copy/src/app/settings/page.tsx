'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '../../components/layout/dashboard-layout'
import { Button } from '../../components/ui/button'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { SESSettings } from '../../components/settings/ses-settings'
import { PerplexitySettings } from '../../components/settings/perplexity-settings'
import { BrandingSettings } from '../../components/settings/branding-settings'
import { ImportExport } from '../../components/settings/import-export'
import { DataExportComponent } from '../../components/compliance/data-export'
import { DataDeletionComponent } from '../../components/compliance/data-deletion'
import { Save, CheckCircle, XCircle } from 'lucide-react'

interface SettingsData {
  ses: {
    region: string
    fromEmail: string
    configured: boolean
  }
  perplexity: {
    configured: boolean
  }
  branding: {
    appName: string
    logo: string
    colors: {
      primary: string
      secondary: string
      accent: string
    }
    theme: {
      borderRadius: string
      fontFamily: string
    }
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SettingsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saveResult, setSaveResult] = useState<{
    type: 'success' | 'error'
    message: string
  } | null>(null)

  const [currentConfig, setCurrentConfig] = useState({
    ses: {
      accessKeyId: '',
      secretAccessKey: '',
      region: '',
      fromEmail: '',
    },
    perplexity: {
      apiKey: '',
    },
    branding: {
      appName: 'EmailFlow',
      logo: '/logo.png',
      colors: {
        primary: '#3b82f6',
        secondary: '#64748b',
        accent: '#f59e0b',
      },
      theme: {
        borderRadius: '0.5rem',
        fontFamily: 'Inter',
      },
    },
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
        
        // Update current config with fetched data
        setCurrentConfig(prev => ({
          ...prev,
          ses: {
            ...prev.ses,
            region: data.ses.region,
            fromEmail: data.ses.fromEmail,
          },
          branding: data.branding,
        }))
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    setSaveResult(null)

    try {
      // In a real implementation, you would save these to environment variables
      // or a secure configuration store. For now, we'll just show a success message.
      
      // Simulate save delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setSaveResult({
        type: 'success',
        message: 'Settings saved successfully! Please restart the application to apply changes.',
      })
    } catch (error) {
      setSaveResult({
        type: 'error',
        message: 'Failed to save settings. Please try again.',
      })
    } finally {
      setSaving(false)
    }
  }

  const handleImport = (importedSettings: any) => {
    if (importedSettings.branding) {
      setCurrentConfig(prev => ({
        ...prev,
        branding: importedSettings.branding,
      }))
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-2">Loading settings...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-2">
              Configure your email marketing platform
            </p>
          </div>
          
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>

        {saveResult && (
          <Alert>
            <div className="flex items-center gap-2">
              {saveResult.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <XCircle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription>{saveResult.message}</AlertDescription>
            </div>
          </Alert>
        )}

        <div className="space-y-6">
          <SESSettings
            initialConfig={{
              region: settings?.ses.region,
              fromEmail: settings?.ses.fromEmail,
            }}
            onConfigChange={(config) => 
              setCurrentConfig(prev => ({ ...prev, ses: config }))
            }
          />

          <PerplexitySettings
            onConfigChange={(config) => 
              setCurrentConfig(prev => ({ ...prev, perplexity: config }))
            }
          />

          <BrandingSettings
            initialConfig={currentConfig.branding}
            onConfigChange={(config) => 
              setCurrentConfig(prev => ({ ...prev, branding: config }))
            }
          />

          <ImportExport onImport={handleImport} />

          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Data & Compliance</h2>
            <div className="space-y-6">
              <DataExportComponent />
              <DataDeletionComponent />
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-yellow-800 mb-2">
            Important Security Note
          </h3>
          <p className="text-sm text-yellow-700">
            API keys and sensitive configuration should be stored as environment variables 
            for security. This interface is for testing and configuration validation. 
            In production, set these values in your deployment environment.
          </p>
        </div>
      </div>
    </DashboardLayout>
  )
}