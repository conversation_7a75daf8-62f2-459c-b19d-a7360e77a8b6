import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '../contexts/auth-context'
import { AuthWrapper } from '../components/auth/auth-wrapper'
import { brandingConfig } from '../config/branding'
import { ErrorBoundary } from '../components/error-boundary'
import { ToastProvider } from '../components/ui/toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: brandingConfig.appName,
  description: 'A modern email marketing platform built with Next.js',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ErrorBoundary>
          <ToastProvider>
            <AuthProvider>
              <AuthWrapper>
                {children}
              </AuthWrapper>
            </AuthProvider>
          </ToastProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}