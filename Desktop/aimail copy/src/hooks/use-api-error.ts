'use client'

import { useCallback } from 'react'
import { useToast } from '@/components/ui/toast'
import { captureError } from '@/lib/monitoring'

interface APIError {
  error: string
  message: string
  code?: string
  details?: any
}

export function useApiError() {
  const { addToast } = useToast()

  const handleError = useCallback((error: unknown, context?: Record<string, any>) => {
    let errorMessage = 'An unexpected error occurred'
    let errorTitle = 'Error'
    let shouldRetry = false
    let retryAction: (() => void) | undefined

    // Handle fetch errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      errorTitle = 'Connection Error'
      errorMessage = 'Unable to connect to the server. Please check your internet connection.'
      shouldRetry = true
    }
    // Handle API response errors
    else if (error && typeof error === 'object' && 'message' in error) {
      const apiError = error as APIError
      errorMessage = apiError.message
      
      // Customize based on error code
      switch (apiError.code) {
        case 'AUTHENTICATION_ERROR':
          errorTitle = 'Authentication Required'
          errorMessage = 'Please log in to continue'
          break
        case 'AUTHORIZATION_ERROR':
          errorTitle = 'Access Denied'
          errorMessage = 'You don\'t have permission to perform this action'
          break
        case 'VALIDATION_ERROR':
          errorTitle = 'Invalid Data'
          break
        case 'RATE_LIMIT_ERROR':
          errorTitle = 'Rate Limited'
          errorMessage = 'Too many requests. Please wait a moment before trying again.'
          break
        case 'EXTERNAL_SERVICE_ERROR':
          errorTitle = 'Service Unavailable'
          shouldRetry = true
          break
        default:
          shouldRetry = true
      }
    }
    // Handle generic errors
    else if (error instanceof Error) {
      errorMessage = error.message
    }
    // Handle string errors
    else if (typeof error === 'string') {
      errorMessage = error
    }

    // Set retry action if applicable
    if (shouldRetry && context?.retry) {
      retryAction = context.retry
    }

    // Log error for monitoring
    captureError(error instanceof Error ? error : new Error(errorMessage), context)

    // Show toast notification
    addToast({
      type: 'error',
      title: errorTitle,
      description: errorMessage,
      action: retryAction ? {
        label: 'Try Again',
        onClick: retryAction
      } : undefined
    })
  }, [addToast])

  return { handleError }
}

// Hook for handling async operations with error handling
export function useAsyncOperation() {
  const { handleError } = useApiError()
  const { addToast } = useToast()

  const execute = useCallback(async <T>(
    operation: () => Promise<T>,
    options?: {
      successMessage?: string
      errorContext?: Record<string, any>
      onSuccess?: (result: T) => void
      onError?: (error: unknown) => void
    }
  ): Promise<T | null> => {
    try {
      const result = await operation()
      
      if (options?.successMessage) {
        addToast({
          type: 'success',
          title: 'Success',
          description: options.successMessage
        })
      }
      
      options?.onSuccess?.(result)
      return result
    } catch (error) {
      handleError(error, options?.errorContext)
      options?.onError?.(error)
      return null
    }
  }, [handleError, addToast])

  return { execute }
}

// Hook for API calls with automatic error handling
export function useApiCall() {
  const { handleError } = useApiError()

  const apiCall = useCallback(async <T>(
    url: string,
    options?: RequestInit & {
      successMessage?: string
      errorContext?: Record<string, any>
    }
  ): Promise<T | null> => {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers
        },
        ...options
      })

      if (!response.ok) {
        let errorData: APIError
        try {
          errorData = await response.json()
        } catch {
          errorData = {
            error: 'HTTPError',
            message: `HTTP ${response.status}: ${response.statusText}`
          }
        }
        throw errorData
      }

      const data = await response.json()
      return data
    } catch (error) {
      handleError(error, { url, method: options?.method, ...options?.errorContext })
      return null
    }
  }, [handleError])

  return { apiCall }
}