import { supabase, supabaseAdmin } from './supabase'
import type { DatabaseResponse } from '../types/database'

/**
 * Test database connection and authentication
 */
export async function testDatabaseConnection(): Promise<DatabaseResponse<boolean>> {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)

    if (error) {
      return {
        data: false,
        error: error as <PERSON>rror
      }
    }

    return {
      data: true,
      error: null
    }
  } catch (error) {
    return {
      data: false,
      error: error as Error
    }
  }
}

/**
 * Test admin database connection
 */
export async function testAdminConnection(): Promise<DatabaseResponse<boolean>> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('count')
      .limit(1)

    if (error) {
      return {
        data: false,
        error: error as Error
      }
    }

    return {
      data: true,
      error: null
    }
  } catch (error) {
    return {
      data: false,
      error: error as Error
    }
  }
}

/**
 * Get database health status
 */
export async function getDatabaseHealth() {
  const connectionTest = await testDatabaseConnection()
  const adminTest = await testAdminConnection()

  return {
    connection: connectionTest.data,
    connectionError: connectionTest.error?.message,
    adminConnection: adminTest.data,
    adminConnectionError: adminTest.error?.message,
    timestamp: new Date().toISOString()
  }
}

/**
 * Initialize database with required extensions and functions
 */
export async function initializeDatabase(): Promise<DatabaseResponse<boolean>> {
  try {
    // Test if we can create the UUID extension (requires admin privileges)
    const { error } = await supabaseAdmin.rpc('create_extension_if_not_exists', {
      extension_name: 'uuid-ossp'
    })

    if (error) {
      console.warn('Could not create UUID extension:', error.message)
      // This is not critical as the extension might already exist
    }

    return {
      data: true,
      error: null
    }
  } catch (error) {
    return {
      data: false,
      error: error as Error
    }
  }
}

/**
 * Validate database schema
 */
export async function validateDatabaseSchema(): Promise<DatabaseResponse<string[]>> {
  try {
    const requiredTables = ['users', 'contacts', 'campaigns', 'sends', 'events', 'suppression']
    const missingTables: string[] = []

    for (const table of requiredTables) {
      const { error } = await supabase
        .from(table)
        .select('*')
        .limit(0)

      if (error) {
        missingTables.push(table)
      }
    }

    return {
      data: missingTables,
      error: null
    }
  } catch (error) {
    return {
      data: [],
      error: error as Error
    }
  }
}

/**
 * Get table row counts for monitoring
 */
export async function getTableStats(userId?: string) {
  try {
    const stats: Record<string, number> = {}

    // Get user-specific counts if userId provided
    if (userId) {
      const { count: contactsCount } = await supabase
        .from('contacts')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      const { count: campaignsCount } = await supabase
        .from('campaigns')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      const { count: suppressionCount } = await supabase
        .from('suppression')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      stats.contacts = contactsCount || 0
      stats.campaigns = campaignsCount || 0
      stats.suppression = suppressionCount || 0
    }

    // Get global counts (admin only)
    const { count: usersCount } = await supabaseAdmin
      .from('users')
      .select('*', { count: 'exact', head: true })

    const { count: sendsCount } = await supabaseAdmin
      .from('sends')
      .select('*', { count: 'exact', head: true })

    const { count: eventsCount } = await supabaseAdmin
      .from('events')
      .select('*', { count: 'exact', head: true })

    stats.users = usersCount || 0
    stats.sends = sendsCount || 0
    stats.events = eventsCount || 0

    return {
      data: stats,
      error: null
    }
  } catch (error) {
    return {
      data: {},
      error: error as Error
    }
  }
}