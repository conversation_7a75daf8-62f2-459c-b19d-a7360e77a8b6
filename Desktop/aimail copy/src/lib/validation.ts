import { z } from 'zod'

// Common validation schemas
export const emailSchema = z.string().email('Please enter a valid email address')

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(255, 'Name must be less than 255 characters')

export const subjectSchema = z
  .string()
  .min(1, 'Subject is required')
  .max(255, 'Subject must be less than 255 characters')

export const htmlBodySchema = z
  .string()
  .min(1, 'Email content is required')

// Contact validation
export const contactSchema = z.object({
  email: emailSchema,
  name: nameSchema.optional(),
  status: z.enum(['active', 'unsubscribed', 'bounced']).optional()
})

export const contactImportSchema = z.object({
  contacts: z.array(contactSchema).min(1, 'At least one contact is required')
})

// Campaign validation
export const campaignSchema = z.object({
  name: nameSchema,
  subject: subjectSchema,
  html_body: htmlBodySchema,
  text_body: z.string().optional(),
  scheduled_at: z.string().datetime().optional()
})

export const campaignUpdateSchema = campaignSchema.partial()

// Auth validation
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
})

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Settings validation
export const sesSettingsSchema = z.object({
  accessKeyId: z.string().min(1, 'AWS Access Key ID is required'),
  secretAccessKey: z.string().min(1, 'AWS Secret Access Key is required'),
  region: z.string().min(1, 'AWS Region is required'),
  fromEmail: emailSchema
})

export const perplexitySettingsSchema = z.object({
  apiKey: z.string().min(1, 'Perplexity API Key is required')
})

export const brandingSettingsSchema = z.object({
  appName: z.string().min(1, 'App name is required').max(50, 'App name must be less than 50 characters'),
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Please enter a valid hex color'),
  logoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal(''))
})

// AI generation validation
export const aiGenerationSchema = z.object({
  prompt: z.string().min(10, 'Prompt must be at least 10 characters'),
  type: z.enum(['subject', 'body', 'both']),
  tone: z.enum(['professional', 'casual', 'friendly', 'urgent']).optional(),
  length: z.enum(['short', 'medium', 'long']).optional()
})

// Validation helper functions
export function validateEmail(email: string): { isValid: boolean; error?: string } {
  try {
    emailSchema.parse(email)
    return { isValid: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.issues[0]?.message || 'Invalid email format' }
    }
    return { isValid: false, error: 'Invalid email format' }
  }
}

export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  try {
    passwordSchema.parse(password)
    return { isValid: true, errors: [] }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, errors: error.issues?.map(e => e.message) || ['Invalid password format'] }
    }
    return { isValid: false, errors: ['Invalid password format'] }
  }
}

export function getValidationErrors(error: z.ZodError): Record<string, string> {
  const errors: Record<string, string> = {}
  
  error.issues?.forEach((err) => {
    const path = err.path.join('.')
    errors[path] = err.message
  })
  
  return errors
}

// Form validation hook
export function useFormValidation<T>(schema: z.ZodSchema<T>) {
  return {
    validate: (data: unknown): { success: true; data: T } | { success: false; errors: Record<string, string> } => {
      try {
        const validData = schema.parse(data)
        return { success: true, data: validData }
      } catch (error) {
        if (error instanceof z.ZodError) {
          return { success: false, errors: getValidationErrors(error) }
        }
        return { success: false, errors: { _form: 'Validation failed' } }
      }
    },
    
    validateField: (fieldName: string, value: unknown): string | undefined => {
      try {
        // Create a partial schema for single field validation
        const fieldSchema = schema.shape?.[fieldName as keyof typeof schema.shape]
        if (fieldSchema) {
          fieldSchema.parse(value)
        }
        return undefined
      } catch (error) {
        if (error instanceof z.ZodError) {
          return error.issues[0]?.message
        }
        return 'Validation error'
      }
    }
  }
}