interface ErrorContext {
  userId?: string
  userAgent?: string
  url?: string
  method?: string
  timestamp: string
  sessionId?: string
  buildVersion?: string
}

interface ErrorEvent {
  id: string
  error: string
  message: string
  stack?: string
  level: 'error' | 'warning' | 'info'
  context: ErrorContext
  fingerprint?: string
}

class ErrorMonitor {
  private errors: ErrorEvent[] = []
  private maxErrors = 100
  private sessionId: string

  constructor() {
    this.sessionId = this.generateSessionId()
    
    // Listen for unhandled errors
    if (typeof window !== 'undefined') {
      window.addEventListener('error', this.handleGlobalError.bind(this))
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this))
    }
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private generateFingerprint(error: string, stack?: string): string {
    // Create a fingerprint for grouping similar errors
    const content = `${error}${stack?.split('\n')[0] || ''}`
    return btoa(content).substr(0, 16)
  }

  private handleGlobalError(event: ErrorEvent) {
    this.captureError(event.error || new Error(event.message), {
      url: event.filename,
      line: event.lineno,
      column: event.colno
    })
  }

  private handleUnhandledRejection(event: PromiseRejectionEvent) {
    const error = event.reason instanceof Error 
      ? event.reason 
      : new Error(String(event.reason))
    
    this.captureError(error, { type: 'unhandled_promise_rejection' })
  }

  captureError(
    error: Error | string, 
    context: Partial<ErrorContext> & Record<string, any> = {},
    level: 'error' | 'warning' | 'info' = 'error'
  ) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const stack = error instanceof Error ? error.stack : undefined
    
    const errorEvent: ErrorEvent = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      error: error instanceof Error ? error.name : 'Error',
      message: errorMessage,
      stack,
      level,
      context: {
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION || 'unknown',
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        ...context
      },
      fingerprint: this.generateFingerprint(errorMessage, stack)
    }

    // Add to local storage
    this.errors.push(errorEvent)
    if (this.errors.length > this.maxErrors) {
      this.errors.shift()
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error captured:', errorEvent)
    }

    // Send to external service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToExternalService(errorEvent)
    }

    // Store in localStorage for debugging
    if (typeof window !== 'undefined') {
      try {
        const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]')
        storedErrors.push(errorEvent)
        // Keep only last 50 errors in localStorage
        if (storedErrors.length > 50) {
          storedErrors.splice(0, storedErrors.length - 50)
        }
        localStorage.setItem('app_errors', JSON.stringify(storedErrors))
      } catch (e) {
        // Ignore localStorage errors
      }
    }
  }

  private async sendToExternalService(errorEvent: ErrorEvent) {
    try {
      // In a real application, you would send this to your error monitoring service
      // Example: Sentry, LogRocket, Bugsnag, etc.
      
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorEvent)
      })
    } catch (e) {
      // Silently fail - don't want error reporting to break the app
      console.warn('Failed to send error to monitoring service:', e)
    }
  }

  getErrors(): ErrorEvent[] {
    return [...this.errors]
  }

  getErrorsByFingerprint(fingerprint: string): ErrorEvent[] {
    return this.errors.filter(error => error.fingerprint === fingerprint)
  }

  clearErrors() {
    this.errors = []
    if (typeof window !== 'undefined') {
      localStorage.removeItem('app_errors')
    }
  }

  // Performance monitoring
  measurePerformance(name: string, fn: () => Promise<any>) {
    const start = performance.now()
    
    return fn().then(
      (result) => {
        const duration = performance.now() - start
        this.capturePerformanceMetric(name, duration, 'success')
        return result
      },
      (error) => {
        const duration = performance.now() - start
        this.capturePerformanceMetric(name, duration, 'error')
        this.captureError(error, { operation: name, duration })
        throw error
      }
    )
  }

  private capturePerformanceMetric(name: string, duration: number, status: 'success' | 'error') {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance: ${name} took ${duration.toFixed(2)}ms (${status})`)
    }

    // In production, send to analytics service
    if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
      // Example: analytics.track('performance', { name, duration, status })
    }
  }
}

// Singleton instance
export const errorMonitor = new ErrorMonitor()

// Convenience functions
export function captureError(error: Error | string, context?: Record<string, any>) {
  errorMonitor.captureError(error, context)
}

export function captureWarning(message: string, context?: Record<string, any>) {
  errorMonitor.captureError(message, context, 'warning')
}

export function captureInfo(message: string, context?: Record<string, any>) {
  errorMonitor.captureError(message, context, 'info')
}

export function withErrorCapture<T extends (...args: any[]) => any>(
  fn: T,
  context?: Record<string, any>
): T {
  return ((...args: any[]) => {
    try {
      const result = fn(...args)
      
      // Handle async functions
      if (result instanceof Promise) {
        return result.catch((error) => {
          errorMonitor.captureError(error, { ...context, args })
          throw error
        })
      }
      
      return result
    } catch (error) {
      errorMonitor.captureError(error as Error, { ...context, args })
      throw error
    }
  }) as T
}

export function measurePerformance<T>(name: string, fn: () => Promise<T>): Promise<T> {
  return errorMonitor.measurePerformance(name, fn)
}