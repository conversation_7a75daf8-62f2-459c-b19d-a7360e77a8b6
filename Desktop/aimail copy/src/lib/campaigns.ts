import { db } from './database'
import type {
  Campaign,
  CreateCampaignData,
  UpdateCampaignData,
  CampaignFilters,
  CampaignStats,
  DatabaseResponse,
  DatabaseListResponse
} from '../types/database'

// Campaign validation utilities
export const campaignValidation = {
  validateName: (name: string): boolean => {
    const trimmed = name.trim()
    return trimmed.length > 0 && trimmed.length <= 255
  },

  validateSubject: (subject: string): boolean => {
    const trimmed = subject.trim()
    return trimmed.length > 0 && trimmed.length <= 255
  },

  validateHtmlBody: (htmlBody: string): boolean => {
    const trimmed = htmlBody.trim()
    return trimmed.length > 0
  },

  validateTextBody: (textBody?: string): boolean => {
    if (textBody === undefined || textBody === null) return true // Text body is optional
    return textBody.trim().length > 0
  },

  validateStatus: (status: string): status is 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' => {
    return ['draft', 'scheduled', 'sending', 'sent', 'failed'].includes(status)
  },

  validateScheduledAt: (scheduledAt?: string): boolean => {
    if (!scheduledAt) return true // Scheduled date is optional
    const date = new Date(scheduledAt)
    return !isNaN(date.getTime()) && date > new Date()
  },

  validateCampaignData: (data: Partial<CreateCampaignData>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (!data.name) {
      errors.push('Campaign name is required')
    } else if (!campaignValidation.validateName(data.name)) {
      errors.push('Campaign name must be between 1 and 255 characters')
    }

    if (!data.subject) {
      errors.push('Subject line is required')
    } else if (!campaignValidation.validateSubject(data.subject)) {
      errors.push('Subject line must be between 1 and 255 characters')
    }

    if (!data.html_body) {
      errors.push('HTML body is required')
    } else if (!campaignValidation.validateHtmlBody(data.html_body)) {
      errors.push('HTML body cannot be empty')
    }

    if (!data.user_id) {
      errors.push('User ID is required')
    }

    if (data.text_body && !campaignValidation.validateTextBody(data.text_body)) {
      errors.push('Text body cannot be empty if provided')
    }

    if (data.scheduled_at && !campaignValidation.validateScheduledAt(data.scheduled_at)) {
      errors.push('Scheduled date must be in the future')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  sanitizeCampaignData: (data: CreateCampaignData): CreateCampaignData => {
    return {
      ...data,
      name: data.name.trim(),
      subject: data.subject.trim(),
      html_body: data.html_body.trim(),
      text_body: data.text_body?.trim() || undefined
    }
  }
}

// Campaign service functions
export class CampaignService {
  /**
   * Get campaigns with optional filtering and pagination
   */
  async getCampaigns(userId: string, filters?: CampaignFilters): Promise<DatabaseListResponse<Campaign>> {
    if (!userId) {
      return {
        data: [],
        error: new Error('User ID is required'),
        count: 0
      }
    }

    return await db.getCampaigns(userId, filters)
  }

  /**
   * Get a single campaign by ID
   */
  async getCampaignById(campaignId: string, userId: string): Promise<DatabaseResponse<Campaign>> {
    if (!campaignId || !userId) {
      return {
        data: null,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    try {
      const campaign = await db.getCampaignById(campaignId)
      
      // Verify campaign belongs to user
      if (campaign.data && campaign.data.user_id !== userId) {
        return {
          data: null,
          error: new Error('Campaign not found or access denied')
        }
      }

      return campaign
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  /**
   * Create a new campaign
   */
  async createCampaign(campaignData: CreateCampaignData): Promise<DatabaseResponse<Campaign>> {
    // Validate input data
    const validation = campaignValidation.validateCampaignData(campaignData)
    if (!validation.isValid) {
      return {
        data: null,
        error: new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }
    }

    // Sanitize data
    const sanitizedData = campaignValidation.sanitizeCampaignData(campaignData)

    return await db.createCampaign(sanitizedData)
  }

  /**
   * Update an existing campaign
   */
  async updateCampaign(campaignData: UpdateCampaignData, userId: string): Promise<DatabaseResponse<Campaign>> {
    if (!campaignData.id || !userId) {
      return {
        data: null,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    // Verify campaign belongs to user
    const existingCampaign = await this.getCampaignById(campaignData.id, userId)
    if (!existingCampaign.data) {
      return {
        data: null,
        error: new Error('Campaign not found or access denied')
      }
    }

    // Prevent updates to sent campaigns
    if (existingCampaign.data.status === 'sent') {
      return {
        data: null,
        error: new Error('Cannot update a campaign that has already been sent')
      }
    }

    // Prevent updates to campaigns that are currently sending
    if (existingCampaign.data.status === 'sending') {
      return {
        data: null,
        error: new Error('Cannot update a campaign that is currently being sent')
      }
    }

    // Validate update data
    const updateValidation: { isValid: boolean; errors: string[] } = { isValid: true, errors: [] }

    if (campaignData.name && !campaignValidation.validateName(campaignData.name)) {
      updateValidation.errors.push('Campaign name must be between 1 and 255 characters')
    }

    if (campaignData.subject && !campaignValidation.validateSubject(campaignData.subject)) {
      updateValidation.errors.push('Subject line must be between 1 and 255 characters')
    }

    if (campaignData.html_body && !campaignValidation.validateHtmlBody(campaignData.html_body)) {
      updateValidation.errors.push('HTML body cannot be empty')
    }

    if (campaignData.text_body && !campaignValidation.validateTextBody(campaignData.text_body)) {
      updateValidation.errors.push('Text body cannot be empty if provided')
    }

    if (campaignData.status && !campaignValidation.validateStatus(campaignData.status)) {
      updateValidation.errors.push('Status must be draft, scheduled, sending, sent, or failed')
    }

    if (campaignData.scheduled_at && !campaignValidation.validateScheduledAt(campaignData.scheduled_at)) {
      updateValidation.errors.push('Scheduled date must be in the future')
    }

    updateValidation.isValid = updateValidation.errors.length === 0

    if (!updateValidation.isValid) {
      return {
        data: null,
        error: new Error(`Validation failed: ${updateValidation.errors.join(', ')}`)
      }
    }

    // Sanitize data
    const sanitizedData = {
      ...campaignData,
      name: campaignData.name?.trim(),
      subject: campaignData.subject?.trim(),
      html_body: campaignData.html_body?.trim(),
      text_body: campaignData.text_body?.trim()
    }

    return await db.updateCampaign(sanitizedData)
  }

  /**
   * Delete a campaign
   */
  async deleteCampaign(campaignId: string, userId: string): Promise<DatabaseResponse<boolean>> {
    if (!campaignId || !userId) {
      return {
        data: false,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    // Verify campaign belongs to user
    const campaign = await this.getCampaignById(campaignId, userId)
    if (!campaign.data) {
      return {
        data: false,
        error: new Error('Campaign not found or access denied')
      }
    }

    // Prevent deletion of sent campaigns
    if (campaign.data.status === 'sent') {
      return {
        data: false,
        error: new Error('Cannot delete a campaign that has already been sent')
      }
    }

    // Prevent deletion of campaigns that are currently sending
    if (campaign.data.status === 'sending') {
      return {
        data: false,
        error: new Error('Cannot delete a campaign that is currently being sent')
      }
    }

    return await db.deleteCampaign(campaignId)
  }

  /**
   * Duplicate a campaign
   */
  async duplicateCampaign(campaignId: string, userId: string, newName?: string): Promise<DatabaseResponse<Campaign>> {
    if (!campaignId || !userId) {
      return {
        data: null,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    // Get original campaign
    const originalCampaign = await this.getCampaignById(campaignId, userId)
    if (!originalCampaign.data) {
      return {
        data: null,
        error: new Error('Campaign not found or access denied')
      }
    }

    // Create duplicate campaign data
    const duplicateData: CreateCampaignData = {
      user_id: userId,
      name: newName || `${originalCampaign.data.name} (Copy)`,
      subject: originalCampaign.data.subject,
      html_body: originalCampaign.data.html_body,
      text_body: originalCampaign.data.text_body
    }

    return await this.createCampaign(duplicateData)
  }

  /**
   * Update campaign status
   */
  async updateCampaignStatus(
    campaignId: string,
    userId: string,
    status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed',
    sentAt?: string
  ): Promise<DatabaseResponse<Campaign>> {
    if (!campaignId || !userId) {
      return {
        data: null,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    const updateData: UpdateCampaignData = {
      id: campaignId,
      status
    }

    if (status === 'sent' && sentAt) {
      updateData.sent_at = sentAt
    }

    return await this.updateCampaign(updateData, userId)
  }

  /**
   * Get campaign statistics
   */
  async getCampaignStats(campaignId: string, userId: string): Promise<DatabaseResponse<CampaignStats>> {
    if (!campaignId || !userId) {
      return {
        data: null,
        error: new Error('Campaign ID and User ID are required')
      }
    }

    // Verify campaign belongs to user
    const campaign = await this.getCampaignById(campaignId, userId)
    if (!campaign.data) {
      return {
        data: null,
        error: new Error('Campaign not found or access denied')
      }
    }

    return await db.getCampaignStats(campaignId)
  }

  /**
   * Get campaigns ready to send (scheduled campaigns where scheduled_at <= now)
   */
  async getCampaignsReadyToSend(): Promise<DatabaseListResponse<Campaign>> {
    try {
      const { supabase } = await import('./supabase')
      const { data, error, count } = await supabase
        .from('campaigns')
        .select('*', { count: 'exact' })
        .eq('status', 'scheduled')
        .lte('scheduled_at', new Date().toISOString())
        .order('scheduled_at', { ascending: true })

      return {
        data: data || [],
        error: error as Error | null,
        count: count || 0
      }
    } catch (error) {
      return {
        data: [],
        error: error as Error,
        count: 0
      }
    }
  }

  /**
   * Get campaign statistics for multiple campaigns
   */
  async getCampaignStatsBatch(campaignIds: string[]): Promise<{ [campaignId: string]: CampaignStats }> {
    const stats: { [campaignId: string]: CampaignStats } = {}

    for (const campaignId of campaignIds) {
      const result = await db.getCampaignStats(campaignId)
      if (result.data) {
        stats[campaignId] = result.data
      }
    }

    return stats
  }

  /**
   * Search campaigns by name or subject
   */
  async searchCampaigns(userId: string, query: string, limit = 10): Promise<DatabaseListResponse<Campaign>> {
    if (!userId || !query.trim()) {
      return {
        data: [],
        error: new Error('User ID and search query are required'),
        count: 0
      }
    }

    return await this.getCampaigns(userId, {
      search: query.trim(),
      limit
    })
  }
}

// Export singleton instance
export const campaignService = new CampaignService()