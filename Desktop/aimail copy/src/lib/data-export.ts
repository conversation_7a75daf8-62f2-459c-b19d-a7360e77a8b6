import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'
import { logAuditEvent, AuditAction, ResourceType } from './audit'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface ExportOptions {
  userId: string
  includeContacts?: boolean
  includeCampaigns?: boolean
  includeAnalytics?: boolean
  includeAuditLogs?: boolean
  format?: 'json' | 'csv'
  dateRange?: {
    start: string
    end: string
  }
}

export interface ExportResult {
  filename: string
  data: any
  size: number
  exportedAt: string
}

/**
 * Export user data for compliance (GDPR, etc.)
 */
export async function exportUserData(options: ExportOptions): Promise<ExportResult> {
  const { userId, format = 'json' } = options
  const exportData: any = {
    user: null,
    contacts: [],
    campaigns: [],
    analytics: [],
    auditLogs: [],
    exportedAt: new Date().toISOString()
  }

  try {
    // Export user profile
    const { data: user } = await supabase
      .from('users')
      .select('id, email, created_at, updated_at')
      .eq('id', userId)
      .single()

    exportData.user = user

    // Export contacts if requested
    if (options.includeContacts) {
      let contactsQuery = supabase
        .from('contacts')
        .select('*')
        .eq('user_id', userId)

      if (options.dateRange) {
        contactsQuery = contactsQuery
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end)
      }

      const { data: contacts } = await contactsQuery
      exportData.contacts = contacts || []
    }

    // Export campaigns if requested
    if (options.includeCampaigns) {
      let campaignsQuery = supabase
        .from('campaigns')
        .select(`
          *,
          sends (
            id,
            contact_id,
            status,
            ses_message_id,
            sent_at,
            created_at,
            events (
              id,
              type,
              metadata,
              created_at
            )
          )
        `)
        .eq('user_id', userId)

      if (options.dateRange) {
        campaignsQuery = campaignsQuery
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end)
      }

      const { data: campaigns } = await campaignsQuery
      exportData.campaigns = campaigns || []
    }

    // Export analytics if requested
    if (options.includeAnalytics) {
      // Get aggregated analytics data
      const { data: analytics } = await supabase
        .rpc('get_user_analytics', { user_id_param: userId })

      exportData.analytics = analytics || []
    }

    // Export audit logs if requested
    if (options.includeAuditLogs) {
      let auditQuery = supabase
        .from('audit_logs')
        .select('*')
        .eq('user_id', userId)

      if (options.dateRange) {
        auditQuery = auditQuery
          .gte('created_at', options.dateRange.start)
          .lte('created_at', options.dateRange.end)
      }

      const { data: auditLogs } = await auditQuery
      exportData.auditLogs = auditLogs || []
    }

    // Format data based on requested format
    let formattedData: string
    let filename: string

    if (format === 'csv') {
      formattedData = convertToCSV(exportData)
      filename = `user-data-export-${userId}-${Date.now()}.csv`
    } else {
      formattedData = JSON.stringify(exportData, null, 2)
      filename = `user-data-export-${userId}-${Date.now()}.json`
    }

    // Log the export action
    await logAuditEvent({
      user_id: userId,
      action: AuditAction.DATA_EXPORTED,
      resource_type: ResourceType.USER,
      resource_id: userId,
      details: {
        format,
        includeContacts: options.includeContacts,
        includeCampaigns: options.includeCampaigns,
        includeAnalytics: options.includeAnalytics,
        includeAuditLogs: options.includeAuditLogs,
        dateRange: options.dateRange,
        recordCounts: {
          contacts: exportData.contacts.length,
          campaigns: exportData.campaigns.length,
          analytics: exportData.analytics.length,
          auditLogs: exportData.auditLogs.length
        }
      }
    })

    return {
      filename,
      data: formattedData,
      size: Buffer.byteLength(formattedData, 'utf8'),
      exportedAt: exportData.exportedAt
    }

  } catch (error) {
    console.error('Data export error:', error)
    throw new Error(`Failed to export user data: ${error}`)
  }
}

/**
 * Convert export data to CSV format
 */
function convertToCSV(data: any): string {
  const csvSections: string[] = []

  // User data
  if (data.user) {
    csvSections.push('USER DATA')
    csvSections.push(objectToCSV([data.user]))
    csvSections.push('')
  }

  // Contacts
  if (data.contacts.length > 0) {
    csvSections.push('CONTACTS')
    csvSections.push(objectToCSV(data.contacts))
    csvSections.push('')
  }

  // Campaigns
  if (data.campaigns.length > 0) {
    csvSections.push('CAMPAIGNS')
    const flatCampaigns = data.campaigns.map((campaign: any) => ({
      ...campaign,
      sends: undefined, // Remove nested data for CSV
      total_sends: campaign.sends?.length || 0
    }))
    csvSections.push(objectToCSV(flatCampaigns))
    csvSections.push('')
  }

  // Audit logs
  if (data.auditLogs.length > 0) {
    csvSections.push('AUDIT LOGS')
    csvSections.push(objectToCSV(data.auditLogs))
    csvSections.push('')
  }

  return csvSections.join('\n')
}

/**
 * Convert array of objects to CSV
 */
function objectToCSV(objects: any[]): string {
  if (objects.length === 0) return ''

  const headers = Object.keys(objects[0])
  const csvRows = [headers.join(',')]

  for (const obj of objects) {
    const values = headers.map(header => {
      const value = obj[header]
      if (value === null || value === undefined) return ''
      if (typeof value === 'object') return JSON.stringify(value)
      return `"${String(value).replace(/"/g, '""')}"`
    })
    csvRows.push(values.join(','))
  }

  return csvRows.join('\n')
}

/**
 * Delete user data for compliance (Right to be forgotten)
 */
export async function deleteUserData(userId: string): Promise<void> {
  try {
    // Delete in order to respect foreign key constraints
    await supabase.from('events').delete().in('send_id', 
      supabase.from('sends').select('id').in('campaign_id',
        supabase.from('campaigns').select('id').eq('user_id', userId)
      )
    )

    await supabase.from('sends').delete().in('campaign_id',
      supabase.from('campaigns').select('id').eq('user_id', userId)
    )

    await supabase.from('campaigns').delete().eq('user_id', userId)
    await supabase.from('contacts').delete().eq('user_id', userId)
    await supabase.from('suppression').delete().eq('user_id', userId)
    
    // Keep audit logs for compliance but anonymize them
    await supabase
      .from('audit_logs')
      .update({ 
        user_id: null,
        details: { anonymized: true, original_user_id: userId }
      })
      .eq('user_id', userId)

    // Finally delete the user
    await supabase.from('users').delete().eq('id', userId)

    // Log the deletion (with anonymized user ID)
    await logAuditEvent({
      user_id: 'anonymized',
      action: 'user_data_deleted',
      resource_type: ResourceType.USER,
      resource_id: userId,
      details: {
        original_user_id: userId,
        deleted_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Data deletion error:', error)
    throw new Error(`Failed to delete user data: ${error}`)
  }
}