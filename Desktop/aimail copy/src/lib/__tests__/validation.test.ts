import {
  emailSchema,
  passwordSchema,
  contactSchema,
  campaignSchema,
  loginSchema,
  validateEmail,
  validatePassword,
  getValidationErrors,
  useFormValidation
} from '../validation'
import { z } from 'zod'

describe('Validation Schemas', () => {
  describe('emailSchema', () => {
    test('should validate correct email', () => {
      expect(() => emailSchema.parse('<EMAIL>')).not.toThrow()
    })

    test('should reject invalid email', () => {
      expect(() => emailSchema.parse('invalid-email')).toThrow()
    })
  })

  describe('passwordSchema', () => {
    test('should validate strong password', () => {
      expect(() => passwordSchema.parse('StrongPass123')).not.toThrow()
    })

    test('should reject short password', () => {
      expect(() => passwordSchema.parse('short')).toThrow()
    })

    test('should reject password without uppercase', () => {
      expect(() => passwordSchema.parse('lowercase123')).toThrow()
    })

    test('should reject password without lowercase', () => {
      expect(() => passwordSchema.parse('UPPERCASE123')).toThrow()
    })

    test('should reject password without number', () => {
      expect(() => passwordSchema.parse('NoNumbers')).toThrow()
    })
  })

  describe('contactSchema', () => {
    test('should validate valid contact', () => {
      const contact = {
        email: '<EMAIL>',
        name: 'Test User',
        status: 'active' as const
      }
      expect(() => contactSchema.parse(contact)).not.toThrow()
    })

    test('should validate contact with only email', () => {
      const contact = { email: '<EMAIL>' }
      expect(() => contactSchema.parse(contact)).not.toThrow()
    })

    test('should reject contact without email', () => {
      const contact = { name: 'Test User' }
      expect(() => contactSchema.parse(contact)).toThrow()
    })

    test('should reject contact with invalid status', () => {
      const contact = {
        email: '<EMAIL>',
        status: 'invalid' as any
      }
      expect(() => contactSchema.parse(contact)).toThrow()
    })
  })

  describe('campaignSchema', () => {
    test('should validate valid campaign', () => {
      const campaign = {
        name: 'Test Campaign',
        subject: 'Test Subject',
        html_body: '<p>Test content</p>',
        text_body: 'Test content'
      }
      expect(() => campaignSchema.parse(campaign)).not.toThrow()
    })

    test('should reject campaign without required fields', () => {
      const campaign = { name: 'Test Campaign' }
      expect(() => campaignSchema.parse(campaign)).toThrow()
    })
  })

  describe('loginSchema', () => {
    test('should validate valid login', () => {
      const login = {
        email: '<EMAIL>',
        password: 'password123'
      }
      expect(() => loginSchema.parse(login)).not.toThrow()
    })

    test('should reject login without email', () => {
      const login = { password: 'password123' }
      expect(() => loginSchema.parse(login)).toThrow()
    })

    test('should reject login without password', () => {
      const login = { email: '<EMAIL>' }
      expect(() => loginSchema.parse(login)).toThrow()
    })
  })
})

describe('Validation Helper Functions', () => {
  describe('validateEmail', () => {
    test('should return valid for correct email', () => {
      const result = validateEmail('<EMAIL>')
      expect(result.isValid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    test('should return invalid for incorrect email', () => {
      const result = validateEmail('invalid-email')
      expect(result.isValid).toBe(false)
      expect(result.error).toBeDefined()
    })
  })

  describe('validatePassword', () => {
    test('should return valid for strong password', () => {
      const result = validatePassword('StrongPass123')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('should return invalid with errors for weak password', () => {
      const result = validatePassword('weak')
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('getValidationErrors', () => {
    test('should format ZodError correctly', () => {
      try {
        contactSchema.parse({ email: 'invalid' })
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors = getValidationErrors(error)
          expect(errors.email).toBeDefined()
        }
      }
    })
  })

  describe('useFormValidation', () => {
    test('should validate correct data', () => {
      const { validate } = useFormValidation(contactSchema)
      const result = validate({ email: '<EMAIL>' })
      
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe('<EMAIL>')
      }
    })

    test('should return errors for invalid data', () => {
      const { validate } = useFormValidation(contactSchema)
      const result = validate({ email: 'invalid' })
      
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.errors.email).toBeDefined()
      }
    })

    test('should validate single field', () => {
      const { validateField } = useFormValidation(contactSchema)
      
      // This test might need adjustment based on actual implementation
      // as the current implementation might not work exactly as expected
      const error = validateField('email', 'invalid')
      expect(error).toBeDefined()
    })
  })
})