// Mock environment variables first
jest.mock('../env', () => ({
  env: {
    AWS_REGION: 'us-east-1',
    AWS_ACCESS_KEY_ID: 'test-access-key',
    AWS_SECRET_ACCESS_KEY: 'test-secret-key',
    SES_FROM_EMAIL: '<EMAIL>',
  },
}))

// Mock AWS SDK
jest.mock('@aws-sdk/client-ses', () => {
  const mockSend = jest.fn()
  return {
    SESClient: jest.fn().mockImplementation(() => ({
      send: mockSend,
    })),
    SendEmailCommand: jest.fn().mockImplementation((params) => ({ input: params })),
  }
})

import { EmailService } from '../email'
import { SESClient } from '@aws-sdk/client-ses'

// Get the mock reference
const mockSESClient = SESClient as jest.MockedClass<typeof SESClient>
const mockSend = jest.fn()

describe('EmailService', () => {
  let emailService: EmailService

  beforeEach(() => {
    // Reset mocks
    mockSend.mockReset()
    mockSend.mockResolvedValue({ MessageId: 'test-message-id' })
    mockSESClient.mockImplementation(() => ({
      send: mockSend,
    } as any))
    jest.clearAllMocks()
    emailService = new EmailService()
  })

  describe('sendEmail', () => {
    const mockRecipient = {
      email: '<EMAIL>',
      name: 'Test Recipient',
    }

    const mockTemplate = {
      subject: 'Test Subject',
      htmlBody: '<h1>Test HTML Body</h1>',
      textBody: 'Test Text Body',
    }

    it('should send email successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' })

      const result = await emailService.sendEmail(mockRecipient, mockTemplate)

      expect(result).toEqual({
        messageId: 'test-message-id',
        success: true,
      })

      expect(mockSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Source: '<EMAIL>',
            Destination: expect.objectContaining({
              ToAddresses: ['<EMAIL>'],
            }),
          }),
        })
      )
    })

    it('should handle SES errors', async () => {
      const error = new Error('SES Error')
      mockSend.mockRejectedValue(error)

      const result = await emailService.sendEmail(mockRecipient, mockTemplate)

      expect(result).toEqual({
        messageId: '',
        success: false,
        error: 'SES Error',
      })
    })

    it('should use custom from email when provided', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' })
      const customFrom = '<EMAIL>'

      await emailService.sendEmail(mockRecipient, mockTemplate, customFrom)

      expect(mockSend).toHaveBeenCalledWith(
        expect.objectContaining({
          input: expect.objectContaining({
            Source: customFrom,
          }),
        })
      )
    })

    it('should send email without text body', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' })
      const templateWithoutText = {
        subject: 'Test Subject',
        htmlBody: '<h1>Test HTML Body</h1>',
      }

      const result = await emailService.sendEmail(mockRecipient, templateWithoutText)

      expect(result.success).toBe(true)
    })
  })

  describe('sendBulkEmails', () => {
    const mockRecipients = [
      { email: '<EMAIL>', name: 'User 1' },
      { email: '<EMAIL>', name: 'User 2' },
      { email: '<EMAIL>', name: 'User 3' },
    ]

    const mockTemplate = {
      subject: 'Bulk Test Subject',
      htmlBody: '<h1>Bulk Test HTML Body</h1>',
    }

    it('should send bulk emails successfully', async () => {
      mockSend.mockResolvedValue({ MessageId: 'test-message-id' })

      const result = await emailService.sendBulkEmails(mockRecipients, mockTemplate)

      expect(result).toEqual({
        totalSent: 3,
        totalFailed: 0,
        results: expect.arrayContaining([
          expect.objectContaining({ success: true }),
          expect.objectContaining({ success: true }),
          expect.objectContaining({ success: true }),
        ]),
      })

      expect(mockSend).toHaveBeenCalledTimes(3)
    })

    it('should handle partial failures in bulk send', async () => {
      mockSend
        .mockResolvedValueOnce({ MessageId: 'success-1' })
        .mockRejectedValueOnce(new Error('Failed send'))
        .mockResolvedValueOnce({ MessageId: 'success-2' })

      const result = await emailService.sendBulkEmails(mockRecipients, mockTemplate)

      expect(result).toEqual({
        totalSent: 2,
        totalFailed: 1,
        results: expect.arrayContaining([
          expect.objectContaining({ success: true }),
          expect.objectContaining({ success: false }),
          expect.objectContaining({ success: true }),
        ]),
      })
    })
  })

  describe('renderTemplate', () => {
    it('should render template with variables', () => {
      const template = {
        subject: 'Hello {{name}}!',
        htmlBody: '<h1>Welcome {{name}} to {{company}}!</h1>',
        textBody: 'Welcome {{name}} to {{company}}!',
      }

      const variables = {
        name: 'John Doe',
        company: 'Acme Corp',
      }

      const result = emailService.renderTemplate(template, variables)

      expect(result).toEqual({
        subject: 'Hello John Doe!',
        htmlBody: '<h1>Welcome John Doe to Acme Corp!</h1>',
        textBody: 'Welcome John Doe to Acme Corp!',
      })
    })

    it('should handle template without variables', () => {
      const template = {
        subject: 'Static Subject',
        htmlBody: '<h1>Static HTML</h1>',
      }

      const result = emailService.renderTemplate(template)

      expect(result).toEqual(template)
    })
  })

  describe('generateEmailWithTracking', () => {
    const trackingData = {
      sendId: 'test-send-id',
      baseUrl: 'https://example.com',
    }

    it('should add tracking pixel to HTML with body tag', () => {
      const htmlBody = '<html><body><h1>Test</h1></body></html>'

      const result = emailService.generateEmailWithTracking(htmlBody, trackingData)

      expect(result).toContain('src="https://example.com/api/track/open?send_id=test-send-id"')
      expect(result).toContain('width="1" height="1"')
      expect(result).toContain('style="display:none;"')
      expect(result.indexOf('</body>')).toBeGreaterThan(result.indexOf('track/open'))
    })

    it('should convert links to tracking links', () => {
      const htmlBody = `
        <html>
          <body>
            <a href="https://example.com/page1">Link 1</a>
            <a href="https://example.com/page2" class="button">Link 2</a>
          </body>
        </html>
      `

      const result = emailService.generateEmailWithTracking(htmlBody, trackingData)

      expect(result).toContain('href="https://example.com/api/track/click?send_id=test-send-id&url=https%3A%2F%2Fexample.com%2Fpage1"')
      expect(result).toContain('href="https://example.com/api/track/click?send_id=test-send-id&url=https%3A%2F%2Fexample.com%2Fpage2"')
    })
  })

  describe('getRateLimitStatus', () => {
    it('should return current rate limit status', () => {
      const status = emailService.getRateLimitStatus()

      expect(status).toEqual({
        currentSendRate: expect.any(Number),
        maxSendRate: 14,
        dailySendCount: expect.any(Number),
        maxDailyQuota: 200,
        timeUntilReset: expect.any(Number),
      })
    })
  })
})