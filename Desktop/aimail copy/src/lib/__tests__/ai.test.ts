import { AIService, AIGenerationRequest } from '../ai'

// Mock fetch globally
global.fetch = jest.fn()

describe('AI Service', () => {
  let aiService: AIService

  beforeEach(() => {
    jest.clearAllMocks()
    // Set up environment variable
    process.env.PERPLEXITY_API_KEY = 'test-api-key'
    aiService = new AIService()
  })

  afterEach(() => {
    delete process.env.PERPLEXITY_API_KEY
  })

  describe('generateEmailContent', () => {
    const mockRequest: AIGenerationRequest = {
      productOrOffer: 'New fitness app',
      goal: 'Increase app downloads',
      targetAudience: 'Fitness enthusiasts',
      tone: 'professional'
    }

    it('should generate email content successfully', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              subjects: [
                'Transform Your Fitness Journey Today',
                'Get Fit with Our Revolutionary App',
                'Your Personal Trainer in Your Pocket'
              ],
              bodies: [
                '<h1>Welcome to FitApp</h1><p>Start your fitness journey today!</p>',
                '<h2>Ready to Get Fit?</h2><p>Download our app and transform your life.</p>'
              ]
            })
          }
        }]
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await aiService.generateEmailContent(mockRequest)

      expect(result).toEqual({
        subjects: [
          'Transform Your Fitness Journey Today',
          'Get Fit with Our Revolutionary App',
          'Your Personal Trainer in Your Pocket'
        ],
        bodies: [
          '<h1>Welcome to FitApp</h1><p>Start your fitness journey today!</p>',
          '<h2>Ready to Get Fit?</h2><p>Download our app and transform your life.</p>'
        ]
      })

      expect(fetch).toHaveBeenCalledWith(
        'https://api.perplexity.ai/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
          }),
          body: expect.stringContaining('New fitness app')
        })
      )
    })

    it('should handle API errors gracefully', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      })

      await expect(aiService.generateEmailContent(mockRequest))
        .rejects.toThrow('Failed to generate AI content. Please try again.')
    })

    it('should handle network errors', async () => {
      ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      await expect(aiService.generateEmailContent(mockRequest))
        .rejects.toThrow('Failed to generate AI content. Please try again.')
    })

    it('should handle malformed API responses', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Invalid JSON response'
          }
        }]
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await aiService.generateEmailContent(mockRequest)

      // Should return fallback content
      expect(result).toEqual({
        subjects: ['Generated Subject Line'],
        bodies: ['<p>Generated email content will appear here.</p>']
      })
    })

    it('should limit subjects to 3 and bodies to 2', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              subjects: [
                'Subject 1', 'Subject 2', 'Subject 3', 'Subject 4', 'Subject 5'
              ],
              bodies: [
                '<p>Body 1</p>', '<p>Body 2</p>', '<p>Body 3</p>'
              ]
            })
          }
        }]
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const result = await aiService.generateEmailContent(mockRequest)

      expect(result.subjects).toHaveLength(3)
      expect(result.bodies).toHaveLength(2)
    })

    it('should handle missing API key', () => {
      delete process.env.PERPLEXITY_API_KEY
      process.env.NODE_ENV = 'production'
      
      expect(() => {
        // Clear the module cache and require fresh
        delete require.cache[require.resolve('../ai')]
        const { AIService } = require('../ai')
        new AIService()
      }).toThrow('PERPLEXITY_API_KEY environment variable is required')
      
      // Reset NODE_ENV
      process.env.NODE_ENV = 'test'
    })

    it('should build correct prompt with all parameters', async () => {
      const fullRequest: AIGenerationRequest = {
        productOrOffer: 'Premium subscription service',
        goal: 'Convert free users to paid',
        targetAudience: 'Small business owners',
        tone: 'friendly'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                subjects: ['Test Subject'],
                bodies: ['<p>Test Body</p>']
              })
            }
          }]
        })
      })

      await aiService.generateEmailContent(fullRequest)

      const fetchCall = (fetch as jest.Mock).mock.calls[0]
      const requestBody = JSON.parse(fetchCall[1].body)
      const prompt = requestBody.messages[1].content

      expect(prompt).toContain('Premium subscription service')
      expect(prompt).toContain('Convert free users to paid')
      expect(prompt).toContain('Small business owners')
      expect(prompt).toContain('friendly')
    })

    it('should use default values for optional parameters', async () => {
      const minimalRequest: AIGenerationRequest = {
        productOrOffer: 'Test product',
        goal: 'Test goal'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                subjects: ['Test Subject'],
                bodies: ['<p>Test Body</p>']
              })
            }
          }]
        })
      })

      await aiService.generateEmailContent(minimalRequest)

      const fetchCall = (fetch as jest.Mock).mock.calls[0]
      const requestBody = JSON.parse(fetchCall[1].body)
      const prompt = requestBody.messages[1].content

      expect(prompt).toContain('General audience')
      expect(prompt).toContain('professional')
    })
  })
})