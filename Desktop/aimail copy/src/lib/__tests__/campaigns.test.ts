import { campaignService, campaignValidation } from '../campaigns'
import { db } from '../database'
import type { Campaign, CreateCampaignData, UpdateCampaignData, CampaignStats } from '../../types/database'

// Mock the database
jest.mock('../database', () => ({
  db: {
    getCampaigns: jest.fn(),
    getCampaignById: jest.fn(),
    createCampaign: jest.fn(),
    updateCampaign: jest.fn(),
    deleteCampaign: jest.fn(),
    getCampaignStats: jest.fn()
  }
}))

const mockDb = db as jest.Mocked<typeof db>

describe('Campaign Validation', () => {
  describe('validateName', () => {
    it('should validate campaign names correctly', () => {
      expect(campaignValidation.validateName('Valid Campaign')).toBe(true)
      expect(campaignValidation.validateName('A')).toBe(true)
      expect(campaignValidation.validateName('A'.repeat(255))).toBe(true)
      expect(campaignValidation.validateName('')).toBe(false)
      expect(campaignValidation.validateName('   ')).toBe(false)
      expect(campaignValidation.validateName('A'.repeat(256))).toBe(false)
    })
  })

  describe('validateSubject', () => {
    it('should validate subject lines correctly', () => {
      expect(campaignValidation.validateSubject('Valid Subject')).toBe(true)
      expect(campaignValidation.validateSubject('A')).toBe(true)
      expect(campaignValidation.validateSubject('A'.repeat(255))).toBe(true)
      expect(campaignValidation.validateSubject('')).toBe(false)
      expect(campaignValidation.validateSubject('   ')).toBe(false)
      expect(campaignValidation.validateSubject('A'.repeat(256))).toBe(false)
    })
  })

  describe('validateHtmlBody', () => {
    it('should validate HTML body correctly', () => {
      expect(campaignValidation.validateHtmlBody('<p>Valid HTML</p>')).toBe(true)
      expect(campaignValidation.validateHtmlBody('Plain text')).toBe(true)
      expect(campaignValidation.validateHtmlBody('')).toBe(false)
      expect(campaignValidation.validateHtmlBody('   ')).toBe(false)
    })
  })

  describe('validateStatus', () => {
    it('should validate campaign status correctly', () => {
      expect(campaignValidation.validateStatus('draft')).toBe(true)
      expect(campaignValidation.validateStatus('scheduled')).toBe(true)
      expect(campaignValidation.validateStatus('sending')).toBe(true)
      expect(campaignValidation.validateStatus('sent')).toBe(true)
      expect(campaignValidation.validateStatus('failed')).toBe(true)
      expect(campaignValidation.validateStatus('invalid')).toBe(false)
      expect(campaignValidation.validateStatus('')).toBe(false)
    })
  })

  describe('validateScheduledAt', () => {
    it('should validate scheduled dates correctly', () => {
      const futureDate = new Date(Date.now() + 86400000).toISOString() // Tomorrow
      const pastDate = new Date(Date.now() - 86400000).toISOString() // Yesterday
      
      expect(campaignValidation.validateScheduledAt(futureDate)).toBe(true)
      expect(campaignValidation.validateScheduledAt(undefined)).toBe(true)
      expect(campaignValidation.validateScheduledAt(pastDate)).toBe(false)
      expect(campaignValidation.validateScheduledAt('invalid-date')).toBe(false)
    })
  })

  describe('validateCampaignData', () => {
    const validCampaignData: CreateCampaignData = {
      user_id: 'user-123',
      name: 'Test Campaign',
      subject: 'Test Subject',
      html_body: '<p>Test HTML</p>'
    }

    it('should validate complete campaign data', () => {
      const result = campaignValidation.validateCampaignData(validCampaignData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject missing required fields', () => {
      const result = campaignValidation.validateCampaignData({})
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Campaign name is required')
      expect(result.errors).toContain('Subject line is required')
      expect(result.errors).toContain('HTML body is required')
      expect(result.errors).toContain('User ID is required')
    })

    it('should reject invalid field values', () => {
      const invalidData = {
        user_id: 'user-123',
        name: '',
        subject: '',
        html_body: '',
        text_body: '',
        scheduled_at: new Date(Date.now() - 86400000).toISOString()
      }
      
      const result = campaignValidation.validateCampaignData(invalidData)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('sanitizeCampaignData', () => {
    it('should sanitize campaign data correctly', () => {
      const dirtyData: CreateCampaignData = {
        user_id: 'user-123',
        name: '  Test Campaign  ',
        subject: '  Test Subject  ',
        html_body: '  <p>Test HTML</p>  ',
        text_body: '  Test text  '
      }

      const sanitized = campaignValidation.sanitizeCampaignData(dirtyData)
      expect(sanitized.name).toBe('Test Campaign')
      expect(sanitized.subject).toBe('Test Subject')
      expect(sanitized.html_body).toBe('<p>Test HTML</p>')
      expect(sanitized.text_body).toBe('Test text')
    })
  })
})

describe('Campaign Service', () => {
  const mockUser = { id: 'user-123', email: '<EMAIL>' }
  const mockCampaign: Campaign = {
    id: 'campaign-123',
    user_id: 'user-123',
    name: 'Test Campaign',
    subject: 'Test Subject',
    html_body: '<p>Test HTML</p>',
    status: 'draft',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getCampaigns', () => {
    it('should get campaigns successfully', async () => {
      mockDb.getCampaigns.mockResolvedValue({
        data: [mockCampaign],
        error: null,
        count: 1
      })

      const result = await campaignService.getCampaigns(mockUser.id)
      
      expect(result.data).toEqual([mockCampaign])
      expect(result.error).toBeNull()
      expect(mockDb.getCampaigns).toHaveBeenCalledWith(mockUser.id, undefined)
    })

    it('should require user ID', async () => {
      const result = await campaignService.getCampaigns('')
      
      expect(result.data).toEqual([])
      expect(result.error?.message).toBe('User ID is required')
      expect(mockDb.getCampaigns).not.toHaveBeenCalled()
    })

    it('should pass filters to database', async () => {
      const filters = { status: 'draft' as const, limit: 10 }
      mockDb.getCampaigns.mockResolvedValue({
        data: [],
        error: null,
        count: 0
      })

      await campaignService.getCampaigns(mockUser.id, filters)
      
      expect(mockDb.getCampaigns).toHaveBeenCalledWith(mockUser.id, filters)
    })
  })

  describe('getCampaignById', () => {
    it('should get campaign by ID successfully', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })

      const result = await campaignService.getCampaignById('campaign-123', mockUser.id)
      
      expect(result.data).toEqual(mockCampaign)
      expect(result.error).toBeNull()
    })

    it('should require campaign ID and user ID', async () => {
      const result1 = await campaignService.getCampaignById('', mockUser.id)
      const result2 = await campaignService.getCampaignById('campaign-123', '')
      
      expect(result1.error?.message).toBe('Campaign ID and User ID are required')
      expect(result2.error?.message).toBe('Campaign ID and User ID are required')
    })

    it('should deny access to campaigns from other users', async () => {
      const otherUserCampaign = { ...mockCampaign, user_id: 'other-user' }
      mockDb.getCampaignById.mockResolvedValue({
        data: otherUserCampaign,
        error: null
      })

      const result = await campaignService.getCampaignById('campaign-123', mockUser.id)
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toBe('Campaign not found or access denied')
    })
  })

  describe('createCampaign', () => {
    const validCampaignData: CreateCampaignData = {
      user_id: mockUser.id,
      name: 'Test Campaign',
      subject: 'Test Subject',
      html_body: '<p>Test HTML</p>'
    }

    it('should create campaign successfully', async () => {
      mockDb.createCampaign.mockResolvedValue({
        data: mockCampaign,
        error: null
      })

      const result = await campaignService.createCampaign(validCampaignData)
      
      expect(result.data).toEqual(mockCampaign)
      expect(result.error).toBeNull()
      expect(mockDb.createCampaign).toHaveBeenCalledWith(validCampaignData)
    })

    it('should validate campaign data before creation', async () => {
      const invalidData = { user_id: mockUser.id } as CreateCampaignData

      const result = await campaignService.createCampaign(invalidData)
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toContain('Validation failed')
      expect(mockDb.createCampaign).not.toHaveBeenCalled()
    })

    it('should sanitize campaign data', async () => {
      const dirtyData: CreateCampaignData = {
        user_id: mockUser.id,
        name: '  Test Campaign  ',
        subject: '  Test Subject  ',
        html_body: '  <p>Test HTML</p>  '
      }

      mockDb.createCampaign.mockResolvedValue({
        data: mockCampaign,
        error: null
      })

      await campaignService.createCampaign(dirtyData)
      
      expect(mockDb.createCampaign).toHaveBeenCalledWith({
        user_id: mockUser.id,
        name: 'Test Campaign',
        subject: 'Test Subject',
        html_body: '<p>Test HTML</p>'
      })
    })
  })

  describe('updateCampaign', () => {
    const updateData: UpdateCampaignData = {
      id: 'campaign-123',
      name: 'Updated Campaign'
    }

    it('should update campaign successfully', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })
      mockDb.updateCampaign.mockResolvedValue({
        data: { ...mockCampaign, name: 'Updated Campaign' },
        error: null
      })

      const result = await campaignService.updateCampaign(updateData, mockUser.id)
      
      expect(result.data?.name).toBe('Updated Campaign')
      expect(result.error).toBeNull()
    })

    it('should require campaign ID and user ID', async () => {
      const result1 = await campaignService.updateCampaign({ id: '' } as UpdateCampaignData, mockUser.id)
      const result2 = await campaignService.updateCampaign(updateData, '')
      
      expect(result1.error?.message).toBe('Campaign ID and User ID are required')
      expect(result2.error?.message).toBe('Campaign ID and User ID are required')
    })

    it('should prevent updates to sent campaigns', async () => {
      const sentCampaign = { ...mockCampaign, status: 'sent' as const }
      mockDb.getCampaignById.mockResolvedValue({
        data: sentCampaign,
        error: null
      })

      const result = await campaignService.updateCampaign(updateData, mockUser.id)
      
      expect(result.error?.message).toBe('Cannot update a campaign that has already been sent')
    })

    it('should prevent updates to campaigns being sent', async () => {
      const sendingCampaign = { ...mockCampaign, status: 'sending' as const }
      mockDb.getCampaignById.mockResolvedValue({
        data: sendingCampaign,
        error: null
      })

      const result = await campaignService.updateCampaign(updateData, mockUser.id)
      
      expect(result.error?.message).toBe('Cannot update a campaign that is currently being sent')
    })
  })

  describe('deleteCampaign', () => {
    it('should delete campaign successfully', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })
      mockDb.deleteCampaign.mockResolvedValue({
        data: true,
        error: null
      })

      const result = await campaignService.deleteCampaign('campaign-123', mockUser.id)
      
      expect(result.data).toBe(true)
      expect(result.error).toBeNull()
    })

    it('should prevent deletion of sent campaigns', async () => {
      const sentCampaign = { ...mockCampaign, status: 'sent' as const }
      mockDb.getCampaignById.mockResolvedValue({
        data: sentCampaign,
        error: null
      })

      const result = await campaignService.deleteCampaign('campaign-123', mockUser.id)
      
      expect(result.error?.message).toBe('Cannot delete a campaign that has already been sent')
    })
  })

  describe('duplicateCampaign', () => {
    it('should duplicate campaign successfully', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })
      mockDb.createCampaign.mockResolvedValue({
        data: { ...mockCampaign, id: 'new-campaign-123', name: 'Test Campaign (Copy)' },
        error: null
      })

      const result = await campaignService.duplicateCampaign('campaign-123', mockUser.id)
      
      expect(result.data?.name).toBe('Test Campaign (Copy)')
      expect(result.error).toBeNull()
    })

    it('should use custom name when provided', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })
      mockDb.createCampaign.mockResolvedValue({
        data: { ...mockCampaign, id: 'new-campaign-123', name: 'Custom Name' },
        error: null
      })

      const result = await campaignService.duplicateCampaign('campaign-123', mockUser.id, 'Custom Name')
      
      expect(mockDb.createCampaign).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'Custom Name' })
      )
    })
  })

  describe('getCampaignStats', () => {
    const mockStats: CampaignStats = {
      campaign_id: 'campaign-123',
      total_sends: 100,
      successful_sends: 95,
      failed_sends: 5,
      bounced_sends: 2,
      opens: 50,
      clicks: 10,
      open_rate: 52.63,
      click_rate: 10.53,
      bounce_rate: 2.0
    }

    it('should get campaign statistics successfully', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: mockCampaign,
        error: null
      })
      mockDb.getCampaignStats.mockResolvedValue({
        data: mockStats,
        error: null
      })

      const result = await campaignService.getCampaignStats('campaign-123', mockUser.id)
      
      expect(result.data).toEqual(mockStats)
      expect(result.error).toBeNull()
    })

    it('should verify campaign ownership before returning stats', async () => {
      mockDb.getCampaignById.mockResolvedValue({
        data: null,
        error: new Error('Campaign not found')
      })

      const result = await campaignService.getCampaignStats('campaign-123', mockUser.id)
      
      expect(result.error?.message).toBe('Campaign not found or access denied')
      expect(mockDb.getCampaignStats).not.toHaveBeenCalled()
    })
  })
})