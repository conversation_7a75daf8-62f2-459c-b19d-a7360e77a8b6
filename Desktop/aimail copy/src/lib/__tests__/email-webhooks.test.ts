import {
  handleBounceNotification,
  handleComplaintNotification,
  getSuppressionList,
  isEmailSuppressed,
  addToSuppressionList,
  removeFromSuppressionList,
  getWebhookStats,
  type SESBounceNotification,
  type SESComplaintNotification
} from '../email-webhooks'
import { supabaseAdmin } from '../supabase'

// Mock Supabase
jest.mock('../supabase', () => ({
  supabaseAdmin: {
    from: jest.fn()
  }
}))

const mockSupabaseAdmin = supabaseAdmin as jest.Mocked<typeof supabaseAdmin>

describe('Email Webhooks', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    console.log = jest.fn()
    console.error = jest.fn()
  })

  describe('handleBounceNotification', () => {
    const mockBounceNotification: SESBounceNotification = {
      notificationType: 'Bounce',
      bounce: {
        bounceType: 'Permanent',
        bounceSubType: 'General',
        bouncedRecipients: [
          {
            emailAddress: '<EMAIL>',
            action: 'failed',
            status: '5.1.1',
            diagnosticCode: 'smtp; 550 5.1.1 User unknown'
          }
        ],
        timestamp: '2023-01-01T12:00:00.000Z',
        feedbackId: 'bounce-feedback-id'
      },
      mail: {
        timestamp: '2023-01-01T11:59:00.000Z',
        messageId: 'ses-message-id-123',
        source: '<EMAIL>',
        destination: ['<EMAIL>']
      }
    }

    const mockSendRecord = {
      id: 'send-id-123',
      campaign_id: 'campaign-id-123',
      contact_id: 'contact-id-123',
      contacts: {
        email: '<EMAIL>',
        user_id: 'user-id-123'
      }
    }

    it('should handle permanent bounce notification successfully', async () => {
      // Mock database responses
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        upsert: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSendRecord, error: null }),
        onConflict: jest.fn().mockResolvedValue({ data: null, error: null })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await handleBounceNotification(mockBounceNotification)

      // Verify send record was queried
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('sends')
      
      // Verify bounce event was created
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('events')
      
      // Verify contact status was updated
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('contacts')
      
      // Verify suppression list was updated
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('suppression')
    })

    it('should handle transient bounce without suppression', async () => {
      const transientBounce = {
        ...mockBounceNotification,
        bounce: {
          ...mockBounceNotification.bounce,
          bounceType: 'Transient' as const
        }
      }

      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSendRecord, error: null })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await handleBounceNotification(transientBounce)

      // Should create event but not update suppression for transient bounces
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('events')
    })

    it('should handle missing send record gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await expect(handleBounceNotification(mockBounceNotification)).resolves.not.toThrow()
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Send record not found'),
        expect.any(Object)
      )
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockRejectedValue(new Error('Database error'))
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await expect(handleBounceNotification(mockBounceNotification)).rejects.toThrow('Database error')
      expect(console.error).toHaveBeenCalledWith(
        'Error handling bounce notification:',
        expect.any(Error)
      )
    })
  })

  describe('handleComplaintNotification', () => {
    const mockComplaintNotification: SESComplaintNotification = {
      notificationType: 'Complaint',
      complaint: {
        complainedRecipients: [
          {
            emailAddress: '<EMAIL>'
          }
        ],
        timestamp: '2023-01-01T12:00:00.000Z',
        feedbackId: 'complaint-feedback-id',
        complaintFeedbackType: 'abuse'
      },
      mail: {
        timestamp: '2023-01-01T11:59:00.000Z',
        messageId: 'ses-message-id-456',
        source: '<EMAIL>',
        destination: ['<EMAIL>']
      }
    }

    const mockSendRecord = {
      id: 'send-id-456',
      campaign_id: 'campaign-id-456',
      contact_id: 'contact-id-456',
      contacts: {
        email: '<EMAIL>',
        user_id: 'user-id-456'
      }
    }

    it('should handle complaint notification successfully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        upsert: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockSendRecord, error: null }),
        onConflict: jest.fn().mockResolvedValue({ data: null, error: null })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await handleComplaintNotification(mockComplaintNotification)

      // Verify complaint event was created
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('events')
      
      // Verify contact status was updated to unsubscribed
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('contacts')
      
      // Verify suppression list was updated
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('suppression')
    })

    it('should handle missing send record gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { message: 'Not found' } })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      await expect(handleComplaintNotification(mockComplaintNotification)).resolves.not.toThrow()
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Send record not found'),
        expect.any(Object)
      )
    })
  })

  describe('getSuppressionList', () => {
    it('should return suppression list for user', async () => {
      const mockSuppressionData = [
        {
          email: '<EMAIL>',
          reason: 'bounce',
          created_at: '2023-01-01T12:00:00.000Z'
        },
        {
          email: '<EMAIL>',
          reason: 'complaint',
          created_at: '2023-01-01T11:00:00.000Z'
        }
      ]

      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: mockSuppressionData, error: null })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await getSuppressionList('user-id-123')

      expect(result).toEqual(mockSuppressionData)
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('suppression')
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({ data: null, error: { message: 'Database error' } })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await getSuppressionList('user-id-123')

      expect(result).toEqual([])
      expect(console.error).toHaveBeenCalledWith(
        'Error fetching suppression list:',
        expect.any(Object)
      )
    })
  })

  describe('isEmailSuppressed', () => {
    it('should return true for suppressed email', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: { id: 'suppression-id' }, error: null })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await isEmailSuppressed('user-id-123', '<EMAIL>')

      expect(result).toBe(true)
    })

    it('should return false for non-suppressed email', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } })
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await isEmailSuppressed('user-id-123', '<EMAIL>')

      expect(result).toBe(false)
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockRejectedValue(new Error('Database error'))
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await isEmailSuppressed('user-id-123', '<EMAIL>')

      expect(result).toBe(false)
      expect(console.error).toHaveBeenCalledWith(
        'Error in isEmailSuppressed:',
        expect.any(Error)
      )
    })
  })

  describe('addToSuppressionList', () => {
    it('should add email to suppression list successfully', async () => {
      let callCount = 0
      const mockFrom = jest.fn((table) => {
        callCount++
        if (table === 'suppression') {
          return {
            upsert: jest.fn(() => ({
              onConflict: jest.fn().mockResolvedValue({ data: null, error: null })
            }))
          }
        } else if (table === 'contacts') {
          return {
            update: jest.fn(() => ({
              eq: jest.fn(() => ({
                eq: jest.fn().mockResolvedValue({ data: null, error: null })
              }))
            }))
          }
        }
      })

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await addToSuppressionList('user-id-123', '<EMAIL>', 'bounce')

      expect(result).toBe(true)
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('suppression')
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('contacts')
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        upsert: jest.fn(() => ({
          onConflict: jest.fn().mockResolvedValue({ data: null, error: { message: 'Database error' } })
        }))
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await addToSuppressionList('user-id-123', '<EMAIL>', 'bounce')

      expect(result).toBe(false)
      expect(console.error).toHaveBeenCalled()
    })
  })

  describe('removeFromSuppressionList', () => {
    it('should remove email from suppression list successfully', async () => {
      const mockFrom = jest.fn((table) => {
        if (table === 'suppression') {
          return {
            delete: jest.fn(() => ({
              eq: jest.fn(() => ({
                eq: jest.fn().mockResolvedValue({ data: null, error: null })
              }))
            }))
          }
        } else if (table === 'contacts') {
          return {
            update: jest.fn(() => ({
              eq: jest.fn(() => ({
                eq: jest.fn().mockResolvedValue({ data: null, error: null })
              }))
            }))
          }
        }
      })

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await removeFromSuppressionList('user-id-123', '<EMAIL>')

      expect(result).toBe(true)
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('suppression')
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith('contacts')
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => ({
        delete: jest.fn(() => ({
          eq: jest.fn(() => ({
            eq: jest.fn().mockResolvedValue({ data: null, error: { message: 'Database error' } })
          }))
        }))
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await removeFromSuppressionList('user-id-123', '<EMAIL>')

      expect(result).toBe(false)
      expect(console.error).toHaveBeenCalledWith(
        'Error removing from suppression list:',
        expect.any(Object)
      )
    })
  })

  describe('getWebhookStats', () => {
    it('should return webhook statistics', async () => {
      const mockFrom = jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        count: 5
      }))

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await getWebhookStats('user-id-123')

      expect(result).toEqual({
        bounces: 5,
        complaints: 5,
        timestamp: expect.any(String)
      })
    })

    it('should handle database errors gracefully', async () => {
      const mockFrom = jest.fn(() => {
        throw new Error('Database error')
      })

      mockSupabaseAdmin.from.mockImplementation(mockFrom)

      const result = await getWebhookStats('user-id-123')

      expect(result).toEqual({
        bounces: 0,
        complaints: 0,
        timestamp: expect.any(String)
      })
      expect(console.error).toHaveBeenCalledWith(
        'Error getting webhook stats:',
        expect.any(Error)
      )
    })
  })
})