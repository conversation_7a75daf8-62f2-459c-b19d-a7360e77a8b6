import { EmailService } from '../email'

// Mock environment variables
process.env.AWS_REGION = 'us-east-1'
process.env.AWS_ACCESS_KEY_ID = 'test-key'
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret'
process.env.SES_FROM_EMAIL = '<EMAIL>'

describe('EmailService Basic Tests', () => {
  test('should create EmailService instance', () => {
    const emailService = new EmailService()
    expect(emailService).toBeDefined()
  })

  test('should render template with variables', () => {
    const emailService = new EmailService()
    const template = {
      subject: 'Hello {{name}}!',
      htmlBody: '<h1>Welcome {{name}}!</h1>',
    }
    const variables = { name: 'John' }
    
    const result = emailService.renderTemplate(template, variables)
    
    expect(result.subject).toBe('Hello John!')
    expect(result.htmlBody).toBe('<h1>Welcome John!</h1>')
  })

  test('should add tracking to email', () => {
    const emailService = new EmailService()
    const htmlBody = '<html><body><h1>Test</h1></body></html>'
    const trackingData = {
      sendId: 'test-123',
      baseUrl: 'https://example.com',
    }
    
    const result = emailService.generateEmailWithTracking(htmlBody, trackingData)
    
    expect(result).toContain('track/open?send_id=test-123')
  })

  test('should return rate limit status', () => {
    const emailService = new EmailService()
    const status = emailService.getRateLimitStatus()
    
    expect(status.maxSendRate).toBe(14)
    expect(status.maxDailyQuota).toBe(200)
  })
})