import { contactService, contactValidation } from '../contacts'
import { db } from '../database'
import type { Contact, CreateContactData, UpdateContactData } from '../../types/database'

// Mock the database service
jest.mock('../database', () => ({
  db: {
    getContacts: jest.fn(),
    createContact: jest.fn(),
    updateContact: jest.fn(),
    deleteContact: jest.fn(),
    isEmailSuppressed: jest.fn(),
    updateSuppressionList: jest.fn()
  }
}))

const mockDb = db as jest.Mocked<typeof db>

describe('contactValidation', () => {
  describe('validateEmail', () => {
    it('should validate correct email formats', () => {
      expect(contactValidation.validateEmail('<EMAIL>')).toBe(true)
      expect(contactValidation.validateEmail('<EMAIL>')).toBe(true)
      expect(contactValidation.validateEmail('<EMAIL>')).toBe(true)
    })

    it('should reject invalid email formats', () => {
      expect(contactValidation.validateEmail('invalid-email')).toBe(false)
      expect(contactValidation.validateEmail('test@')).toBe(false)
      expect(contactValidation.validateEmail('@domain.com')).toBe(false)
      expect(contactValidation.validateEmail('')).toBe(false)
    })
  })

  describe('validateName', () => {
    it('should validate correct names', () => {
      expect(contactValidation.validateName('John Doe')).toBe(true)
      expect(contactValidation.validateName('Jane')).toBe(true)
      expect(contactValidation.validateName(undefined)).toBe(true) // Name is optional
    })

    it('should reject invalid names', () => {
      expect(contactValidation.validateName('')).toBe(false)
      expect(contactValidation.validateName('   ')).toBe(false)
      expect(contactValidation.validateName('a'.repeat(256))).toBe(false)
    })
  })

  describe('validateStatus', () => {
    it('should validate correct statuses', () => {
      expect(contactValidation.validateStatus('active')).toBe(true)
      expect(contactValidation.validateStatus('unsubscribed')).toBe(true)
      expect(contactValidation.validateStatus('bounced')).toBe(true)
    })

    it('should reject invalid statuses', () => {
      expect(contactValidation.validateStatus('invalid')).toBe(false)
      expect(contactValidation.validateStatus('pending')).toBe(false)
      expect(contactValidation.validateStatus('')).toBe(false)
    })
  })

  describe('validateContactData', () => {
    it('should validate correct contact data', () => {
      const data: CreateContactData = {
        user_id: 'user-123',
        email: '<EMAIL>',
        name: 'John Doe',
        status: 'active'
      }

      const result = contactValidation.validateContactData(data)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject data with missing required fields', () => {
      const data = {
        name: 'John Doe'
      } as CreateContactData

      const result = contactValidation.validateContactData(data)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Email is required')
      expect(result.errors).toContain('User ID is required')
    })

    it('should reject data with invalid email', () => {
      const data: CreateContactData = {
        user_id: 'user-123',
        email: 'invalid-email',
        name: 'John Doe'
      }

      const result = contactValidation.validateContactData(data)
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Invalid email format')
    })
  })

  describe('sanitizeContactData', () => {
    it('should sanitize contact data correctly', () => {
      const data: CreateContactData = {
        user_id: 'user-123',
        email: '  <EMAIL>  ',
        name: '  John Doe  '
      }

      const sanitized = contactValidation.sanitizeContactData(data)
      expect(sanitized.email).toBe('<EMAIL>')
      expect(sanitized.name).toBe('John Doe')
      expect(sanitized.status).toBe('active')
    })
  })
})

describe('ContactService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getContacts', () => {
    it('should get contacts successfully', async () => {
      const mockContacts: Contact[] = [
        {
          id: 'contact-1',
          user_id: 'user-123',
          email: '<EMAIL>',
          name: 'John Doe',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ]

      mockDb.getContacts.mockResolvedValue({
        data: mockContacts,
        error: null,
        count: 1
      })

      const result = await contactService.getContacts('user-123')
      
      expect(result.data).toEqual(mockContacts)
      expect(result.error).toBeNull()
      expect(mockDb.getContacts).toHaveBeenCalledWith('user-123', undefined)
    })

    it('should return error for missing user ID', async () => {
      const result = await contactService.getContacts('')
      
      expect(result.data).toEqual([])
      expect(result.error?.message).toBe('User ID is required')
      expect(mockDb.getContacts).not.toHaveBeenCalled()
    })
  })

  describe('createContact', () => {
    it('should create contact successfully', async () => {
      const contactData: CreateContactData = {
        user_id: 'user-123',
        email: '<EMAIL>',
        name: 'John Doe',
        status: 'active'
      }

      const mockContact: Contact = {
        id: 'contact-1',
        ...contactData,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [],
        error: null,
        count: 0
      })
      mockDb.isEmailSuppressed.mockResolvedValue(false)
      mockDb.createContact.mockResolvedValue({
        data: mockContact,
        error: null
      })

      const result = await contactService.createContact(contactData)
      
      expect(result.data).toEqual(mockContact)
      expect(result.error).toBeNull()
      expect(mockDb.createContact).toHaveBeenCalledWith({
        ...contactData,
        email: '<EMAIL>' // Should be sanitized
      })
    })

    it('should reject invalid contact data', async () => {
      const contactData = {
        user_id: 'user-123',
        email: 'invalid-email'
      } as CreateContactData

      const result = await contactService.createContact(contactData)
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toContain('Validation failed')
      expect(mockDb.createContact).not.toHaveBeenCalled()
    })

    it('should reject duplicate email', async () => {
      const contactData: CreateContactData = {
        user_id: 'user-123',
        email: '<EMAIL>'
      }

      const existingContact: Contact = {
        id: 'contact-1',
        user_id: 'user-123',
        email: '<EMAIL>',
        status: 'active',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [existingContact],
        error: null,
        count: 1
      })

      const result = await contactService.createContact(contactData)
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toBe('Contact with this email already exists')
      expect(mockDb.createContact).not.toHaveBeenCalled()
    })

    it('should reject suppressed email', async () => {
      const contactData: CreateContactData = {
        user_id: 'user-123',
        email: '<EMAIL>'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [],
        error: null,
        count: 0
      })
      mockDb.isEmailSuppressed.mockResolvedValue(true)

      const result = await contactService.createContact(contactData)
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toBe('Cannot add contact: email is in suppression list')
      expect(mockDb.createContact).not.toHaveBeenCalled()
    })
  })

  describe('updateContact', () => {
    it('should update contact successfully', async () => {
      const updateData: UpdateContactData = {
        id: 'contact-1',
        name: 'Jane Doe',
        status: 'unsubscribed'
      }

      const updatedContact: Contact = {
        id: 'contact-1',
        user_id: 'user-123',
        email: '<EMAIL>',
        name: 'Jane Doe',
        status: 'unsubscribed',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockDb.updateContact.mockResolvedValue({
        data: updatedContact,
        error: null
      })

      const result = await contactService.updateContact(updateData, 'user-123')
      
      expect(result.data).toEqual(updatedContact)
      expect(result.error).toBeNull()
      expect(mockDb.updateContact).toHaveBeenCalledWith(updateData)
    })

    it('should reject missing required fields', async () => {
      const result = await contactService.updateContact({} as UpdateContactData, 'user-123')
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toBe('Contact ID and User ID are required')
      expect(mockDb.updateContact).not.toHaveBeenCalled()
    })

    it('should reject invalid email format', async () => {
      const updateData: UpdateContactData = {
        id: 'contact-1',
        email: 'invalid-email'
      }

      const result = await contactService.updateContact(updateData, 'user-123')
      
      expect(result.data).toBeNull()
      expect(result.error?.message).toBe('Invalid email format')
      expect(mockDb.updateContact).not.toHaveBeenCalled()
    })
  })

  describe('deleteContact', () => {
    it('should delete contact successfully', async () => {
      const mockContact: Contact = {
        id: 'contact-1',
        user_id: 'user-123',
        email: '<EMAIL>',
        status: 'active',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [mockContact],
        error: null,
        count: 1
      })
      mockDb.deleteContact.mockResolvedValue({
        data: true,
        error: null
      })

      const result = await contactService.deleteContact('contact-1', 'user-123')
      
      expect(result.data).toBe(true)
      expect(result.error).toBeNull()
      expect(mockDb.deleteContact).toHaveBeenCalledWith('contact-1')
    })

    it('should reject missing required fields', async () => {
      const result = await contactService.deleteContact('', 'user-123')
      
      expect(result.data).toBe(false)
      expect(result.error?.message).toBe('Contact ID and User ID are required')
      expect(mockDb.deleteContact).not.toHaveBeenCalled()
    })
  })

  describe('bulkCreateContacts', () => {
    it('should handle bulk creation with mixed results', async () => {
      const contactsData: CreateContactData[] = [
        {
          user_id: 'user-123',
          email: '<EMAIL>',
          name: 'Valid User'
        },
        {
          user_id: 'user-123',
          email: 'invalid-email',
          name: 'Invalid User'
        }
      ]

      const validContact: Contact = {
        id: 'contact-1',
        user_id: 'user-123',
        email: '<EMAIL>',
        name: 'Valid User',
        status: 'active',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [],
        error: null,
        count: 0
      })

      // Mock createContact to succeed for valid email, fail for invalid
      mockDb.createContact
        .mockResolvedValueOnce({
          data: validContact,
          error: null
        })
        .mockResolvedValueOnce({
          data: null,
          error: new Error('Validation failed: Invalid email format')
        })

      mockDb.isEmailSuppressed.mockResolvedValue(false)

      const result = await contactService.bulkCreateContacts(contactsData, 'user-123')
      
      expect(result.successful).toHaveLength(1)
      expect(result.failed).toHaveLength(1)
      expect(result.duplicates).toHaveLength(0)
      expect(result.successful[0]).toEqual(validContact)
      expect(result.failed[0].data.email).toBe('invalid-email')
    })
  })

  describe('updateContactStatus', () => {
    it('should update contact status and add to suppression list', async () => {
      const mockContact: Contact = {
        id: 'contact-1',
        user_id: 'user-123',
        email: '<EMAIL>',
        status: 'active',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      const updatedContact: Contact = {
        ...mockContact,
        status: 'bounced'
      }

      mockDb.getContacts.mockResolvedValue({
        data: [mockContact],
        error: null,
        count: 1
      })
      mockDb.updateContact.mockResolvedValue({
        data: updatedContact,
        error: null
      })
      mockDb.updateSuppressionList.mockResolvedValue({
        data: {
          id: 'suppression-1',
          user_id: 'user-123',
          email: '<EMAIL>',
          reason: 'bounce',
          created_at: '2023-01-01T00:00:00Z'
        },
        error: null
      })

      const result = await contactService.updateContactStatus(
        '<EMAIL>',
        'user-123',
        'bounced'
      )
      
      expect(result.data).toEqual(updatedContact)
      expect(result.error).toBeNull()
      expect(mockDb.updateSuppressionList).toHaveBeenCalledWith(
        'user-123',
        '<EMAIL>',
        'bounce'
      )
    })
  })

  describe('getContactStats', () => {
    it('should calculate contact statistics correctly', async () => {
      const mockContacts: Contact[] = [
        {
          id: 'contact-1',
          user_id: 'user-123',
          email: '<EMAIL>',
          status: 'active',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        {
          id: 'contact-2',
          user_id: 'user-123',
          email: '<EMAIL>',
          status: 'unsubscribed',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        {
          id: 'contact-3',
          user_id: 'user-123',
          email: '<EMAIL>',
          status: 'bounced',
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        }
      ]

      mockDb.getContacts.mockResolvedValue({
        data: mockContacts,
        error: null,
        count: 3
      })

      const stats = await contactService.getContactStats('user-123')
      
      expect(stats).toEqual({
        total: 3,
        active: 1,
        unsubscribed: 1,
        bounced: 1
      })
    })
  })
})