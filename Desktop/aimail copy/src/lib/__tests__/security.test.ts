import { 
  sanitizeHtml, 
  sanitizeText, 
  sanitizeEmail, 
  sanitizeUrl,
  escapeString,
  sanitizedEmailSchema,
  sanitizedTextSchema,
  sanitizedHtmlSchema,
  sanitizedUrlSchema
} from '../security'

describe('Security Utils', () => {
  describe('sanitizeHtml', () => {
    it('should allow safe HTML tags', () => {
      const input = '<p>Hello <strong>world</strong></p>'
      const result = sanitizeHtml(input)
      expect(result).toBe('<p>Hello <strong>world</strong></p>')
    })

    it('should remove dangerous script tags', () => {
      const input = '<p>Hello</p><script>alert("xss")</script>'
      const result = sanitizeHtml(input)
      expect(result).toBe('<p>Hello</p>')
    })

    it('should remove javascript: URLs', () => {
      const input = '<a href="javascript:alert(\'xss\')">Click me</a>'
      const result = sanitizeHtml(input)
      expect(result).not.toContain('javascript:')
    })

    it('should remove event handlers', () => {
      const input = '<div onclick="alert(\'xss\')">Click me</div>'
      const result = sanitizeHtml(input)
      expect(result).not.toContain('onclick')
    })

    it('should preserve allowed attributes', () => {
      const input = '<a href="https://example.com" title="Example">Link</a>'
      const result = sanitizeHtml(input)
      expect(result).toContain('href="https://example.com"')
      expect(result).toContain('title="Example"')
    })
  })

  describe('sanitizeText', () => {
    it('should remove angle brackets', () => {
      const input = 'Hello <script>alert("xss")</script> world'
      const result = sanitizeText(input)
      expect(result).toBe('Hello scriptalert("xss")/script world')
    })

    it('should remove javascript: protocol', () => {
      const input = 'javascript:alert("xss")'
      const result = sanitizeText(input)
      expect(result).toBe('alert("xss")')
    })

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss")'
      const result = sanitizeText(input)
      expect(result).toBe('alert("xss")')
    })

    it('should trim whitespace', () => {
      const input = '  hello world  '
      const result = sanitizeText(input)
      expect(result).toBe('hello world')
    })
  })

  describe('sanitizeEmail', () => {
    it('should normalize email addresses', () => {
      const input = '  <EMAIL>  '
      const result = sanitizeEmail(input)
      expect(result).toBe('<EMAIL>')
    })

    it('should remove invalid characters', () => {
      const input = 'test<script>@example.com'
      const result = sanitizeEmail(input)
      expect(result).toBe('<EMAIL>')
    })
  })

  describe('sanitizeUrl', () => {
    it('should allow valid HTTP URLs', () => {
      const input = 'https://example.com/path?query=value'
      const result = sanitizeUrl(input)
      expect(result).toBe(input)
    })

    it('should reject javascript: URLs', () => {
      const input = 'javascript:alert("xss")'
      const result = sanitizeUrl(input)
      expect(result).toBe('')
    })

    it('should reject data: URLs', () => {
      const input = 'data:text/html,<script>alert("xss")</script>'
      const result = sanitizeUrl(input)
      expect(result).toBe('')
    })

    it('should reject invalid URLs', () => {
      const input = 'not-a-url'
      const result = sanitizeUrl(input)
      expect(result).toBe('')
    })
  })

  describe('escapeString', () => {
    it('should escape single quotes', () => {
      const input = "It's a test"
      const result = escapeString(input)
      expect(result).toBe("It''s a test")
    })

    it('should escape backslashes', () => {
      const input = 'C:\\Users\\<USER>\\\\Users\\\\test')
    })
  })

  describe('Validation Schemas', () => {
    describe('sanitizedEmailSchema', () => {
      it('should validate and sanitize valid emails', () => {
        const result = sanitizedEmailSchema.parse('<EMAIL>')
        expect(result).toBe('<EMAIL>')
      })

      it('should reject invalid emails', () => {
        expect(() => sanitizedEmailSchema.parse('invalid-email')).toThrow()
      })
    })

    describe('sanitizedTextSchema', () => {
      it('should validate and sanitize text', () => {
        const result = sanitizedTextSchema.parse('  Hello <world>  ')
        expect(result).toBe('Hello world')
      })

      it('should reject text that is too long', () => {
        const longText = 'a'.repeat(1001)
        expect(() => sanitizedTextSchema.parse(longText)).toThrow()
      })
    })

    describe('sanitizedHtmlSchema', () => {
      it('should validate and sanitize HTML', () => {
        const result = sanitizedHtmlSchema.parse('<p>Hello</p><script>alert("xss")</script>')
        expect(result).toBe('<p>Hello</p>')
      })

      it('should reject HTML that is too long', () => {
        const longHtml = '<p>' + 'a'.repeat(50000) + '</p>'
        expect(() => sanitizedHtmlSchema.parse(longHtml)).toThrow()
      })
    })

    describe('sanitizedUrlSchema', () => {
      it('should validate and sanitize URLs', () => {
        const result = sanitizedUrlSchema.parse('https://example.com')
        expect(result).toBe('https://example.com/')
      })

      it('should reject invalid URLs', () => {
        expect(() => sanitizedUrlSchema.parse('javascript:alert("xss")')).toThrow()
      })
    })
  })
})