import {
  getCampaignAnalytics,
  getDashboardMetrics,
  getTimeSeriesData,
  getTopPerformingCampaigns,
  getCampaignComparison,
} from '../analytics'
import { getCampaignTrackingStats } from '../tracking'
import { supabase } from '../supabase'

// Mock dependencies
jest.mock('../tracking', () => ({
  getCampaignTrackingStats: jest.fn(),
}))

jest.mock('../supabase', () => ({
  supabase: {
    from: jest.fn(),
  },
}))

describe('Analytics Service', () => {
  const userId = 'test-user-id'
  const campaignId = 'test-campaign-id'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getCampaignAnalytics', () => {
    it('should get comprehensive analytics for a campaign', async () => {
      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        subject: 'Test Subject',
        status: 'sent',
        sent_at: '2023-01-01T00:00:00Z',
      }

      const mockTrackingStats = {
        totalSends: 100,
        opens: 30,
        clicks: 10,
        openRate: 30,
        clickRate: 10,
        uniqueOpens: 25,
        uniqueClicks: 8,
      }

      const mockBounceComplaintEvents = [
        { type: 'bounce' },
        { type: 'bounce' },
        { type: 'complaint' },
      ]

      // Setup mocks
      const mockSingle = jest.fn().mockResolvedValue({ data: mockCampaign, error: null })
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))
      
      const mockEventsSelect = jest.fn().mockResolvedValue({ 
        data: mockBounceComplaintEvents, 
        error: null 
      })
      const mockIn = jest.fn(() => mockEventsSelect)
      const mockEventsEq = jest.fn(() => ({ in: mockIn }))
      const mockEventsSelectChain = jest.fn(() => ({ eq: mockEventsEq }))

      ;(supabase.from as jest.Mock)
        .mockReturnValueOnce({ select: mockSelect })
        .mockReturnValueOnce({ select: mockEventsSelectChain })

      ;(getCampaignTrackingStats as jest.Mock).mockResolvedValue(mockTrackingStats)

      const result = await getCampaignAnalytics(campaignId, userId)

      expect(result).toEqual({
        campaignId: mockCampaign.id,
        campaignName: mockCampaign.name,
        subject: mockCampaign.subject,
        status: mockCampaign.status,
        sentAt: mockCampaign.sent_at,
        totalSends: 100,
        opens: 30,
        clicks: 10,
        bounces: 2,
        complaints: 1,
        openRate: 30,
        clickRate: 10,
        bounceRate: 2,
        complaintRate: 1,
        uniqueOpens: 25,
        uniqueClicks: 8,
      })

      expect(getCampaignTrackingStats).toHaveBeenCalledWith(campaignId)
    })

    it('should throw error for non-existent campaign', async () => {
      const mockSingle = jest.fn().mockResolvedValue({ data: null, error: new Error('Not found') })
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))
      
      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      await expect(getCampaignAnalytics('invalid-id', userId)).rejects.toThrow('Campaign not found')
    })
  })

  describe('getDashboardMetrics', () => {
    it('should get comprehensive dashboard metrics', async () => {
      const mockCampaigns = [
        { id: 'campaign-1', name: 'Campaign 1', subject: 'Subject 1', status: 'sent', sent_at: '2023-01-01T00:00:00Z' },
        { id: 'campaign-2', name: 'Campaign 2', subject: 'Subject 2', status: 'draft', sent_at: null },
      ]

      const mockContacts = [
        { id: 'contact-1' },
        { id: 'contact-2' },
        { id: 'contact-3' },
      ]

      const mockSends = [
        { id: 'send-1' },
        { id: 'send-2' },
        { id: 'send-3' },
        { id: 'send-4' },
        { id: 'send-5' },
      ]

      const mockEvents = [
        { type: 'open' },
        { type: 'open' },
        { type: 'open' },
        { type: 'click' },
        { type: 'click' },
      ]

      // Setup complex mock chain
      const mockOrder = jest.fn().mockResolvedValue({ data: mockCampaigns, error: null })
      const mockEq1 = jest.fn(() => ({ order: mockOrder }))
      const mockSelect1 = jest.fn(() => ({ eq: mockEq1 }))

      const mockEq2 = jest.fn().mockResolvedValue({ data: mockContacts, error: null })
      const mockSelect2 = jest.fn(() => ({ eq: mockEq2 }))

      const mockEq3 = jest.fn().mockResolvedValue({ data: mockSends, error: null })
      const mockSelect3 = jest.fn(() => ({ eq: mockEq3 }))

      const mockIn = jest.fn().mockResolvedValue({ data: mockEvents, error: null })
      const mockSelect4 = jest.fn(() => ({ in: mockIn }))

      ;(supabase.from as jest.Mock)
        .mockReturnValueOnce({ select: mockSelect1 })
        .mockReturnValueOnce({ select: mockSelect2 })
        .mockReturnValueOnce({ select: mockSelect3 })
        .mockReturnValueOnce({ select: mockSelect4 })

      // Mock getCampaignAnalytics for recent campaigns
      ;(getCampaignTrackingStats as jest.Mock).mockResolvedValue({
        totalSends: 50,
        opens: 15,
        clicks: 5,
        openRate: 30,
        clickRate: 10,
        uniqueOpens: 12,
        uniqueClicks: 4,
      })

      const result = await getDashboardMetrics(userId)

      expect(result).toEqual({
        totalCampaigns: 2,
        totalContacts: 3,
        totalSends: 5,
        totalOpens: 3,
        totalClicks: 2,
        averageOpenRate: 60, // 3 opens / 5 sends * 100
        averageClickRate: 40, // 2 clicks / 5 sends * 100
        recentCampaigns: expect.any(Array),
      })
    })

    it('should handle empty data gracefully', async () => {
      const mockOrder = jest.fn().mockResolvedValue({ data: [], error: null })
      const mockEq = jest.fn(() => ({ order: mockOrder }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await getDashboardMetrics(userId)

      expect(result).toEqual({
        totalCampaigns: 0,
        totalContacts: 0,
        totalSends: 0,
        totalOpens: 0,
        totalClicks: 0,
        averageOpenRate: 0,
        averageClickRate: 0,
        recentCampaigns: [],
      })
    })
  })

  describe('getTimeSeriesData', () => {
    it('should get time series data for specified days', async () => {
      const mockSends = [
        { id: 'send-1', sent_at: '2023-01-01T10:00:00Z' },
        { id: 'send-2', sent_at: '2023-01-01T11:00:00Z' },
        { id: 'send-3', sent_at: '2023-01-02T10:00:00Z' },
      ]

      const mockEvents = [
        { type: 'open', created_at: '2023-01-01T10:30:00Z', send_id: 'send-1' },
        { type: 'click', created_at: '2023-01-01T11:30:00Z', send_id: 'send-2' },
        { type: 'open', created_at: '2023-01-02T10:30:00Z', send_id: 'send-3' },
      ]

      const mockOrder1 = jest.fn().mockResolvedValue({ data: mockSends, error: null })
      const mockGte1 = jest.fn(() => ({ order: mockOrder1 }))
      const mockEq1 = jest.fn(() => ({ gte: mockGte1 }))
      const mockSelect1 = jest.fn(() => ({ eq: mockEq1 }))

      const mockOrder2 = jest.fn().mockResolvedValue({ data: mockEvents, error: null })
      const mockGte2 = jest.fn(() => ({ order: mockOrder2 }))
      const mockIn = jest.fn(() => ({ gte: mockGte2 }))
      const mockSelect2 = jest.fn(() => ({ in: mockIn }))

      ;(supabase.from as jest.Mock)
        .mockReturnValueOnce({ select: mockSelect1 })
        .mockReturnValueOnce({ select: mockSelect2 })

      const result = await getTimeSeriesData(userId, 3)

      expect(result).toHaveLength(3)
      expect(result[0]).toHaveProperty('date')
      expect(result[0]).toHaveProperty('opens')
      expect(result[0]).toHaveProperty('clicks')
      expect(result[0]).toHaveProperty('sends')
    })

    it('should handle empty data', async () => {
      const mockOrder = jest.fn().mockResolvedValue({ data: [], error: null })
      const mockGte = jest.fn(() => ({ order: mockOrder }))
      const mockEq = jest.fn(() => ({ gte: mockGte }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await getTimeSeriesData(userId, 7)

      expect(result).toHaveLength(7)
      expect(result.every(day => day.opens === 0 && day.clicks === 0 && day.sends === 0)).toBe(true)
    })
  })

  describe('getTopPerformingCampaigns', () => {
    it('should get top performing campaigns sorted by open rate', async () => {
      const mockCampaigns = [
        { id: 'campaign-1', name: 'Campaign 1', subject: 'Subject 1', status: 'sent', sent_at: '2023-01-01T00:00:00Z' },
        { id: 'campaign-2', name: 'Campaign 2', subject: 'Subject 2', status: 'sent', sent_at: '2023-01-02T00:00:00Z' },
      ]

      const mockOrder = jest.fn().mockResolvedValue({ data: mockCampaigns, error: null })
      const mockEq = jest.fn(() => ({ order: mockOrder }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      // Mock different performance for each campaign
      ;(getCampaignTrackingStats as jest.Mock)
        .mockResolvedValueOnce({
          totalSends: 100,
          opens: 50,
          clicks: 20,
          openRate: 50,
          clickRate: 20,
          uniqueOpens: 45,
          uniqueClicks: 18,
        })
        .mockResolvedValueOnce({
          totalSends: 100,
          opens: 30,
          clicks: 10,
          openRate: 30,
          clickRate: 10,
          uniqueOpens: 25,
          uniqueClicks: 8,
        })

      const result = await getTopPerformingCampaigns(userId, 5)

      expect(result).toHaveLength(2)
      expect(result[0].openRate).toBeGreaterThan(result[1].openRate)
      expect(result[0].campaignName).toBe('Campaign 1')
    })

    it('should return empty array when no campaigns exist', async () => {
      const mockOrder = jest.fn().mockResolvedValue({ data: [], error: null })
      const mockEq = jest.fn(() => ({ order: mockOrder }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await getTopPerformingCampaigns(userId, 5)

      expect(result).toEqual([])
    })
  })

  describe('getCampaignComparison', () => {
    it('should get analytics for multiple campaigns', async () => {
      const campaignIds = ['campaign-1', 'campaign-2']

      // Mock getCampaignAnalytics calls
      const mockAnalytics1 = {
        campaignId: 'campaign-1',
        campaignName: 'Campaign 1',
        subject: 'Subject 1',
        status: 'sent',
        totalSends: 100,
        opens: 50,
        clicks: 20,
        bounces: 2,
        complaints: 1,
        openRate: 50,
        clickRate: 20,
        bounceRate: 2,
        complaintRate: 1,
        uniqueOpens: 45,
        uniqueClicks: 18,
      }

      const mockAnalytics2 = {
        campaignId: 'campaign-2',
        campaignName: 'Campaign 2',
        subject: 'Subject 2',
        status: 'sent',
        totalSends: 200,
        opens: 60,
        clicks: 15,
        bounces: 3,
        complaints: 0,
        openRate: 30,
        clickRate: 7.5,
        bounceRate: 1.5,
        complaintRate: 0,
        uniqueOpens: 55,
        uniqueClicks: 12,
      }

      // Setup mocks for getCampaignAnalytics
      const mockCampaign1 = { id: 'campaign-1', name: 'Campaign 1', subject: 'Subject 1', status: 'sent', sent_at: '2023-01-01T00:00:00Z' }
      const mockCampaign2 = { id: 'campaign-2', name: 'Campaign 2', subject: 'Subject 2', status: 'sent', sent_at: '2023-01-02T00:00:00Z' }

      const mockSingle = jest.fn()
        .mockResolvedValueOnce({ data: mockCampaign1, error: null })
        .mockResolvedValueOnce({ data: mockCampaign2, error: null })
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      ;(getCampaignTrackingStats as jest.Mock)
        .mockResolvedValueOnce({
          totalSends: 100,
          opens: 50,
          clicks: 20,
          openRate: 50,
          clickRate: 20,
          uniqueOpens: 45,
          uniqueClicks: 18,
        })
        .mockResolvedValueOnce({
          totalSends: 200,
          opens: 60,
          clicks: 15,
          openRate: 30,
          clickRate: 7.5,
          uniqueOpens: 55,
          uniqueClicks: 12,
        })

      const result = await getCampaignComparison(campaignIds, userId)

      expect(result).toHaveLength(2)
      expect(result[0].campaignId).toBe('campaign-1')
      expect(result[1].campaignId).toBe('campaign-2')
    })
  })
})