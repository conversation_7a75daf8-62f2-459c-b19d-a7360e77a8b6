import { NextRequest } from 'next/server'
import { Zod<PERSON>rror, z } from 'zod'
import {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  RateLimitError,
  ExternalServiceError,
  formatAPIError,
  createErrorResponse,
  with<PERSON>rrorHandler
} from '../error-handler'

// Mock NextRequest
const mockRequest = {
  nextUrl: { pathname: '/api/test' },
  url: 'http://localhost:3000/api/test',
  method: 'POST',
  headers: new Map([['user-agent', 'test-agent']])
} as unknown as NextRequest

describe('Error Classes', () => {
  test('AppError should create error with correct properties', () => {
    const error = new AppError('Test error', 400, 'TEST_ERROR', { detail: 'test' })
    
    expect(error.message).toBe('Test error')
    expect(error.statusCode).toBe(400)
    expect(error.code).toBe('TEST_ERROR')
    expect(error.details).toEqual({ detail: 'test' })
    expect(error.name).toBe('AppError')
  })

  test('ValidationError should have correct defaults', () => {
    const error = new ValidationError('Invalid input')
    
    expect(error.statusCode).toBe(400)
    expect(error.code).toBe('VALIDATION_ERROR')
    expect(error.name).toBe('ValidationError')
  })

  test('AuthenticationError should have correct defaults', () => {
    const error = new AuthenticationError()
    
    expect(error.statusCode).toBe(401)
    expect(error.code).toBe('AUTHENTICATION_ERROR')
    expect(error.message).toBe('Authentication required')
  })

  test('AuthorizationError should have correct defaults', () => {
    const error = new AuthorizationError()
    
    expect(error.statusCode).toBe(403)
    expect(error.code).toBe('AUTHORIZATION_ERROR')
    expect(error.message).toBe('Insufficient permissions')
  })

  test('NotFoundError should have correct defaults', () => {
    const error = new NotFoundError('User')
    
    expect(error.statusCode).toBe(404)
    expect(error.code).toBe('NOT_FOUND_ERROR')
    expect(error.message).toBe('User not found')
  })

  test('RateLimitError should have correct defaults', () => {
    const error = new RateLimitError()
    
    expect(error.statusCode).toBe(429)
    expect(error.code).toBe('RATE_LIMIT_ERROR')
    expect(error.message).toBe('Rate limit exceeded')
  })

  test('ExternalServiceError should have correct defaults', () => {
    const error = new ExternalServiceError('SES', 'Service timeout')
    
    expect(error.statusCode).toBe(502)
    expect(error.code).toBe('EXTERNAL_SERVICE_ERROR')
    expect(error.message).toBe('Service timeout')
    expect(error.details).toEqual({ service: 'SES' })
  })
})

describe('formatAPIError', () => {
  test('should format AppError correctly', () => {
    const error = new ValidationError('Invalid email', { field: 'email' })
    const formatted = formatAPIError(error, mockRequest)
    
    expect(formatted.error).toBe('ValidationError')
    expect(formatted.message).toBe('Invalid email')
    expect(formatted.code).toBe('VALIDATION_ERROR')
    expect(formatted.details).toEqual({ field: 'email' })
    expect(formatted.path).toBe('/api/test')
    expect(formatted.timestamp).toBeDefined()
  })

  test('should format ZodError correctly', () => {
    const schema = z.object({ email: z.string().email() })
    
    try {
      schema.parse({ email: 'invalid' })
    } catch (error) {
      const formatted = formatAPIError(error, mockRequest)
      
      expect(formatted.error).toBe('ValidationError')
      expect(formatted.message).toBe('Invalid input data')
      expect(formatted.code).toBe('VALIDATION_ERROR')
      expect(formatted.details).toHaveLength(1)
      expect(formatted.details[0]).toMatchObject({
        field: 'email',
        message: 'Invalid email address'
      })
    }
  })

  test('should format generic Error in development', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'
    
    const error = new Error('Generic error')
    const formatted = formatAPIError(error)
    
    expect(formatted.error).toBe('InternalServerError')
    expect(formatted.message).toBe('Generic error')
    expect(formatted.code).toBe('INTERNAL_SERVER_ERROR')
    expect(formatted.details).toBeDefined()
    
    process.env.NODE_ENV = originalEnv
  })

  test('should format generic Error in production', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'
    
    const error = new Error('Generic error')
    const formatted = formatAPIError(error)
    
    expect(formatted.error).toBe('InternalServerError')
    expect(formatted.message).toBe('An internal server error occurred')
    expect(formatted.code).toBe('INTERNAL_SERVER_ERROR')
    expect(formatted.details).toBeUndefined()
    
    process.env.NODE_ENV = originalEnv
  })

  test('should format unknown error', () => {
    const formatted = formatAPIError('string error')
    
    expect(formatted.error).toBe('UnknownError')
    expect(formatted.message).toBe('An unknown error occurred')
    expect(formatted.code).toBe('UNKNOWN_ERROR')
  })
})

describe('createErrorResponse', () => {
  test('should create response with correct status for AppError', () => {
    const error = new ValidationError('Invalid input')
    const response = createErrorResponse(error, mockRequest)
    
    expect(response.status).toBe(400)
  })

  test('should create response with correct status for ZodError', () => {
    const schema = z.object({ email: z.string().email() })
    
    try {
      schema.parse({ email: 'invalid' })
    } catch (error) {
      const response = createErrorResponse(error, mockRequest)
      expect(response.status).toBe(400)
    }
  })

  test('should create response with 500 status for generic error', () => {
    const error = new Error('Generic error')
    const response = createErrorResponse(error, mockRequest)
    
    expect(response.status).toBe(500)
  })
})

describe('withErrorHandler', () => {
  test('should handle successful request', async () => {
    const handler = jest.fn().mockResolvedValue(new Response('success'))
    const wrappedHandler = withErrorHandler(handler)
    
    const response = await wrappedHandler(mockRequest)
    
    expect(handler).toHaveBeenCalledWith(mockRequest, undefined)
    expect(response).toBeInstanceOf(Response)
  })

  test('should handle thrown AppError', async () => {
    const handler = jest.fn().mockRejectedValue(new ValidationError('Invalid input'))
    const wrappedHandler = withErrorHandler(handler)
    
    const response = await wrappedHandler(mockRequest)
    
    expect(response.status).toBe(400)
    const body = await response.json()
    expect(body.error).toBe('ValidationError')
    expect(body.message).toBe('Invalid input')
  })

  test('should handle thrown generic error', async () => {
    const handler = jest.fn().mockRejectedValue(new Error('Generic error'))
    const wrappedHandler = withErrorHandler(handler)
    
    const response = await wrappedHandler(mockRequest)
    
    expect(response.status).toBe(500)
    const body = await response.json()
    expect(body.error).toBe('InternalServerError')
  })

  test('should pass context to handler', async () => {
    const handler = jest.fn().mockResolvedValue(new Response('success'))
    const wrappedHandler = withErrorHandler(handler)
    const context = { params: { id: '123' } }
    
    await wrappedHandler(mockRequest, context)
    
    expect(handler).toHaveBeenCalledWith(mockRequest, context)
  })
})