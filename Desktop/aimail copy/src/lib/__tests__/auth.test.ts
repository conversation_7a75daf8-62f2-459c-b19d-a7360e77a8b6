import { AuthService } from '../auth'
import { supabase } from '../supabase'

// Mock the supabase module
jest.mock('../supabase')

const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('signIn', () => {
    it('should sign in successfully with valid credentials', async () => {
      const mockUser = { id: '1', email: '<EMAIL>' }
      const mockSession = { access_token: 'token' }
      
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      } as any)

      const result = await AuthService.signIn({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
      expect(result.user).toEqual(mockUser)
      expect(result.session).toEqual(mockSession)
    })

    it('should throw error with invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' },
      } as any)

      await expect(
        AuthService.signIn({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })
      ).rejects.toThrow('Invalid credentials')
    })
  })

  describe('signUp', () => {
    it('should sign up successfully with valid data', async () => {
      const mockUser = { id: '1', email: '<EMAIL>' }
      const mockSession = { access_token: 'token' }
      
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      } as any)

      const result = await AuthService.signUp({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      })
      expect(result.user).toEqual(mockUser)
      expect(result.session).toEqual(mockSession)
    })

    it('should throw error with invalid data', async () => {
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Email already exists' },
      } as any)

      await expect(
        AuthService.signUp({
          email: '<EMAIL>',
          password: 'weak',
        })
      ).rejects.toThrow('Email already exists')
    })
  })

  describe('signOut', () => {
    it('should sign out successfully', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: null,
      } as any)

      await expect(AuthService.signOut()).resolves.not.toThrow()
      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
    })

    it('should throw error if sign out fails', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: { message: 'Sign out failed' },
      } as any)

      await expect(AuthService.signOut()).rejects.toThrow('Sign out failed')
    })
  })

  describe('getSession', () => {
    it('should return session when available', async () => {
      const mockSession = { access_token: 'token' }
      
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      } as any)

      const session = await AuthService.getSession()
      expect(session).toEqual(mockSession)
    })

    it('should return null when no session', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      } as any)

      const session = await AuthService.getSession()
      expect(session).toBeNull()
    })
  })

  describe('getUser', () => {
    it('should return user when authenticated', async () => {
      const mockUser = { id: '1', email: '<EMAIL>' }
      
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      } as any)

      const user = await AuthService.getUser()
      expect(user).toEqual(mockUser)
    })

    it('should return null when not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      } as any)

      const user = await AuthService.getUser()
      expect(user).toBeNull()
    })
  })
})