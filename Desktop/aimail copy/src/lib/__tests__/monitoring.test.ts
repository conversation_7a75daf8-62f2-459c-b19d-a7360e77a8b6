import { errorMonitor, captureError, captureWarning, captureInfo, withErrorCapture, measurePerformance } from '../monitoring'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock fetch
global.fetch = jest.fn()

describe('ErrorMonitor', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    errorMonitor.clearErrors()
    localStorageMock.getItem.mockReturnValue('[]')
  })

  test('should capture error with context', () => {
    const error = new Error('Test error')
    const context = { userId: '123', operation: 'test' }
    
    errorMonitor.captureError(error, context)
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Test error')
    expect(errors[0].context.userId).toBe('123')
    expect(errors[0].context.operation).toBe('test')
  })

  test('should capture string error', () => {
    errorMonitor.captureError('String error')
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('String error')
    expect(errors[0].error).toBe('Error')
  })

  test('should generate fingerprint for similar errors', () => {
    const error1 = new Error('Same error')
    const error2 = new Error('Same error')
    
    errorMonitor.captureError(error1)
    errorMonitor.captureError(error2)
    
    const errors = errorMonitor.getErrors()
    expect(errors[0].fingerprint).toBe(errors[1].fingerprint)
  })

  test('should limit stored errors', () => {
    // Capture more than maxErrors (100)
    for (let i = 0; i < 105; i++) {
      errorMonitor.captureError(`Error ${i}`)
    }
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(100)
    expect(errors[0].message).toBe('Error 5') // First 5 should be removed
  })

  test('should store errors in localStorage', () => {
    const error = new Error('Test error')
    errorMonitor.captureError(error)
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'app_errors',
      expect.stringContaining('Test error')
    )
  })

  test('should send errors to external service in production', async () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'production'
    
    const mockFetch = fetch as jest.MockedFunction<typeof fetch>
    mockFetch.mockResolvedValueOnce(new Response('ok'))
    
    const error = new Error('Production error')
    errorMonitor.captureError(error)
    
    // Wait for async operation
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(mockFetch).toHaveBeenCalledWith('/api/errors', expect.objectContaining({
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('Production error')
    }))
    
    process.env.NODE_ENV = originalEnv
  })

  test('should get errors by fingerprint', () => {
    const error1 = new Error('Same error')
    const error2 = new Error('Same error')
    const error3 = new Error('Different error')
    
    errorMonitor.captureError(error1)
    errorMonitor.captureError(error2)
    errorMonitor.captureError(error3)
    
    const errors = errorMonitor.getErrors()
    const fingerprint = errors[0].fingerprint!
    const similarErrors = errorMonitor.getErrorsByFingerprint(fingerprint)
    
    expect(similarErrors).toHaveLength(2)
    expect(similarErrors.every(e => e.message === 'Same error')).toBe(true)
  })

  test('should clear errors', () => {
    errorMonitor.captureError('Test error')
    expect(errorMonitor.getErrors()).toHaveLength(1)
    
    errorMonitor.clearErrors()
    expect(errorMonitor.getErrors()).toHaveLength(0)
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('app_errors')
  })

  test('should measure performance', async () => {
    const mockFn = jest.fn().mockResolvedValue('success')
    
    const result = await errorMonitor.measurePerformance('test-operation', mockFn)
    
    expect(result).toBe('success')
    expect(mockFn).toHaveBeenCalled()
  })

  test('should capture error in performance measurement', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('Performance error'))
    
    await expect(errorMonitor.measurePerformance('test-operation', mockFn)).rejects.toThrow('Performance error')
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Performance error')
    expect(errors[0].context.operation).toBe('test-operation')
  })
})

describe('Convenience Functions', () => {
  beforeEach(() => {
    errorMonitor.clearErrors()
  })

  test('captureError should capture error', () => {
    captureError('Test error', { context: 'test' })
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Test error')
    expect(errors[0].level).toBe('error')
  })

  test('captureWarning should capture warning', () => {
    captureWarning('Test warning', { context: 'test' })
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Test warning')
    expect(errors[0].level).toBe('warning')
  })

  test('captureInfo should capture info', () => {
    captureInfo('Test info', { context: 'test' })
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Test info')
    expect(errors[0].level).toBe('info')
  })

  test('withErrorCapture should capture sync errors', () => {
    const fn = jest.fn().mockImplementation(() => {
      throw new Error('Sync error')
    })
    
    const wrappedFn = withErrorCapture(fn, { operation: 'test' })
    
    expect(() => wrappedFn()).toThrow('Sync error')
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Sync error')
    expect(errors[0].context.operation).toBe('test')
  })

  test('withErrorCapture should capture async errors', async () => {
    const fn = jest.fn().mockRejectedValue(new Error('Async error'))
    const wrappedFn = withErrorCapture(fn, { operation: 'test' })
    
    await expect(wrappedFn()).rejects.toThrow('Async error')
    
    const errors = errorMonitor.getErrors()
    expect(errors).toHaveLength(1)
    expect(errors[0].message).toBe('Async error')
    expect(errors[0].context.operation).toBe('test')
  })

  test('measurePerformance should measure and return result', async () => {
    const mockFn = jest.fn().mockResolvedValue('result')
    
    const result = await measurePerformance('test-op', mockFn)
    
    expect(result).toBe('result')
    expect(mockFn).toHaveBeenCalled()
  })
})