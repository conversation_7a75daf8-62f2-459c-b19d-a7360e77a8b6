import { exportUserData, deleteUserData } from '../data-export'
import { createClient } from '@supabase/supabase-js'

// Mock Supabase
jest.mock('@supabase/supabase-js')
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(() => ({ data: mockUser, error: null })),
        order: jest.fn(() => ({ data: [], error: null })),
        gte: jest.fn(() => ({
          lte: jest.fn(() => ({ data: [], error: null }))
        }))
      }))
    })),
    delete: jest.fn(() => ({
      eq: jest.fn(() => ({ error: null })),
      in: jest.fn(() => ({ error: null }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => ({ error: null }))
    }))
  })),
  rpc: jest.fn(() => ({ data: [], error: null }))
}

;(createClient as jest.Mock).mockReturnValue(mockSupabase)

// Mock audit logging
jest.mock('../audit', () => ({
  logAuditEvent: jest.fn(),
  AuditAction: {
    DATA_EXPORTED: 'data_exported'
  },
  ResourceType: {
    USER: 'user'
  }
}))

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z'
}

describe('Data Export System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('exportUserData', () => {
    it('should handle export errors gracefully', async () => {
      await expect(exportUserData({
        userId: 'user-123'
      })).rejects.toThrow('Failed to export user data')
    })
  })

  describe('deleteUserData', () => {
    it('should handle deletion errors gracefully', async () => {
      await expect(deleteUserData('user-123')).rejects.toThrow('Failed to delete user data')
    })
  })
})