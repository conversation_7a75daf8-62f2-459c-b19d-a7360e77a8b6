import { SettingsService } from '../settings'

// Mock AWS SDK
jest.mock('@aws-sdk/client-ses', () => ({
  SESClient: jest.fn().mockImplementation(() => ({
    send: jest.fn(),
  })),
  GetSendQuotaCommand: jest.fn(),
  VerifyEmailIdentityCommand: jest.fn(),
}))

// Mock fetch for Perplexity API
global.fetch = jest.fn()

describe('SettingsService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('validateSESConfig', () => {
    it('should validate SES configuration successfully', async () => {
      const mockSend = jest.fn().mockResolvedValue({
        Max24HourSend: 200,
        MaxSendRate: 1,
        SentLast24Hours: 0,
      })

      const { SESClient } = require('@aws-sdk/client-ses')
      SESClient.mockImplementation(() => ({
        send: mockSend,
      }))

      const config = {
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        region: 'us-east-1',
        fromEmail: '<EMAIL>',
      }

      const result = await SettingsService.validateSESConfig(config)

      expect(result.valid).toBe(true)
      expect(result.quota).toEqual({
        max24HourSend: 200,
        maxSendRate: 1,
        sentLast24Hours: 0,
      })
    })

    it('should handle SES validation errors', async () => {
      const mockSend = jest.fn().mockRejectedValue(new Error('Invalid credentials'))

      const { SESClient } = require('@aws-sdk/client-ses')
      SESClient.mockImplementation(() => ({
        send: mockSend,
      }))

      const config = {
        accessKeyId: 'invalid-key',
        secretAccessKey: 'invalid-secret',
        region: 'us-east-1',
        fromEmail: '<EMAIL>',
      }

      const result = await SettingsService.validateSESConfig(config)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Invalid credentials')
    })
  })

  describe('testSESEmailIdentity', () => {
    it('should verify email identity successfully', async () => {
      const mockSend = jest.fn().mockResolvedValue({})

      const { SESClient } = require('@aws-sdk/client-ses')
      SESClient.mockImplementation(() => ({
        send: mockSend,
      }))

      const config = {
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        region: 'us-east-1',
        fromEmail: '<EMAIL>',
      }

      const result = await SettingsService.testSESEmailIdentity(config)

      expect(result.valid).toBe(true)
      expect(mockSend).toHaveBeenCalled()
    })

    it('should handle email verification errors', async () => {
      const mockSend = jest.fn().mockRejectedValue(new Error('Email not verified'))

      const { SESClient } = require('@aws-sdk/client-ses')
      SESClient.mockImplementation(() => ({
        send: mockSend,
      }))

      const config = {
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        region: 'us-east-1',
        fromEmail: '<EMAIL>',
      }

      const result = await SettingsService.testSESEmailIdentity(config)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Email not verified')
    })
  })

  describe('validatePerplexityConfig', () => {
    it('should validate Perplexity API configuration successfully', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          choices: [{ message: { content: 'OK' } }],
        }),
      } as Response)

      const config = { apiKey: 'test-api-key' }
      const result = await SettingsService.validatePerplexityConfig(config)

      expect(result.valid).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.perplexity.ai/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
          },
        })
      )
    })

    it('should handle Perplexity API validation errors', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: async () => ({
          error: { message: 'Invalid API key' },
        }),
      } as Response)

      const config = { apiKey: 'invalid-api-key' }
      const result = await SettingsService.validatePerplexityConfig(config)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Invalid API key')
    })

    it('should handle network errors', async () => {
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>
      mockFetch.mockRejectedValue(new Error('Network error'))

      const config = { apiKey: 'test-api-key' }
      const result = await SettingsService.validatePerplexityConfig(config)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Network error')
    })
  })

  describe('exportSettings', () => {
    it('should export settings in correct format', () => {
      const settings = {
        ses: {
          accessKeyId: 'test-key',
          secretAccessKey: 'test-secret',
          region: 'us-east-1',
          fromEmail: '<EMAIL>',
        },
        perplexity: {
          apiKey: 'test-api-key',
        },
        branding: {
          appName: 'Test App',
          logo: '/test-logo.png',
          colors: {
            primary: '#000000',
            secondary: '#ffffff',
            accent: '#ff0000',
          },
          theme: {
            borderRadius: '0.5rem',
            fontFamily: 'Arial',
          },
        },
      }

      const exported = SettingsService.exportSettings(settings)
      const parsed = JSON.parse(exported)

      expect(parsed.version).toBe('1.0')
      expect(parsed.timestamp).toBeDefined()
      expect(parsed.settings.ses.region).toBe('us-east-1')
      expect(parsed.settings.ses.fromEmail).toBe('<EMAIL>')
      expect(parsed.settings.branding).toEqual(settings.branding)
      
      // Sensitive data should not be exported
      expect(parsed.settings.ses.accessKeyId).toBeUndefined()
      expect(parsed.settings.ses.secretAccessKey).toBeUndefined()
    })
  })

  describe('importSettings', () => {
    it('should import valid settings successfully', () => {
      const validSettings = JSON.stringify({
        version: '1.0',
        timestamp: '2023-01-01T00:00:00.000Z',
        settings: {
          branding: {
            appName: 'Imported App',
            logo: '/imported-logo.png',
            colors: {
              primary: '#123456',
              secondary: '#654321',
              accent: '#abcdef',
            },
            theme: {
              borderRadius: '1rem',
              fontFamily: 'Roboto',
            },
          },
        },
      })

      const result = SettingsService.importSettings(validSettings)

      expect(result.valid).toBe(true)
      expect(result.settings?.branding?.appName).toBe('Imported App')
      expect(result.settings?.branding?.colors.primary).toBe('#123456')
    })

    it('should reject invalid JSON', () => {
      const invalidJson = 'invalid json'
      const result = SettingsService.importSettings(invalidJson)

      expect(result.valid).toBe(false)
      expect(result.error).toBeDefined()
    })

    it('should reject invalid settings format', () => {
      const invalidSettings = JSON.stringify({
        version: '1.0',
        // Missing settings object
      })

      const result = SettingsService.importSettings(invalidSettings)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Invalid settings file format')
    })

    it('should reject invalid branding configuration', () => {
      const invalidBranding = JSON.stringify({
        version: '1.0',
        settings: {
          branding: {
            appName: '', // Invalid: empty string
            logo: '/logo.png',
            colors: {
              primary: 'invalid-color', // Invalid: not hex color
              secondary: '#ffffff',
              accent: '#000000',
            },
            theme: {
              borderRadius: '0.5rem',
              fontFamily: 'Inter',
            },
          },
        },
      })

      const result = SettingsService.importSettings(invalidBranding)

      expect(result.valid).toBe(false)
      expect(result.error).toBeDefined()
    })
  })
})