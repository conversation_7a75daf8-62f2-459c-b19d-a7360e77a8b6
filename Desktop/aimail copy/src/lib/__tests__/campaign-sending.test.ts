import { EmailService } from '../email'
import { contactService, contactValidation } from '../contacts'

// Mock environment variables
process.env.AWS_REGION = 'us-east-1'
process.env.AWS_ACCESS_KEY_ID = 'test-key'
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret'
process.env.SES_FROM_EMAIL = '<EMAIL>'

describe('Campaign Sending Integration', () => {
  let emailService: EmailService

  beforeEach(() => {
    emailService = new EmailService()
  })

  describe('Email Template Processing', () => {
    test('should render campaign template with contact data', () => {
      const campaignTemplate = {
        subject: 'Welcome {{name}}!',
        htmlBody: '<h1>Hello {{name}}, welcome to {{company}}!</h1>',
        textBody: 'Hello {{name}}, welcome to {{company}}!',
      }

      const contactData = {
        name: '<PERSON>',
        company: 'Acme Corp',
        email: '<EMAIL>',
      }

      const result = emailService.renderTemplate(campaignTemplate, contactData)

      expect(result.subject).toBe('Welcome John Doe!')
      expect(result.htmlBody).toBe('<h1>Hello <PERSON> Doe, welcome to Acme Corp!</h1>')
      expect(result.textBody).toBe('Hello John Doe, welcome to Acme Corp!')
    })

    test('should handle missing contact data gracefully', () => {
      const campaignTemplate = {
        subject: 'Welcome {{name}}!',
        htmlBody: '<h1>Hello {{name}}, welcome to {{company}}!</h1>',
      }

      const contactData = {
        name: 'John Doe',
        // company is missing
      }

      const result = emailService.renderTemplate(campaignTemplate, contactData)

      expect(result.subject).toBe('Welcome John Doe!')
      expect(result.htmlBody).toBe('<h1>Hello John Doe, welcome to {{company}}!</h1>')
    })

    test('should add tracking to campaign emails', () => {
      const htmlBody = `
        <html>
          <body>
            <h1>Campaign Email</h1>
            <p>Check out our <a href="https://example.com/product">latest product</a>!</p>
            <p><a href="https://example.com/unsubscribe">Unsubscribe</a></p>
          </body>
        </html>
      `

      const trackingData = {
        sendId: 'send-123',
        baseUrl: 'https://myapp.com',
      }

      const result = emailService.generateEmailWithTracking(htmlBody, trackingData)

      // Should contain tracking pixel
      expect(result).toContain('src="https://myapp.com/api/track/open?send_id=send-123"')
      
      // Should convert links to tracking links
      expect(result).toContain('href="https://myapp.com/api/track/click?send_id=send-123&url=https%3A%2F%2Fexample.com%2Fproduct"')
      expect(result).toContain('href="https://myapp.com/api/track/click?send_id=send-123&url=https%3A%2F%2Fexample.com%2Funsubscribe"')
    })
  })

  describe('Bulk Email Processing', () => {
    test('should process multiple recipients', async () => {
      const recipients = [
        { email: '<EMAIL>', name: 'User 1' },
        { email: '<EMAIL>', name: 'User 2' },
        { email: '<EMAIL>', name: 'User 3' },
      ]

      const template = {
        subject: 'Test Campaign',
        htmlBody: '<h1>Hello {{name}}!</h1>',
      }

      // Mock the sendEmail method to simulate successful sends
      const originalSendEmail = emailService.sendEmail
      emailService.sendEmail = jest.fn().mockResolvedValue({
        messageId: 'test-message-id',
        success: true,
      })

      const result = await emailService.sendBulkEmails(recipients, template)

      expect(result.totalSent).toBe(3)
      expect(result.totalFailed).toBe(0)
      expect(result.results).toHaveLength(3)
      expect(result.results.every(r => r.success)).toBe(true)

      // Restore original method
      emailService.sendEmail = originalSendEmail
    })

    test('should handle partial failures in bulk sending', async () => {
      const recipients = [
        { email: '<EMAIL>', name: 'User 1' },
        { email: '<EMAIL>', name: 'User 2' },
        { email: '<EMAIL>', name: 'User 3' },
      ]

      const template = {
        subject: 'Test Campaign',
        htmlBody: '<h1>Hello {{name}}!</h1>',
      }

      // Mock the sendEmail method to simulate mixed results
      const originalSendEmail = emailService.sendEmail
      emailService.sendEmail = jest.fn()
        .mockResolvedValueOnce({ messageId: 'msg-1', success: true })
        .mockResolvedValueOnce({ messageId: '', success: false, error: 'Failed to send' })
        .mockResolvedValueOnce({ messageId: 'msg-3', success: true })

      const result = await emailService.sendBulkEmails(recipients, template)

      expect(result.totalSent).toBe(2)
      expect(result.totalFailed).toBe(1)
      expect(result.results).toHaveLength(3)
      expect(result.results.filter(r => r.success)).toHaveLength(2)
      expect(result.results.filter(r => !r.success)).toHaveLength(1)

      // Restore original method
      emailService.sendEmail = originalSendEmail
    })
  })

  describe('Rate Limiting', () => {
    test('should respect rate limits during bulk sending', async () => {
      const recipients = Array.from({ length: 5 }, (_, i) => ({
        email: `user${i}@example.com`,
        name: `User ${i}`,
      }))

      const template = {
        subject: 'Test Campaign',
        htmlBody: '<h1>Hello {{name}}!</h1>',
      }

      // Mock the sendEmail method
      const originalSendEmail = emailService.sendEmail
      emailService.sendEmail = jest.fn().mockResolvedValue({
        messageId: 'test-message-id',
        success: true,
      })

      const startTime = Date.now()
      await emailService.sendBulkEmails(recipients, template)
      const endTime = Date.now()

      // Should complete in reasonable time (not too long due to rate limiting)
      expect(endTime - startTime).toBeLessThan(5000)
      expect(emailService.sendEmail).toHaveBeenCalledTimes(5)

      // Restore original method
      emailService.sendEmail = originalSendEmail
    })

    test('should provide rate limit status', () => {
      const status = emailService.getRateLimitStatus()

      expect(status).toEqual({
        currentSendRate: expect.any(Number),
        maxSendRate: 14,
        dailySendCount: expect.any(Number),
        maxDailyQuota: 200,
        timeUntilReset: expect.any(Number),
      })
    })
  })

  describe('Contact Validation', () => {
    test('should validate contact data', () => {
      const validContact = {
        email: '<EMAIL>',
        name: 'Test User',
        user_id: 'user-123',
      }

      const validation = contactValidation.validateContactData(validContact)
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toHaveLength(0)
    })

    test('should reject invalid email addresses', () => {
      const invalidContact = {
        email: 'invalid-email',
        name: 'Test User',
        user_id: 'user-123',
      }

      const validation = contactValidation.validateContactData(invalidContact)
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toContain('Invalid email format')
    })

    test('should sanitize contact data', () => {
      const rawContact = {
        email: '  <EMAIL>  ',
        name: '  Test User  ',
        user_id: 'user-123',
        status: 'active' as const,
      }

      const sanitized = contactValidation.sanitizeContactData(rawContact)
      
      expect(sanitized.email).toBe('<EMAIL>')
      expect(sanitized.name).toBe('Test User')
      expect(sanitized.status).toBe('active')
    })
  })
})