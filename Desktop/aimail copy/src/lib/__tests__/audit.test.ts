// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn()
}))

import { logAuditEvent, getAuditLogs, AuditAction, ResourceType, getRequestMetadata } from '../audit'
import { createClient } from '@supabase/supabase-js'

const mockSupabase = {
  from: jest.fn(() => ({
    insert: jest.fn(() => ({ error: null })),
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        order: jest.fn(() => ({
          limit: jest.fn(() => ({
            range: jest.fn(() => ({ data: [], error: null })),
            data: [],
            error: null
          })),
          data: [],
          error: null
        })),
        gte: jest.fn(() => ({
          lte: jest.fn(() => ({
            order: jest.fn(() => ({
              limit: jest.fn(() => ({ data: [], error: null })),
              data: [],
              error: null
            }))
          }))
        }))
      }))
    }))
  }))
}

// Set up the mock return value
;(createClient as jest.Mock).mockReturnValue(mockSupabase)

describe('Audit System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('logAuditEvent', () => {
    it('should handle audit logging gracefully', async () => {
      // Test that the function doesn't throw errors
      await expect(logAuditEvent({
        user_id: 'user-123',
        action: AuditAction.CAMPAIGN_CREATED,
        resource_type: ResourceType.CAMPAIGN,
        resource_id: 'campaign-123',
        details: { campaign_name: 'Test Campaign' },
        ip_address: '***********',
        user_agent: 'Mozilla/5.0'
      })).resolves.not.toThrow()
    })
  })

  describe('getRequestMetadata', () => {
    it('should extract IP address from x-forwarded-for header', () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'x-forwarded-for') return '***********, ********'
            if (header === 'user-agent') return 'Mozilla/5.0'
            return null
          })
        }
      } as any

      const result = getRequestMetadata(mockRequest)

      expect(result).toEqual({
        ip_address: '***********',
        user_agent: 'Mozilla/5.0'
      })
    })

    it('should fall back to x-real-ip header', () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'x-real-ip') return '***********'
            if (header === 'user-agent') return 'Mozilla/5.0'
            return null
          })
        }
      } as any

      const result = getRequestMetadata(mockRequest)

      expect(result).toEqual({
        ip_address: '***********',
        user_agent: 'Mozilla/5.0'
      })
    })

    it('should use unknown for missing headers', () => {
      const mockRequest = {
        headers: {
          get: jest.fn(() => null)
        }
      } as any

      const result = getRequestMetadata(mockRequest)

      expect(result).toEqual({
        ip_address: 'unknown',
        user_agent: 'unknown'
      })
    })
  })
})