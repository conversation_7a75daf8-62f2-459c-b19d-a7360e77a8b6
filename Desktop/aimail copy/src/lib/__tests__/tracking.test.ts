import {
  generateTrackingPixelUrl,
  generateClickTrackingUrl,
  addTrackingPixel,
  addClickTracking,
  generateTrackedEmailHtml,
  logTrackingEvent,
  validateSendId,
  getCampaignTrackingStats,
} from '../tracking'
import { supabase } from '../supabase'

// Mock Supabase
jest.mock('../supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: jest.fn(() => ({ error: null })),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => ({ data: { id: 'test-send-id' }, error: null })),
          in: jest.fn(() => ({ eq: jest.fn(() => ({ data: [], error: null })) })),
        })),
        in: jest.fn(() => ({
          eq: jest.fn(() => ({ data: [], error: null })),
        })),
      })),
    })),
  },
}))

describe('Tracking Utilities', () => {
  const baseUrl = 'https://example.com'
  const sendId = 'test-send-id'
  const originalUrl = 'https://external.com/page'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('generateTrackingPixelUrl', () => {
    it('should generate correct tracking pixel URL', () => {
      const result = generateTrackingPixelUrl(baseUrl, sendId)
      expect(result).toBe(`${baseUrl}/api/track/open?send_id=${sendId}`)
    })
  })

  describe('generateClickTrackingUrl', () => {
    it('should generate correct click tracking URL', () => {
      const result = generateClickTrackingUrl(baseUrl, sendId, originalUrl)
      const encodedUrl = encodeURIComponent(originalUrl)
      expect(result).toBe(`${baseUrl}/api/track/click?send_id=${sendId}&url=${encodedUrl}`)
    })

    it('should properly encode URLs with special characters', () => {
      const specialUrl = 'https://example.com/page?param=value&other=test'
      const result = generateClickTrackingUrl(baseUrl, sendId, specialUrl)
      const encodedUrl = encodeURIComponent(specialUrl)
      expect(result).toBe(`${baseUrl}/api/track/click?send_id=${sendId}&url=${encodedUrl}`)
    })
  })

  describe('addTrackingPixel', () => {
    it('should add tracking pixel before closing body tag', () => {
      const html = '<html><body><h1>Test</h1></body></html>'
      const pixelUrl = 'https://example.com/pixel.png'
      const result = addTrackingPixel(html, pixelUrl)
      
      expect(result).toContain(`<img src="${pixelUrl}" width="1" height="1" style="display:none;" alt="" />`)
      expect(result).toContain('</body>')
      expect(result.indexOf('img')).toBeLessThan(result.indexOf('</body>'))
    })

    it('should add tracking pixel at the end if no body tag', () => {
      const html = '<div>Test content</div>'
      const pixelUrl = 'https://example.com/pixel.png'
      const result = addTrackingPixel(html, pixelUrl)
      
      expect(result).toContain(`<img src="${pixelUrl}" width="1" height="1" style="display:none;" alt="" />`)
      expect(result.endsWith('alt="" />')).toBe(true)
    })
  })

  describe('addClickTracking', () => {
    it('should replace regular links with tracking links', () => {
      const html = '<a href="https://example.com">Click here</a>'
      const result = addClickTracking(html, baseUrl, sendId)
      
      expect(result).toContain('/api/track/click')
      expect(result).toContain(`send_id=${sendId}`)
      expect(result).toContain(encodeURIComponent('https://example.com'))
    })

    it('should skip links that are already tracking links', () => {
      const html = '<a href="https://example.com/api/track/click?send_id=123">Already tracked</a>'
      const result = addClickTracking(html, baseUrl, sendId)
      
      expect(result).toBe(html) // Should remain unchanged
    })

    it('should skip mailto links', () => {
      const html = '<a href="mailto:<EMAIL>">Email us</a>'
      const result = addClickTracking(html, baseUrl, sendId)
      
      expect(result).toBe(html) // Should remain unchanged
    })

    it('should handle multiple links', () => {
      const html = `
        <a href="https://example.com">Link 1</a>
        <a href="https://google.com">Link 2</a>
        <a href="mailto:<EMAIL>">Email</a>
      `
      const result = addClickTracking(html, baseUrl, sendId)
      
      // Should have 2 tracking links (not the mailto)
      const trackingLinks = (result.match(/\/api\/track\/click/g) || []).length
      expect(trackingLinks).toBe(2)
    })

    it('should preserve link attributes', () => {
      const html = '<a href="https://example.com" class="btn" target="_blank">Click here</a>'
      const result = addClickTracking(html, baseUrl, sendId)
      
      expect(result).toContain('class="btn"')
      expect(result).toContain('target="_blank"')
      expect(result).toContain('/api/track/click')
    })
  })

  describe('generateTrackedEmailHtml', () => {
    it('should add both click tracking and tracking pixel', () => {
      const html = `
        <html>
          <body>
            <h1>Test Email</h1>
            <a href="https://example.com">Click here</a>
            <p>Some content</p>
          </body>
        </html>
      `
      const result = generateTrackedEmailHtml(html, baseUrl, sendId)
      
      // Should contain click tracking
      expect(result).toContain('/api/track/click')
      expect(result).toContain(`send_id=${sendId}`)
      
      // Should contain tracking pixel
      expect(result).toContain('/api/track/open')
      expect(result).toContain('width="1" height="1"')
    })
  })

  describe('logTrackingEvent', () => {
    it('should insert tracking event into database', async () => {
      const mockInsert = jest.fn(() => ({ error: null }))
      const mockFrom = jest.fn(() => ({ insert: mockInsert }))
      ;(supabase.from as jest.Mock).mockReturnValue({ insert: mockInsert })

      await logTrackingEvent(sendId, 'open', { userAgent: 'test' })

      expect(supabase.from).toHaveBeenCalledWith('events')
      expect(mockInsert).toHaveBeenCalledWith({
        send_id: sendId,
        type: 'open',
        metadata: { userAgent: 'test' },
      })
    })

    it('should handle database errors', async () => {
      const mockInsert = jest.fn(() => ({ error: new Error('Database error') }))
      ;(supabase.from as jest.Mock).mockReturnValue({ insert: mockInsert })

      await expect(logTrackingEvent(sendId, 'open')).rejects.toThrow('Database error')
    })

    it('should handle null metadata', async () => {
      const mockInsert = jest.fn(() => ({ error: null }))
      ;(supabase.from as jest.Mock).mockReturnValue({ insert: mockInsert })

      await logTrackingEvent(sendId, 'click')

      expect(mockInsert).toHaveBeenCalledWith({
        send_id: sendId,
        type: 'click',
        metadata: null,
      })
    })
  })

  describe('validateSendId', () => {
    it('should return true for valid send ID', async () => {
      const mockSingle = jest.fn(() => ({ data: { id: sendId }, error: null }))
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))
      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await validateSendId(sendId)

      expect(result).toBe(true)
      expect(supabase.from).toHaveBeenCalledWith('sends')
      expect(mockSelect).toHaveBeenCalledWith('id')
      expect(mockEq).toHaveBeenCalledWith('id', sendId)
    })

    it('should return false for invalid send ID', async () => {
      const mockSingle = jest.fn(() => ({ data: null, error: new Error('Not found') }))
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))
      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await validateSendId('invalid-id')

      expect(result).toBe(false)
    })

    it('should return false on database error', async () => {
      const mockSingle = jest.fn(() => { throw new Error('Database error') })
      const mockEq = jest.fn(() => ({ single: mockSingle }))
      const mockSelect = jest.fn(() => ({ eq: mockEq }))
      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await validateSendId(sendId)

      expect(result).toBe(false)
    })
  })

  describe('getCampaignTrackingStats', () => {
    it('should calculate correct statistics', async () => {
      const campaignId = 'test-campaign-id'
      
      // Mock sends data
      const mockSends = [
        { id: 'send-1' },
        { id: 'send-2' },
        { id: 'send-3' },
      ]
      
      // Mock open events (send-1 opened twice, send-2 opened once)
      const mockOpenEvents = [
        { send_id: 'send-1' },
        { send_id: 'send-1' },
        { send_id: 'send-2' },
      ]
      
      // Mock click events (send-1 clicked once)
      const mockClickEvents = [
        { send_id: 'send-1', metadata: { originalUrl: 'https://example.com' } },
      ]

      // Setup mocks
      const mockSelect = jest.fn()
        .mockReturnValueOnce({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({ data: mockSends, error: null })
          })
        })
        .mockReturnValueOnce({
          in: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({ data: mockOpenEvents, error: null })
          })
        })
        .mockReturnValueOnce({
          in: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({ data: mockClickEvents, error: null })
          })
        })

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await getCampaignTrackingStats(campaignId)

      expect(result).toEqual({
        totalSends: 3,
        opens: 3,
        clicks: 1,
        openRate: (2 / 3) * 100, // 2 unique opens out of 3 sends
        clickRate: (1 / 3) * 100, // 1 unique click out of 3 sends
        uniqueOpens: 2,
        uniqueClicks: 1,
      })
    })

    it('should handle campaigns with no sends', async () => {
      const campaignId = 'empty-campaign-id'
      
      const mockSelect = jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ data: [], error: null })
        })
      })

      ;(supabase.from as jest.Mock).mockReturnValue({ select: mockSelect })

      const result = await getCampaignTrackingStats(campaignId)

      expect(result).toEqual({
        totalSends: 0,
        opens: 0,
        clicks: 0,
        openRate: 0,
        clickRate: 0,
        uniqueOpens: 0,
        uniqueClicks: 0,
      })
    })
  })
})