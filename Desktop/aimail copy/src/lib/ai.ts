/**
 * AI service for generating email content using Perplexity API
 */

export interface AIGeneratedContent {
  subjects: string[]
  bodies: string[]
}

export interface AIGenerationRequest {
  productOrOffer: string
  goal: string
  targetAudience?: string
  tone?: 'professional' | 'casual' | 'friendly' | 'urgent'
}

export class AIService {
  private apiKey: string
  private baseUrl = 'https://api.perplexity.ai/chat/completions'

  constructor() {
    this.apiKey = process.env.PERPLEXITY_API_KEY || ''
    if (!this.apiKey && process.env.NODE_ENV !== 'test') {
      throw new Error('PERPLEXITY_API_KEY environment variable is required')
    }
  }

  async generateEmailContent(request: AIGenerationRequest): Promise<AIGeneratedContent> {
    const prompt = this.buildPrompt(request)
    
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'llama-3.1-sonar-small-128k-online',
          messages: [
            {
              role: 'system',
              content: 'You are an expert email marketing copywriter. Generate compelling email content that drives engagement and conversions.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.7,
        }),
      })

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return this.parseAIResponse(data.choices[0].message.content)
    } catch (error) {
      console.error('AI generation error:', error)
      throw new Error('Failed to generate AI content. Please try again.')
    }
  }

  private buildPrompt(request: AIGenerationRequest): string {
    const { productOrOffer, goal, targetAudience, tone = 'professional' } = request
    
    return `Create email marketing content for the following:

Product/Offer: ${productOrOffer}
Goal: ${goal}
Target Audience: ${targetAudience || 'General audience'}
Tone: ${tone}

Please provide:
1. 3 compelling subject lines that will increase open rates
2. 2 different email body versions (one shorter, one longer) in HTML format

Format your response as JSON:
{
  "subjects": ["subject1", "subject2", "subject3"],
  "bodies": ["<html>body1</html>", "<html>body2</html>"]
}

Make the content engaging, actionable, and optimized for conversions. Include a clear call-to-action in each body version.`
  }

  private parseAIResponse(content: string): AIGeneratedContent {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      
      // Validate the response structure
      if (!parsed.subjects || !Array.isArray(parsed.subjects) || parsed.subjects.length === 0) {
        throw new Error('Invalid subjects in AI response')
      }
      
      if (!parsed.bodies || !Array.isArray(parsed.bodies) || parsed.bodies.length === 0) {
        throw new Error('Invalid bodies in AI response')
      }

      return {
        subjects: parsed.subjects.slice(0, 3), // Ensure max 3 subjects
        bodies: parsed.bodies.slice(0, 2), // Ensure max 2 bodies
      }
    } catch (error) {
      console.error('Failed to parse AI response:', error)
      // Fallback response
      return {
        subjects: ['Generated Subject Line'],
        bodies: ['<p>Generated email content will appear here.</p>'],
      }
    }
  }
}

export const aiService = new AIService()