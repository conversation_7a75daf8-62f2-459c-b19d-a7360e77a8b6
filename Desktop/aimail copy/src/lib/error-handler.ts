import { NextRequest, NextResponse } from 'next/server'
import { ZodError } from 'zod'

export interface APIError {
  error: string
  message: string
  code?: string
  details?: any
  timestamp: string
  path?: string
}

export class AppError extends Error {
  public readonly statusCode: number
  public readonly code?: string
  public readonly details?: any

  constructor(
    message: string,
    statusCode: number = 500,
    code?: string,
    details?: any
  ) {
    super(message)
    this.name = 'AppError'
    this.statusCode = statusCode
    this.code = code
    this.details = details

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, AppError)
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR')
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR')
    this.name = 'NotFoundError'
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR')
    this.name = 'RateLimitError'
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, message?: string) {
    super(message || `${service} service unavailable`, 502, 'EXTERNAL_SERVICE_ERROR', { service })
    this.name = 'ExternalServiceError'
  }
}

export function formatAPIError(
  error: unknown,
  request?: NextRequest
): APIError {
  const timestamp = new Date().toISOString()
  const path = request?.nextUrl?.pathname

  // Handle known application errors
  if (error instanceof AppError) {
    return {
      error: error.name,
      message: error.message,
      code: error.code,
      details: error.details,
      timestamp,
      path
    }
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return {
      error: 'ValidationError',
      message: 'Invalid input data',
      code: 'VALIDATION_ERROR',
      details: error.issues?.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      })) || [],
      timestamp,
      path
    }
  }

  // Handle generic errors
  if (error instanceof Error) {
    // Don't expose internal error details in production
    const message = process.env.NODE_ENV === 'production' 
      ? 'An internal server error occurred'
      : error.message

    return {
      error: 'InternalServerError',
      message,
      code: 'INTERNAL_SERVER_ERROR',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp,
      path
    }
  }

  // Handle unknown errors
  return {
    error: 'UnknownError',
    message: 'An unknown error occurred',
    code: 'UNKNOWN_ERROR',
    timestamp,
    path
  }
}

export function createErrorResponse(error: unknown, request?: NextRequest): NextResponse {
  const apiError = formatAPIError(error, request)
  
  // Log error for monitoring
  logError(error, request)
  
  // Determine status code
  let statusCode = 500
  if (error instanceof AppError) {
    statusCode = error.statusCode
  } else if (error instanceof ZodError) {
    statusCode = 400
  }

  return NextResponse.json(apiError, { status: statusCode })
}

export function logError(error: unknown, request?: NextRequest) {
  const errorInfo = {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    url: request?.url,
    method: request?.method,
    userAgent: request?.headers.get('user-agent'),
    timestamp: new Date().toISOString()
  }

  console.error('API Error:', errorInfo)

  // In production, send to error monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error, { extra: errorInfo })
  }
}

// Middleware wrapper for API routes
export function withErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context)
    } catch (error) {
      return createErrorResponse(error, request)
    }
  }
}

// Async error handler for promises
export function handleAsyncError<T>(
  promise: Promise<T>,
  errorMessage?: string
): Promise<T> {
  return promise.catch((error) => {
    if (errorMessage) {
      throw new AppError(errorMessage, 500, 'ASYNC_ERROR', { originalError: error.message })
    }
    throw error
  })
}