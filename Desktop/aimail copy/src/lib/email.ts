import { SESClient, SendEmailCommand, SendEmailCommandInput } from '@aws-sdk/client-ses'
import { env } from './env'
import { isEmailSuppressed } from './email-webhooks'

// Rate limiting configuration
const RATE_LIMIT = {
  maxSendRate: 14, // emails per second (SES default is 14/sec)
  maxDailyQuota: 200, // daily quota (SES default is 200/day)
  batchSize: 50, // emails per batch
  delayBetweenBatches: 1000, // ms delay between batches
}

// Initialize SES client
const sesClient = new SESClient({
  region: env.AWS_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
})

export interface EmailTemplate {
  subject: string
  htmlBody: string
  textBody?: string
}

export interface EmailRecipient {
  email: string
  name?: string
}

export interface SendEmailResult {
  messageId: string
  success: boolean
  error?: string
}

export interface BulkSendResult {
  totalSent: number
  totalFailed: number
  results: SendEmailResult[]
}

export class EmailService {
  private sendCount = 0
  private lastResetTime = Date.now()
  private dailySendCount = 0
  private lastDailyReset = new Date().toDateString()

  /**
   * Send a single email using Amazon SES
   */
  async sendEmail(
    recipient: EmailRecipient,
    template: EmailTemplate,
    fromEmail?: string,
    userId?: string
  ): Promise<SendEmailResult> {
    try {
      // Check if email is suppressed (if userId provided)
      if (userId && await isEmailSuppressed(userId, recipient.email)) {
        return {
          messageId: '',
          success: false,
          error: 'Email address is suppressed (bounced, complained, or unsubscribed)',
        }
      }

      // Check rate limits
      await this.checkRateLimit()

      const params: SendEmailCommandInput = {
        Source: fromEmail || env.SES_FROM_EMAIL,
        Destination: {
          ToAddresses: [recipient.email],
        },
        Message: {
          Subject: {
            Data: template.subject,
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: template.htmlBody,
              Charset: 'UTF-8',
            },
            ...(template.textBody && {
              Text: {
                Data: template.textBody,
                Charset: 'UTF-8',
              },
            }),
          },
        },
      }

      const command = new SendEmailCommand(params)
      const result = await sesClient.send(command)

      // Update rate limiting counters
      this.updateRateLimitCounters()

      return {
        messageId: result.MessageId || '',
        success: true,
      }
    } catch (error) {
      console.error('Failed to send email:', error)
      return {
        messageId: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Send emails in bulk with rate limiting and batch processing
   */
  async sendBulkEmails(
    recipients: EmailRecipient[],
    template: EmailTemplate,
    fromEmail?: string,
    userId?: string
  ): Promise<BulkSendResult> {
    const results: SendEmailResult[] = []
    let totalSent = 0
    let totalFailed = 0

    // Process recipients in batches
    for (let i = 0; i < recipients.length; i += RATE_LIMIT.batchSize) {
      const batch = recipients.slice(i, i + RATE_LIMIT.batchSize)
      
      // Send emails in current batch
      const batchPromises = batch.map(recipient =>
        this.sendEmail(recipient, template, fromEmail, userId)
      )

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Count successes and failures
      batchResults.forEach(result => {
        if (result.success) {
          totalSent++
        } else {
          totalFailed++
        }
      })

      // Add delay between batches to respect rate limits
      if (i + RATE_LIMIT.batchSize < recipients.length) {
        await this.delay(RATE_LIMIT.delayBetweenBatches)
      }
    }

    return {
      totalSent,
      totalFailed,
      results,
    }
  }

  /**
   * Render email template with personalization
   */
  renderTemplate(
    template: EmailTemplate,
    variables: Record<string, string> = {}
  ): EmailTemplate {
    const renderContent = (content: string): string => {
      let rendered = content
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
        rendered = rendered.replace(placeholder, value)
      })
      return rendered
    }

    return {
      subject: renderContent(template.subject),
      htmlBody: renderContent(template.htmlBody),
      textBody: template.textBody ? renderContent(template.textBody) : undefined,
    }
  }

  /**
   * Generate HTML email template with tracking pixels and links
   * @deprecated Use generateTrackedEmailHtml from tracking.ts instead
   */
  generateEmailWithTracking(
    htmlBody: string,
    trackingData: {
      sendId: string
      baseUrl: string
    }
  ): string {
    let trackedHtml = htmlBody

    // Add tracking pixel for opens
    const trackingPixel = `<img src="${trackingData.baseUrl}/api/track/open?send_id=${trackingData.sendId}" width="1" height="1" style="display:none;" alt="" />`
    
    // Insert tracking pixel before closing body tag, or at the end if no body tag
    if (trackedHtml.includes('</body>')) {
      trackedHtml = trackedHtml.replace('</body>', `${trackingPixel}</body>`)
    } else {
      trackedHtml += trackingPixel
    }

    // Replace links with tracking links
    const linkRegex = /<a\s+([^>]*?)href=["']([^"']+)["']([^>]*?)>/gi
    trackedHtml = trackedHtml.replace(linkRegex, (match, beforeHref, url, afterHref) => {
      // Skip if already a tracking link
      if (url.includes('/api/track/click')) {
        return match
      }

      const trackingUrl = `${trackingData.baseUrl}/api/track/click?send_id=${trackingData.sendId}&url=${encodeURIComponent(url)}`
      return `<a ${beforeHref}href="${trackingUrl}"${afterHref}>`
    })

    return trackedHtml
  }

  /**
   * Check if we're within rate limits
   */
  private async checkRateLimit(): Promise<void> {
    const now = Date.now()
    const today = new Date().toDateString()

    // Reset daily counter if it's a new day
    if (this.lastDailyReset !== today) {
      this.dailySendCount = 0
      this.lastDailyReset = today
    }

    // Reset per-second counter if more than 1 second has passed
    if (now - this.lastResetTime >= 1000) {
      this.sendCount = 0
      this.lastResetTime = now
    }

    // Check daily quota
    if (this.dailySendCount >= RATE_LIMIT.maxDailyQuota) {
      throw new Error('Daily email quota exceeded')
    }

    // Check per-second rate limit
    if (this.sendCount >= RATE_LIMIT.maxSendRate) {
      // Wait until next second
      const waitTime = 1000 - (now - this.lastResetTime)
      if (waitTime > 0) {
        await this.delay(waitTime)
      }
      this.sendCount = 0
      this.lastResetTime = Date.now()
    }
  }

  /**
   * Update rate limiting counters
   */
  private updateRateLimitCounters(): void {
    this.sendCount++
    this.dailySendCount++
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get current rate limit status
   */
  getRateLimitStatus() {
    const now = Date.now()
    const today = new Date().toDateString()

    return {
      currentSendRate: this.sendCount,
      maxSendRate: RATE_LIMIT.maxSendRate,
      dailySendCount: this.lastDailyReset === today ? this.dailySendCount : 0,
      maxDailyQuota: RATE_LIMIT.maxDailyQuota,
      timeUntilReset: Math.max(0, 1000 - (now - this.lastResetTime)),
    }
  }
}

// Export singleton instance
export const emailService = new EmailService()