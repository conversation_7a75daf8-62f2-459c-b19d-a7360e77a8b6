import { db } from './database'
import type {
  Contact,
  CreateContactData,
  UpdateContactData,
  ContactFilters,
  DatabaseResponse,
  DatabaseListResponse
} from '../types/database'

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// Contact validation utilities
export const contactValidation = {
  validateEmail: (email: string): boolean => {
    return EMAIL_REGEX.test(email.trim().toLowerCase())
  },

  validateName: (name?: string): boolean => {
    if (name === undefined || name === null) return true // Name is optional
    const trimmed = name.trim()
    return trimmed.length > 0 && trimmed.length <= 255
  },

  validateStatus: (status: string): status is 'active' | 'unsubscribed' | 'bounced' => {
    return ['active', 'unsubscribed', 'bounced'].includes(status)
  },

  validateContactData: (data: Partial<CreateContactData>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (!data.email) {
      errors.push('Email is required')
    } else if (!contactValidation.validateEmail(data.email)) {
      errors.push('Invalid email format')
    }

    if (!data.user_id) {
      errors.push('User ID is required')
    }

    if (data.name && !contactValidation.validateName(data.name)) {
      errors.push('Name must be between 1 and 255 characters')
    }

    if (data.status && !contactValidation.validateStatus(data.status)) {
      errors.push('Status must be active, unsubscribed, or bounced')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  sanitizeContactData: (data: CreateContactData): CreateContactData => {
    return {
      ...data,
      email: data.email.trim().toLowerCase(),
      name: data.name?.trim() || undefined,
      status: data.status || 'active'
    }
  }
}

// Contact service functions
export class ContactService {
  /**
   * Get contacts with optional filtering and pagination
   */
  async getContacts(userId: string, filters?: ContactFilters): Promise<DatabaseListResponse<Contact>> {
    if (!userId) {
      return {
        data: [],
        error: new Error('User ID is required'),
        count: 0
      }
    }

    return await db.getContacts(userId, filters)
  }

  /**
   * Get a single contact by ID
   */
  async getContactById(contactId: string, userId: string): Promise<DatabaseResponse<Contact>> {
    if (!contactId || !userId) {
      return {
        data: null,
        error: new Error('Contact ID and User ID are required')
      }
    }

    try {
      const { data: contacts } = await db.getContacts(userId, { limit: 1 })
      const contact = contacts.find(c => c.id === contactId)
      
      return {
        data: contact || null,
        error: contact ? null : new Error('Contact not found')
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  /**
   * Create a new contact
   */
  async createContact(contactData: CreateContactData): Promise<DatabaseResponse<Contact>> {
    // Validate input data
    const validation = contactValidation.validateContactData(contactData)
    if (!validation.isValid) {
      return {
        data: null,
        error: new Error(`Validation failed: ${validation.errors.join(', ')}`)
      }
    }

    // Sanitize data
    const sanitizedData = contactValidation.sanitizeContactData(contactData)

    // Check if contact already exists
    const existingContacts = await db.getContacts(sanitizedData.user_id, {
      search: sanitizedData.email,
      limit: 1
    })

    if (existingContacts.data.length > 0) {
      return {
        data: null,
        error: new Error('Contact with this email already exists')
      }
    }

    // Check if email is suppressed
    const isSuppressed = await db.isEmailSuppressed(sanitizedData.user_id, sanitizedData.email)
    if (isSuppressed) {
      return {
        data: null,
        error: new Error('Cannot add contact: email is in suppression list')
      }
    }

    return await db.createContact(sanitizedData)
  }

  /**
   * Update an existing contact
   */
  async updateContact(contactData: UpdateContactData, userId: string): Promise<DatabaseResponse<Contact>> {
    if (!contactData.id || !userId) {
      return {
        data: null,
        error: new Error('Contact ID and User ID are required')
      }
    }

    // Validate update data
    if (contactData.email) {
      if (!contactValidation.validateEmail(contactData.email)) {
        return {
          data: null,
          error: new Error('Invalid email format')
        }
      }

      // Check if email already exists for another contact
      const existingContacts = await db.getContacts(userId, {
        search: contactData.email,
        limit: 10
      })

      const duplicateContact = existingContacts.data.find(c => 
        c.email.toLowerCase() === contactData.email!.toLowerCase() && c.id !== contactData.id
      )

      if (duplicateContact) {
        return {
          data: null,
          error: new Error('Another contact with this email already exists')
        }
      }
    }

    if (contactData.name && !contactValidation.validateName(contactData.name)) {
      return {
        data: null,
        error: new Error('Name must be between 1 and 255 characters')
      }
    }

    if (contactData.status && !contactValidation.validateStatus(contactData.status)) {
      return {
        data: null,
        error: new Error('Status must be active, unsubscribed, or bounced')
      }
    }

    // Sanitize email if provided
    const sanitizedData = {
      ...contactData,
      email: contactData.email?.trim().toLowerCase(),
      name: contactData.name?.trim()
    }

    return await db.updateContact(sanitizedData)
  }

  /**
   * Delete a contact
   */
  async deleteContact(contactId: string, userId: string): Promise<DatabaseResponse<boolean>> {
    if (!contactId || !userId) {
      return {
        data: false,
        error: new Error('Contact ID and User ID are required')
      }
    }

    // Verify contact belongs to user
    const contact = await this.getContactById(contactId, userId)
    if (!contact.data) {
      return {
        data: false,
        error: new Error('Contact not found or access denied')
      }
    }

    return await db.deleteContact(contactId)
  }

  /**
   * Bulk create contacts (for CSV import)
   */
  async bulkCreateContacts(
    contactsData: CreateContactData[],
    userId: string
  ): Promise<{
    successful: Contact[]
    failed: { data: CreateContactData; error: string }[]
    duplicates: CreateContactData[]
  }> {
    const successful: Contact[] = []
    const failed: { data: CreateContactData; error: string }[] = []
    const duplicates: CreateContactData[] = []

    // Get existing contacts to check for duplicates
    const existingContacts = await db.getContacts(userId)
    const existingEmails = new Set(
      existingContacts.data.map(c => c.email.toLowerCase())
    )

    for (const contactData of contactsData) {
      // Check for duplicates
      if (existingEmails.has(contactData.email.toLowerCase())) {
        duplicates.push(contactData)
        continue
      }

      // Attempt to create contact
      const result = await this.createContact(contactData)
      
      if (result.data) {
        successful.push(result.data)
        existingEmails.add(contactData.email.toLowerCase()) // Prevent duplicates within the batch
      } else {
        failed.push({
          data: contactData,
          error: result.error?.message || 'Unknown error'
        })
      }
    }

    return { successful, failed, duplicates }
  }

  /**
   * Update contact status (for bounce/complaint handling)
   */
  async updateContactStatus(
    email: string,
    userId: string,
    status: 'active' | 'unsubscribed' | 'bounced',
    reason?: string
  ): Promise<DatabaseResponse<Contact>> {
    if (!email || !userId) {
      return {
        data: null,
        error: new Error('Email and User ID are required')
      }
    }

    // Find contact by email
    const contacts = await db.getContacts(userId, {
      search: email,
      limit: 1
    })

    const contact = contacts.data.find(c => 
      c.email.toLowerCase() === email.toLowerCase()
    )

    if (!contact) {
      return {
        data: null,
        error: new Error('Contact not found')
      }
    }

    // Update contact status
    const result = await db.updateContact({
      id: contact.id,
      status
    })

    // Add to suppression list if bounced or unsubscribed
    if (result.data && (status === 'bounced' || status === 'unsubscribed')) {
      const suppressionReason = status === 'bounced' ? 'bounce' : 'unsubscribe'
      await db.updateSuppressionList(userId, email, suppressionReason)
    }

    return result
  }

  /**
   * Get active contacts for campaign sending
   */
  async getActiveContacts(userId: string): Promise<DatabaseListResponse<Contact>> {
    if (!userId) {
      return {
        data: [],
        error: new Error('User ID is required'),
        count: 0
      }
    }

    return await db.getContacts(userId, {
      status: 'active',
      limit: 10000 // Large limit to get all active contacts
    })
  }

  /**
   * Get contact statistics
   */
  async getContactStats(userId: string): Promise<{
    total: number
    active: number
    unsubscribed: number
    bounced: number
  }> {
    const contacts = await db.getContacts(userId)
    
    const stats = {
      total: contacts.data.length,
      active: contacts.data.filter(c => c.status === 'active').length,
      unsubscribed: contacts.data.filter(c => c.status === 'unsubscribed').length,
      bounced: contacts.data.filter(c => c.status === 'bounced').length
    }

    return stats
  }
}

// Export singleton instance
export const contactService = new ContactService()