import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface AuditLogEntry {
  user_id: string
  action: string
  resource_type: string
  resource_id?: string
  details?: Record<string, any>
  ip_address?: string
  user_agent?: string
  timestamp?: string
}

export enum AuditAction {
  // Authentication
  LOGIN = 'login',
  LOGOUT = 'logout',
  LOGIN_FAILED = 'login_failed',
  
  // Campaign operations
  CAMPAIGN_CREATED = 'campaign_created',
  CAMPAIGN_UPDATED = 'campaign_updated',
  CAMPAIGN_DELETED = 'campaign_deleted',
  CAMPAIGN_SENT = 'campaign_sent',
  CAMPAIGN_DUPLICATED = 'campaign_duplicated',
  
  // Contact operations
  CONTACT_CREATED = 'contact_created',
  CONTACT_UPDATED = 'contact_updated',
  CONTACT_DELETED = 'contact_deleted',
  CONTACTS_IMPORTED = 'contacts_imported',
  CONTACT_SUPPRESSED = 'contact_suppressed',
  
  // Settings operations
  SETTINGS_UPDATED = 'settings_updated',
  API_KEY_UPDATED = 'api_key_updated',
  BRANDING_UPDATED = 'branding_updated',
  
  // Data operations
  DATA_EXPORTED = 'data_exported',
  DATA_IMPORTED = 'data_imported',
  
  // Security events
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  CSRF_VIOLATION = 'csrf_violation',
  UNAUTHORIZED_ACCESS = 'unauthorized_access'
}

export enum ResourceType {
  USER = 'user',
  CAMPAIGN = 'campaign',
  CONTACT = 'contact',
  SETTINGS = 'settings',
  SYSTEM = 'system'
}

/**
 * Log an audit event
 */
export async function logAuditEvent(entry: AuditLogEntry): Promise<void> {
  try {
    const { error } = await supabase
      .from('audit_logs')
      .insert({
        user_id: entry.user_id,
        action: entry.action,
        resource_type: entry.resource_type,
        resource_id: entry.resource_id,
        details: entry.details,
        ip_address: entry.ip_address,
        user_agent: entry.user_agent,
        created_at: entry.timestamp || new Date().toISOString()
      })

    if (error) {
      console.error('Failed to log audit event:', error)
    }
  } catch (error) {
    console.error('Audit logging error:', error)
  }
}

/**
 * Get audit logs for a user
 */
export async function getAuditLogs(
  userId: string,
  options: {
    limit?: number
    offset?: number
    action?: string
    resourceType?: string
    startDate?: string
    endDate?: string
  } = {}
) {
  let query = supabase
    .from('audit_logs')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })

  if (options.action) {
    query = query.eq('action', options.action)
  }

  if (options.resourceType) {
    query = query.eq('resource_type', options.resourceType)
  }

  if (options.startDate) {
    query = query.gte('created_at', options.startDate)
  }

  if (options.endDate) {
    query = query.lte('created_at', options.endDate)
  }

  if (options.limit) {
    query = query.limit(options.limit)
  }

  if (options.offset) {
    query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Failed to fetch audit logs: ${error.message}`)
  }

  return data
}

/**
 * Helper function to extract request metadata
 */
export function getRequestMetadata(request: Request) {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0].trim() : 
             request.headers.get('x-real-ip') || 
             'unknown'
  
  return {
    ip_address: ip,
    user_agent: request.headers.get('user-agent') || 'unknown'
  }
}

/**
 * Audit logging middleware for API routes
 */
export function withAuditLog(
  action: AuditAction,
  resourceType: ResourceType,
  handler: (request: Request, context: any) => Promise<Response>
) {
  return async (request: Request, context: any) => {
    const startTime = Date.now()
    let response: Response
    let error: Error | null = null

    try {
      response = await handler(request, context)
    } catch (e) {
      error = e as Error
      response = new Response('Internal Server Error', { status: 500 })
    }

    // Extract user ID from request (you may need to adjust this based on your auth setup)
    const userId = request.headers.get('x-user-id') || 'anonymous'
    const metadata = getRequestMetadata(request)

    // Log the audit event
    await logAuditEvent({
      user_id: userId,
      action,
      resource_type: resourceType,
      details: {
        method: request.method,
        url: request.url,
        status: response.status,
        duration: Date.now() - startTime,
        error: error?.message
      },
      ...metadata
    })

    if (error) {
      throw error
    }

    return response
  }
}