import { z } from 'zod'
import { SESClient, GetSendQuotaCommand, VerifyEmailIdentityCommand } from '@aws-sdk/client-ses'
import { BrandingConfig } from '../config/branding'

// Settings validation schemas
export const sesConfigSchema = z.object({
  accessKeyId: z.string().min(1, 'AWS Access Key ID is required'),
  secretAccessKey: z.string().min(1, 'AWS Secret Access Key is required'),
  region: z.string().min(1, 'AWS Region is required'),
  fromEmail: z.string().email('Valid email address is required'),
})

export const perplexityConfigSchema = z.object({
  apiKey: z.string().min(1, 'Perplexity API key is required'),
})

export const brandingConfigSchema = z.object({
  appName: z.string().min(1, 'App name is required'),
  logo: z.string().min(1, 'Logo URL is required'),
  colors: z.object({
    primary: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    secondary: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
    accent: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
  }),
  theme: z.object({
    borderRadius: z.string().min(1, 'Border radius is required'),
    fontFamily: z.string().min(1, 'Font family is required'),
  }),
})

export const settingsSchema = z.object({
  ses: sesConfigSchema,
  perplexity: perplexityConfigSchema,
  branding: brandingConfigSchema,
})

export type SESConfig = z.infer<typeof sesConfigSchema>
export type PerplexityConfig = z.infer<typeof perplexityConfigSchema>
export type SettingsConfig = z.infer<typeof settingsSchema>

// Settings service
export class SettingsService {
  /**
   * Validate SES configuration by testing connection and quota
   */
  static async validateSESConfig(config: SESConfig): Promise<{
    valid: boolean
    error?: string
    quota?: {
      max24HourSend: number
      maxSendRate: number
      sentLast24Hours: number
    }
  }> {
    try {
      const sesClient = new SESClient({
        region: config.region,
        credentials: {
          accessKeyId: config.accessKeyId,
          secretAccessKey: config.secretAccessKey,
        },
      })

      // Test connection by getting send quota
      const quotaCommand = new GetSendQuotaCommand({})
      const quotaResponse = await sesClient.send(quotaCommand)

      return {
        valid: true,
        quota: {
          max24HourSend: quotaResponse.Max24HourSend || 0,
          maxSendRate: quotaResponse.MaxSendRate || 0,
          sentLast24Hours: quotaResponse.SentLast24Hours || 0,
        },
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown SES error',
      }
    }
  }

  /**
   * Test SES email identity verification
   */
  static async testSESEmailIdentity(config: SESConfig): Promise<{
    valid: boolean
    error?: string
  }> {
    try {
      const sesClient = new SESClient({
        region: config.region,
        credentials: {
          accessKeyId: config.accessKeyId,
          secretAccessKey: config.secretAccessKey,
        },
      })

      // Attempt to verify email identity (this will send a verification email if not already verified)
      const verifyCommand = new VerifyEmailIdentityCommand({
        EmailAddress: config.fromEmail,
      })
      
      await sesClient.send(verifyCommand)

      return { valid: true }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown email verification error',
      }
    }
  }

  /**
   * Validate Perplexity API configuration
   */
  static async validatePerplexityConfig(config: PerplexityConfig): Promise<{
    valid: boolean
    error?: string
  }> {
    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'llama-3.1-sonar-small-128k-online',
          messages: [
            {
              role: 'user',
              content: 'Test connection - respond with "OK"',
            },
          ],
          max_tokens: 10,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        return {
          valid: false,
          error: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
        }
      }

      return { valid: true }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown Perplexity API error',
      }
    }
  }

  /**
   * Export settings configuration
   */
  static exportSettings(settings: SettingsConfig): string {
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: {
        // Don't export sensitive keys, only structure
        ses: {
          region: settings.ses.region,
          fromEmail: settings.ses.fromEmail,
        },
        branding: settings.branding,
      },
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * Import and validate settings configuration
   */
  static importSettings(jsonData: string): {
    valid: boolean
    settings?: Partial<SettingsConfig>
    error?: string
  } {
    try {
      const data = JSON.parse(jsonData)

      if (!data.settings) {
        return {
          valid: false,
          error: 'Invalid settings file format',
        }
      }

      // Validate branding if present
      let branding: BrandingConfig | undefined
      if (data.settings.branding) {
        const brandingResult = brandingConfigSchema.safeParse(data.settings.branding)
        if (!brandingResult.success) {
          return {
            valid: false,
            error: `Invalid branding configuration: ${brandingResult.error.errors[0]?.message}`,
          }
        }
        branding = brandingResult.data
      }

      return {
        valid: true,
        settings: {
          branding,
          // SES and Perplexity configs need to be entered manually for security
        },
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Invalid JSON format',
      }
    }
  }
}