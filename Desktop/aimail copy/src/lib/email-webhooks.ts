import { supabaseAdmin } from './supabase'

/**
 * SES Bounce Notification Interface
 */
export interface SESBounceNotification {
  notificationType: 'Bounce'
  bounce: {
    bounceType: 'Permanent' | 'Transient'
    bounceSubType: string
    bouncedRecipients: Array<{
      emailAddress: string
      action?: string
      status?: string
      diagnosticCode?: string
    }>
    timestamp: string
    feedbackId: string
    reportingMTA?: string
  }
  mail: {
    timestamp: string
    messageId: string
    source: string
    sourceArn?: string
    sendingAccountId?: string
    destination: string[]
    headersTruncated?: boolean
    headers?: Array<{
      name: string
      value: string
    }>
    commonHeaders?: {
      from?: string[]
      to?: string[]
      messageId?: string
      subject?: string
    }
  }
}

/**
 * SES Complaint Notification Interface
 */
export interface SESComplaintNotification {
  notificationType: 'Complaint'
  complaint: {
    complainedRecipients: Array<{
      emailAddress: string
    }>
    timestamp: string
    feedbackId: string
    userAgent?: string
    complaintFeedbackType?: string
    arrivalDate?: string
  }
  mail: {
    timestamp: string
    messageId: string
    source: string
    sourceArn?: string
    sendingAccountId?: string
    destination: string[]
    headersTruncated?: boolean
    headers?: Array<{
      name: string
      value: string
    }>
    commonHeaders?: {
      from?: string[]
      to?: string[]
      messageId?: string
      subject?: string
    }
  }
}

/**
 * Handle bounce notifications from SES
 */
export async function handleBounceNotification(notification: SESBounceNotification): Promise<void> {
  try {
    const { bounce, mail } = notification
    const messageId = mail.messageId

    console.log(`Processing bounce notification for message ${messageId}`)

    // Find the send record using SES message ID
    const { data: sendRecord, error: sendError } = await supabaseAdmin
      .from('sends')
      .select(`
        id,
        campaign_id,
        contact_id,
        contacts!inner(email, user_id)
      `)
      .eq('ses_message_id', messageId)
      .single()

    if (sendError || !sendRecord) {
      console.error(`Send record not found for message ID ${messageId}:`, sendError)
      return
    }

    // Process each bounced recipient
    for (const recipient of bounce.bouncedRecipients) {
      const email = recipient.emailAddress
      const userId = sendRecord.contacts.user_id

      console.log(`Processing bounce for email: ${email}, type: ${bounce.bounceType}`)

      // Update send status to bounced
      await supabaseAdmin
        .from('sends')
        .update({
          status: 'bounced',
          error_message: `Bounce: ${bounce.bounceSubType} - ${recipient.diagnosticCode || 'No diagnostic code'}`
        })
        .eq('id', sendRecord.id)

      // Create bounce event
      await supabaseAdmin
        .from('events')
        .insert({
          send_id: sendRecord.id,
          type: 'bounce',
          metadata: {
            bounceType: bounce.bounceType,
            bounceSubType: bounce.bounceSubType,
            diagnosticCode: recipient.diagnosticCode,
            feedbackId: bounce.feedbackId,
            timestamp: bounce.timestamp
          }
        })

      // For permanent bounces, update contact status and add to suppression list
      if (bounce.bounceType === 'Permanent') {
        // Update contact status to bounced
        await supabaseAdmin
          .from('contacts')
          .update({ status: 'bounced' })
          .eq('email', email)
          .eq('user_id', userId)

        // Add to suppression list (use upsert to handle duplicates)
        await supabaseAdmin
          .from('suppression')
          .upsert({
            user_id: userId,
            email: email,
            reason: 'bounce'
          }, {
            onConflict: 'user_id,email'
          })

        console.log(`Added ${email} to suppression list due to permanent bounce`)
      }
      
      // For transient bounces, just log the event but don't suppress
      else {
        console.log(`Transient bounce recorded for ${email}, not adding to suppression list`)
      }
    }

    console.log(`Bounce notification processed successfully for message ${messageId}`)

  } catch (error) {
    console.error('Error handling bounce notification:', error)
    
    // Log error for monitoring
    const errorLog = {
      error: error instanceof Error ? error.message : 'Unknown error',
      notification: notification,
      timestamp: new Date().toISOString(),
      type: 'bounce_processing_error'
    }
    
    console.error('Bounce processing error details:', errorLog)
    throw error
  }
}

/**
 * Handle complaint notifications from SES
 */
export async function handleComplaintNotification(notification: SESComplaintNotification): Promise<void> {
  try {
    const { complaint, mail } = notification
    const messageId = mail.messageId

    console.log(`Processing complaint notification for message ${messageId}`)

    // Find the send record using SES message ID
    const { data: sendRecord, error: sendError } = await supabaseAdmin
      .from('sends')
      .select(`
        id,
        campaign_id,
        contact_id,
        contacts!inner(email, user_id)
      `)
      .eq('ses_message_id', messageId)
      .single()

    if (sendError || !sendRecord) {
      console.error(`Send record not found for message ID ${messageId}:`, sendError)
      return
    }

    // Process each complained recipient
    for (const recipient of complaint.complainedRecipients) {
      const email = recipient.emailAddress
      const userId = sendRecord.contacts.user_id

      console.log(`Processing complaint for email: ${email}`)

      // Create complaint event
      await supabaseAdmin
        .from('events')
        .insert({
          send_id: sendRecord.id,
          type: 'complaint',
          metadata: {
            complaintFeedbackType: complaint.complaintFeedbackType,
            userAgent: complaint.userAgent,
            feedbackId: complaint.feedbackId,
            timestamp: complaint.timestamp
          }
        })

      // Update contact status to unsubscribed (complaints are treated as unsubscribes)
      await supabaseAdmin
        .from('contacts')
        .update({ status: 'unsubscribed' })
        .eq('email', email)
        .eq('user_id', userId)

      // Add to suppression list (use upsert to handle duplicates)
      await supabaseAdmin
        .from('suppression')
        .upsert({
          user_id: userId,
          email: email,
          reason: 'complaint'
        }, {
          onConflict: 'user_id,email'
        })

      console.log(`Added ${email} to suppression list due to complaint`)
    }

    console.log(`Complaint notification processed successfully for message ${messageId}`)

  } catch (error) {
    console.error('Error handling complaint notification:', error)
    
    // Log error for monitoring
    const errorLog = {
      error: error instanceof Error ? error.message : 'Unknown error',
      notification: notification,
      timestamp: new Date().toISOString(),
      type: 'complaint_processing_error'
    }
    
    console.error('Complaint processing error details:', errorLog)
    throw error
  }
}

/**
 * Get suppression list for a user
 */
export async function getSuppressionList(userId: string): Promise<{
  email: string
  reason: string
  created_at: string
}[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('suppression')
      .select('email, reason, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching suppression list:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error in getSuppressionList:', error)
    return []
  }
}

/**
 * Check if an email is suppressed for a user
 */
export async function isEmailSuppressed(userId: string, email: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .from('suppression')
      .select('id')
      .eq('user_id', userId)
      .eq('email', email)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking suppression status:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error in isEmailSuppressed:', error)
    return false
  }
}

/**
 * Add email to suppression list manually
 */
export async function addToSuppressionList(
  userId: string,
  email: string,
  reason: 'bounce' | 'complaint' | 'unsubscribe'
): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('suppression')
      .upsert({
        user_id: userId,
        email: email,
        reason: reason
      }, {
        onConflict: 'user_id,email'
      })

    if (error) {
      console.error('Error adding to suppression list:', error)
      return false
    }

    // Also update contact status if contact exists
    await supabaseAdmin
      .from('contacts')
      .update({ 
        status: reason === 'bounce' ? 'bounced' : 'unsubscribed' 
      })
      .eq('user_id', userId)
      .eq('email', email)

    return true
  } catch (error) {
    console.error('Error in addToSuppressionList:', error)
    return false
  }
}

/**
 * Remove email from suppression list
 */
export async function removeFromSuppressionList(
  userId: string,
  email: string
): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('suppression')
      .delete()
      .eq('user_id', userId)
      .eq('email', email)

    if (error) {
      console.error('Error removing from suppression list:', error)
      return false
    }

    // Also update contact status back to active if contact exists
    await supabaseAdmin
      .from('contacts')
      .update({ status: 'active' })
      .eq('user_id', userId)
      .eq('email', email)

    return true
  } catch (error) {
    console.error('Error in removeFromSuppressionList:', error)
    return false
  }
}

/**
 * Get bounce and complaint statistics for monitoring
 */
export async function getWebhookStats(userId?: string) {
  try {
    let bounceQuery = supabaseAdmin
      .from('events')
      .select('*', { count: 'exact', head: true })
      .eq('type', 'bounce')

    let complaintQuery = supabaseAdmin
      .from('events')
      .select('*', { count: 'exact', head: true })
      .eq('type', 'complaint')

    // If userId provided, filter by user's sends
    if (userId) {
      bounceQuery = bounceQuery.in('send_id', 
        supabaseAdmin
          .from('sends')
          .select('id')
          .in('campaign_id', 
            supabaseAdmin
              .from('campaigns')
              .select('id')
              .eq('user_id', userId)
          )
      )

      complaintQuery = complaintQuery.in('send_id',
        supabaseAdmin
          .from('sends')
          .select('id')
          .in('campaign_id',
            supabaseAdmin
              .from('campaigns')
              .select('id')
              .eq('user_id', userId)
          )
      )
    }

    const [bounceResult, complaintResult] = await Promise.all([
      bounceQuery,
      complaintQuery
    ])

    return {
      bounces: bounceResult.count || 0,
      complaints: complaintResult.count || 0,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('Error getting webhook stats:', error)
    return {
      bounces: 0,
      complaints: 0,
      timestamp: new Date().toISOString()
    }
  }
}