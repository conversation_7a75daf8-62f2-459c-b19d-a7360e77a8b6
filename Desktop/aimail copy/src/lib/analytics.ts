import { supabase } from './supabase'
import { getCampaignTrackingStats } from './tracking'

export interface CampaignAnalytics {
  campaignId: string
  campaignName: string
  subject: string
  status: string
  sentAt?: string
  totalSends: number
  opens: number
  clicks: number
  bounces: number
  complaints: number
  openRate: number
  clickRate: number
  bounceRate: number
  complaintRate: number
  uniqueOpens: number
  uniqueClicks: number
}

export interface DashboardMetrics {
  totalCampaigns: number
  totalContacts: number
  totalSends: number
  totalOpens: number
  totalClicks: number
  averageOpenRate: number
  averageClickRate: number
  recentCampaigns: CampaignAnalytics[]
}

export interface TimeSeriesData {
  date: string
  opens: number
  clicks: number
  sends: number
}

/**
 * Get comprehensive analytics for a specific campaign
 */
export async function getCampaignAnalytics(campaignId: string, userId: string): Promise<CampaignAnalytics> {
  try {
    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('campaigns')
      .select('id, name, subject, status, sent_at')
      .eq('id', campaignId)
      .eq('user_id', userId)
      .single()

    if (campaignError || !campaign) {
      throw new Error('Campaign not found')
    }

    // Get tracking statistics
    const trackingStats = await getCampaignTrackingStats(campaignId)

    // Get bounce and complaint counts
    const { data: bounceComplaintEvents, error: eventsError } = await supabase
      .from('events')
      .select(`
        type,
        sends!inner(
          campaign_id
        )
      `)
      .eq('sends.campaign_id', campaignId)
      .in('type', ['bounce', 'complaint'])

    if (eventsError) {
      throw eventsError
    }

    const bounces = bounceComplaintEvents?.filter(event => event.type === 'bounce').length || 0
    const complaints = bounceComplaintEvents?.filter(event => event.type === 'complaint').length || 0

    return {
      campaignId: campaign.id,
      campaignName: campaign.name,
      subject: campaign.subject,
      status: campaign.status,
      sentAt: campaign.sent_at,
      totalSends: trackingStats.totalSends,
      opens: trackingStats.opens,
      clicks: trackingStats.clicks,
      bounces,
      complaints,
      openRate: trackingStats.openRate,
      clickRate: trackingStats.clickRate,
      bounceRate: trackingStats.totalSends > 0 ? (bounces / trackingStats.totalSends) * 100 : 0,
      complaintRate: trackingStats.totalSends > 0 ? (complaints / trackingStats.totalSends) * 100 : 0,
      uniqueOpens: trackingStats.uniqueOpens,
      uniqueClicks: trackingStats.uniqueClicks,
    }
  } catch (error) {
    console.error('Error getting campaign analytics:', error)
    throw error
  }
}

/**
 * Get dashboard overview metrics
 */
export async function getDashboardMetrics(userId: string): Promise<DashboardMetrics> {
  try {
    // Get total campaigns
    const { data: campaigns, error: campaignsError } = await supabase
      .from('campaigns')
      .select('id, name, subject, status, sent_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (campaignsError) {
      throw campaignsError
    }

    const totalCampaigns = campaigns?.length || 0

    // Get total contacts
    const { data: contacts, error: contactsError } = await supabase
      .from('contacts')
      .select('id')
      .eq('user_id', userId)
      .eq('status', 'active')

    if (contactsError) {
      throw contactsError
    }

    const totalContacts = contacts?.length || 0

    // Get total sends
    const { data: sends, error: sendsError } = await supabase
      .from('sends')
      .select(`
        id,
        campaigns!inner(
          user_id
        )
      `)
      .eq('campaigns.user_id', userId)
      .eq('status', 'sent')

    if (sendsError) {
      throw sendsError
    }

    const totalSends = sends?.length || 0

    // Get total opens and clicks
    const sendIds = sends?.map(send => send.id) || []
    
    let totalOpens = 0
    let totalClicks = 0

    if (sendIds.length > 0) {
      const { data: events, error: eventsError } = await supabase
        .from('events')
        .select('type')
        .in('send_id', sendIds)
        .in('type', ['open', 'click'])

      if (eventsError) {
        throw eventsError
      }

      totalOpens = events?.filter(event => event.type === 'open').length || 0
      totalClicks = events?.filter(event => event.type === 'click').length || 0
    }

    // Calculate average rates
    const averageOpenRate = totalSends > 0 ? (totalOpens / totalSends) * 100 : 0
    const averageClickRate = totalSends > 0 ? (totalClicks / totalSends) * 100 : 0

    // Get recent campaigns with analytics
    const recentCampaigns: CampaignAnalytics[] = []
    const sentCampaigns = campaigns?.filter(c => c.status === 'sent').slice(0, 5) || []

    for (const campaign of sentCampaigns) {
      try {
        const analytics = await getCampaignAnalytics(campaign.id, userId)
        recentCampaigns.push(analytics)
      } catch (error) {
        console.error(`Error getting analytics for campaign ${campaign.id}:`, error)
      }
    }

    return {
      totalCampaigns,
      totalContacts,
      totalSends,
      totalOpens,
      totalClicks,
      averageOpenRate,
      averageClickRate,
      recentCampaigns,
    }
  } catch (error) {
    console.error('Error getting dashboard metrics:', error)
    throw error
  }
}

/**
 * Get time series data for analytics charts
 */
export async function getTimeSeriesData(
  userId: string,
  days: number = 30
): Promise<TimeSeriesData[]> {
  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // Get sends by date
    const { data: sends, error: sendsError } = await supabase
      .from('sends')
      .select(`
        sent_at,
        campaigns!inner(
          user_id
        )
      `)
      .eq('campaigns.user_id', userId)
      .eq('status', 'sent')
      .gte('sent_at', startDate.toISOString())
      .order('sent_at', { ascending: true })

    if (sendsError) {
      throw sendsError
    }

    const sendIds = sends?.map(send => send.id) || []

    // Get events by date
    let events: any[] = []
    if (sendIds.length > 0) {
      const { data: eventsData, error: eventsError } = await supabase
        .from('events')
        .select('type, created_at, send_id')
        .in('send_id', sendIds)
        .in('type', ['open', 'click'])
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true })

      if (eventsError) {
        throw eventsError
      }

      events = eventsData || []
    }

    // Group data by date
    const dataByDate: Record<string, { opens: number; clicks: number; sends: number }> = {}

    // Initialize all dates with zero values
    for (let i = 0; i < days; i++) {
      const date = new Date()
      date.setDate(date.getDate() - (days - 1 - i))
      const dateStr = date.toISOString().split('T')[0]
      dataByDate[dateStr] = { opens: 0, clicks: 0, sends: 0 }
    }

    // Count sends by date
    sends?.forEach(send => {
      if (send.sent_at) {
        const dateStr = send.sent_at.split('T')[0]
        if (dataByDate[dateStr]) {
          dataByDate[dateStr].sends++
        }
      }
    })

    // Count events by date
    events.forEach(event => {
      const dateStr = event.created_at.split('T')[0]
      if (dataByDate[dateStr]) {
        if (event.type === 'open') {
          dataByDate[dateStr].opens++
        } else if (event.type === 'click') {
          dataByDate[dateStr].clicks++
        }
      }
    })

    // Convert to array format
    return Object.entries(dataByDate).map(([date, data]) => ({
      date,
      opens: data.opens,
      clicks: data.clicks,
      sends: data.sends,
    }))
  } catch (error) {
    console.error('Error getting time series data:', error)
    throw error
  }
}

/**
 * Get top performing campaigns
 */
export async function getTopPerformingCampaigns(
  userId: string,
  limit: number = 10
): Promise<CampaignAnalytics[]> {
  try {
    // Get all sent campaigns
    const { data: campaigns, error: campaignsError } = await supabase
      .from('campaigns')
      .select('id, name, subject, status, sent_at')
      .eq('user_id', userId)
      .eq('status', 'sent')
      .order('sent_at', { ascending: false })

    if (campaignsError) {
      throw campaignsError
    }

    if (!campaigns || campaigns.length === 0) {
      return []
    }

    // Get analytics for each campaign
    const campaignAnalytics: CampaignAnalytics[] = []
    
    for (const campaign of campaigns) {
      try {
        const analytics = await getCampaignAnalytics(campaign.id, userId)
        campaignAnalytics.push(analytics)
      } catch (error) {
        console.error(`Error getting analytics for campaign ${campaign.id}:`, error)
      }
    }

    // Sort by open rate and return top performers
    return campaignAnalytics
      .filter(analytics => analytics.totalSends > 0)
      .sort((a, b) => b.openRate - a.openRate)
      .slice(0, limit)
  } catch (error) {
    console.error('Error getting top performing campaigns:', error)
    throw error
  }
}

/**
 * Get campaign comparison data
 */
export async function getCampaignComparison(
  campaignIds: string[],
  userId: string
): Promise<CampaignAnalytics[]> {
  try {
    const comparisons: CampaignAnalytics[] = []

    for (const campaignId of campaignIds) {
      try {
        const analytics = await getCampaignAnalytics(campaignId, userId)
        comparisons.push(analytics)
      } catch (error) {
        console.error(`Error getting analytics for campaign ${campaignId}:`, error)
      }
    }

    return comparisons
  } catch (error) {
    console.error('Error getting campaign comparison:', error)
    throw error
  }
}