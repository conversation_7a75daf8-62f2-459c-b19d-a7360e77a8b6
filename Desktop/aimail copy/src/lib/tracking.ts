import { supabase } from './supabase'

export interface TrackingEvent {
  id: string
  send_id: string
  type: 'open' | 'click' | 'bounce' | 'complaint'
  metadata?: Record<string, any>
  created_at: string
}

export interface TrackingPixelData {
  sendId: string
  baseUrl: string
}

export interface ClickTrackingData {
  sendId: string
  originalUrl: string
  baseUrl: string
}

/**
 * Generate a tracking pixel URL for email opens
 */
export function generateTrackingPixelUrl(baseUrl: string, sendId: string): string {
  return `${baseUrl}/api/track/open?send_id=${sendId}`
}

/**
 * Generate a tracking URL for link clicks
 */
export function generateClickTrackingUrl(
  baseUrl: string,
  sendId: string,
  originalUrl: string
): string {
  const encodedUrl = encodeURIComponent(originalUrl)
  return `${baseUrl}/api/track/click?send_id=${sendId}&url=${encodedUrl}`
}

/**
 * Add tracking pixel to HTML email content
 */
export function addTrackingPixel(htmlContent: string, pixelUrl: string): string {
  const trackingPixel = `<img src="${pixelUrl}" width="1" height="1" style="display:none;" alt="" />`
  
  // Insert tracking pixel before closing body tag, or at the end if no body tag
  if (htmlContent.includes('</body>')) {
    return htmlContent.replace('</body>', `${trackingPixel}</body>`)
  } else {
    return htmlContent + trackingPixel
  }
}

/**
 * Replace links in HTML content with tracking links
 */
export function addClickTracking(
  htmlContent: string,
  baseUrl: string,
  sendId: string
): string {
  const linkRegex = /<a\s+([^>]*?)href=["']([^"']+)["']([^>]*?)>/gi
  
  return htmlContent.replace(linkRegex, (match, beforeHref, url, afterHref) => {
    // Skip if already a tracking link or if it's a mailto link
    if (url.includes('/api/track/click') || url.startsWith('mailto:')) {
      return match
    }

    const trackingUrl = generateClickTrackingUrl(baseUrl, sendId, url)
    return `<a ${beforeHref}href="${trackingUrl}"${afterHref}>`
  })
}

/**
 * Generate complete tracked email HTML
 */
export function generateTrackedEmailHtml(
  htmlContent: string,
  baseUrl: string,
  sendId: string
): string {
  let trackedHtml = htmlContent

  // Add click tracking to links
  trackedHtml = addClickTracking(trackedHtml, baseUrl, sendId)

  // Add tracking pixel for opens
  const pixelUrl = generateTrackingPixelUrl(baseUrl, sendId)
  trackedHtml = addTrackingPixel(trackedHtml, pixelUrl)

  return trackedHtml
}

/**
 * Log a tracking event to the database
 */
export async function logTrackingEvent(
  sendId: string,
  eventType: 'open' | 'click' | 'bounce' | 'complaint',
  metadata?: Record<string, any>
): Promise<void> {
  try {
    const { error } = await supabase
      .from('events')
      .insert({
        send_id: sendId,
        type: eventType,
        metadata: metadata || null,
      })

    if (error) {
      console.error('Failed to log tracking event:', error)
      throw error
    }
  } catch (error) {
    console.error('Error logging tracking event:', error)
    throw error
  }
}

/**
 * Check if a send ID exists and is valid
 */
export async function validateSendId(sendId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('sends')
      .select('id')
      .eq('id', sendId)
      .single()

    if (error || !data) {
      return false
    }

    return true
  } catch (error) {
    console.error('Error validating send ID:', error)
    return false
  }
}

/**
 * Get tracking statistics for a campaign
 */
export async function getCampaignTrackingStats(campaignId: string) {
  try {
    // Get total sends for the campaign
    const { data: sends, error: sendsError } = await supabase
      .from('sends')
      .select('id')
      .eq('campaign_id', campaignId)
      .eq('status', 'sent')

    if (sendsError) {
      throw sendsError
    }

    const totalSends = sends?.length || 0

    if (totalSends === 0) {
      return {
        totalSends: 0,
        opens: 0,
        clicks: 0,
        openRate: 0,
        clickRate: 0,
        uniqueOpens: 0,
        uniqueClicks: 0,
      }
    }

    const sendIds = sends.map(send => send.id)

    // Get open events
    const { data: openEvents, error: openError } = await supabase
      .from('events')
      .select('send_id')
      .in('send_id', sendIds)
      .eq('type', 'open')

    if (openError) {
      throw openError
    }

    // Get click events
    const { data: clickEvents, error: clickError } = await supabase
      .from('events')
      .select('send_id, metadata')
      .in('send_id', sendIds)
      .eq('type', 'click')

    if (clickError) {
      throw clickError
    }

    const totalOpens = openEvents?.length || 0
    const totalClicks = clickEvents?.length || 0

    // Calculate unique opens and clicks
    const uniqueOpenSends = new Set(openEvents?.map(event => event.send_id) || [])
    const uniqueClickSends = new Set(clickEvents?.map(event => event.send_id) || [])

    const uniqueOpens = uniqueOpenSends.size
    const uniqueClicks = uniqueClickSends.size

    return {
      totalSends,
      opens: totalOpens,
      clicks: totalClicks,
      openRate: totalSends > 0 ? (uniqueOpens / totalSends) * 100 : 0,
      clickRate: totalSends > 0 ? (uniqueClicks / totalSends) * 100 : 0,
      uniqueOpens,
      uniqueClicks,
    }
  } catch (error) {
    console.error('Error getting campaign tracking stats:', error)
    throw error
  }
}

/**
 * Get detailed tracking events for a campaign
 */
export async function getCampaignTrackingEvents(campaignId: string) {
  try {
    const { data, error } = await supabase
      .from('events')
      .select(`
        id,
        type,
        metadata,
        created_at,
        sends!inner(
          id,
          campaign_id,
          contacts!inner(
            email,
            name
          )
        )
      `)
      .eq('sends.campaign_id', campaignId)
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    return data || []
  } catch (error) {
    console.error('Error getting campaign tracking events:', error)
    throw error
  }
}