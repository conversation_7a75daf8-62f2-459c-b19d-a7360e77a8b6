import { supabase, supabaseAdmin } from './supabase'
import type {
  User,
  Contact,
  Campaign,
  EmailSend,
  TrackingEvent,
  SuppressionEntry,
  CreateContactData,
  CreateCampaignData,
  CreateEmailSendData,
  CreateTrackingEventData,
  CreateSuppressionEntryData,
  UpdateContactData,
  UpdateCampaignData,
  UpdateEmailSendData,
  ContactFilters,
  CampaignFilters,
  EmailSendFilters,
  TrackingEventFilters,
  CampaignStats,
  DashboardStats,
  DatabaseResponse,
  DatabaseListResponse
} from '../types/database'

export class DatabaseService {
  // Contact operations
  async getContacts(userId: string, filters?: ContactFilters): Promise<DatabaseListResponse<Contact>> {
    try {
      let query = supabase
        .from('contacts')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      if (filters?.search) {
        query = query.or(`email.ilike.%${filters.search}%,name.ilike.%${filters.search}%`)
      }

      if (filters?.limit) {
        query = query.limit(filters.limit)
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data, error, count } = await query

      return {
        data: data || [],
        error: error as Error | null,
        count: count || 0
      }
    } catch (error) {
      return {
        data: [],
        error: error as Error,
        count: 0
      }
    }
  }

  async createContact(contactData: CreateContactData): Promise<DatabaseResponse<Contact>> {
    try {
      const { data, error } = await supabase
        .from('contacts')
        .insert(contactData)
        .select()
        .single()

      return {
        data: data as Contact | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async updateContact(contactData: UpdateContactData): Promise<DatabaseResponse<Contact>> {
    try {
      const { id, ...updateData } = contactData
      const { data, error } = await supabase
        .from('contacts')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      return {
        data: data as Contact | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async deleteContact(contactId: string): Promise<DatabaseResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('contacts')
        .delete()
        .eq('id', contactId)

      return {
        data: !error,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: false,
        error: error as Error
      }
    }
  }

  // Campaign operations
  async getCampaigns(userId: string, filters?: CampaignFilters): Promise<DatabaseListResponse<Campaign>> {
    try {
      let query = supabase
        .from('campaigns')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,subject.ilike.%${filters.search}%`)
      }

      if (filters?.limit) {
        query = query.limit(filters.limit)
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data, error, count } = await query

      return {
        data: data || [],
        error: error as Error | null,
        count: count || 0
      }
    } catch (error) {
      return {
        data: [],
        error: error as Error,
        count: 0
      }
    }
  }

  async createCampaign(campaignData: CreateCampaignData): Promise<DatabaseResponse<Campaign>> {
    try {
      const { data, error } = await supabase
        .from('campaigns')
        .insert(campaignData)
        .select()
        .single()

      return {
        data: data as Campaign | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async updateCampaign(campaignData: UpdateCampaignData): Promise<DatabaseResponse<Campaign>> {
    try {
      const { id, ...updateData } = campaignData
      const { data, error } = await supabase
        .from('campaigns')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      return {
        data: data as Campaign | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async getCampaignById(campaignId: string): Promise<DatabaseResponse<Campaign>> {
    try {
      const { data, error } = await supabase
        .from('campaigns')
        .select('*')
        .eq('id', campaignId)
        .single()

      return {
        data: data as Campaign | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async deleteCampaign(campaignId: string): Promise<DatabaseResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('campaigns')
        .delete()
        .eq('id', campaignId)

      return {
        data: !error,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: false,
        error: error as Error
      }
    }
  }

  // Email send operations
  async logEmailSend(sendData: CreateEmailSendData): Promise<DatabaseResponse<EmailSend>> {
    try {
      const { data, error } = await supabase
        .from('sends')
        .insert(sendData)
        .select()
        .single()

      return {
        data: data as EmailSend | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async updateEmailSend(sendData: UpdateEmailSendData): Promise<DatabaseResponse<EmailSend>> {
    try {
      const { id, ...updateData } = sendData
      const { data, error } = await supabase
        .from('sends')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()

      return {
        data: data as EmailSend | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async getEmailSends(filters?: EmailSendFilters): Promise<DatabaseListResponse<EmailSend>> {
    try {
      let query = supabase
        .from('sends')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })

      if (filters?.campaign_id) {
        query = query.eq('campaign_id', filters.campaign_id)
      }

      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      if (filters?.limit) {
        query = query.limit(filters.limit)
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data, error, count } = await query

      return {
        data: data || [],
        error: error as Error | null,
        count: count || 0
      }
    } catch (error) {
      return {
        data: [],
        error: error as Error,
        count: 0
      }
    }
  }

  // Tracking event operations
  async trackEvent(eventData: CreateTrackingEventData): Promise<DatabaseResponse<TrackingEvent>> {
    try {
      const { data, error } = await supabase
        .from('events')
        .insert(eventData)
        .select()
        .single()

      return {
        data: data as TrackingEvent | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async getTrackingEvents(filters?: TrackingEventFilters): Promise<DatabaseListResponse<TrackingEvent>> {
    try {
      let query = supabase
        .from('events')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })

      if (filters?.send_id) {
        query = query.eq('send_id', filters.send_id)
      }

      if (filters?.type) {
        query = query.eq('type', filters.type)
      }

      if (filters?.limit) {
        query = query.limit(filters.limit)
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
      }

      const { data, error, count } = await query

      return {
        data: data || [],
        error: error as Error | null,
        count: count || 0
      }
    } catch (error) {
      return {
        data: [],
        error: error as Error,
        count: 0
      }
    }
  }

  // Suppression list operations
  async updateSuppressionList(userId: string, email: string, reason: 'bounce' | 'complaint' | 'unsubscribe'): Promise<DatabaseResponse<SuppressionEntry>> {
    try {
      const suppressionData: CreateSuppressionEntryData = {
        user_id: userId,
        email,
        reason
      }

      const { data, error } = await supabase
        .from('suppression')
        .upsert(suppressionData, { onConflict: 'user_id,email' })
        .select()
        .single()

      return {
        data: data as SuppressionEntry | null,
        error: error as Error | null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async isEmailSuppressed(userId: string, email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('suppression')
        .select('id')
        .eq('user_id', userId)
        .eq('email', email)
        .single()

      return !!data && !error
    } catch (error) {
      return false
    }
  }

  // Statistics and analytics
  async getCampaignStats(campaignId: string): Promise<DatabaseResponse<CampaignStats>> {
    try {
      // Get basic send statistics
      const { data: sendStats, error: sendError } = await supabase
        .from('sends')
        .select('status')
        .eq('campaign_id', campaignId)

      if (sendError) {
        return { data: null, error: sendError as Error }
      }

      // Get event statistics
      const { data: eventStats, error: eventError } = await supabase
        .from('events')
        .select('type, send_id')
        .in('send_id', sendStats?.map(s => s.id) || [])

      if (eventError) {
        return { data: null, error: eventError as Error }
      }

      // Calculate statistics
      const totalSends = sendStats?.length || 0
      const successfulSends = sendStats?.filter(s => s.status === 'sent').length || 0
      const failedSends = sendStats?.filter(s => s.status === 'failed').length || 0
      const bouncedSends = sendStats?.filter(s => s.status === 'bounced').length || 0
      
      const opens = eventStats?.filter(e => e.type === 'open').length || 0
      const clicks = eventStats?.filter(e => e.type === 'click').length || 0
      
      const openRate = successfulSends > 0 ? (opens / successfulSends) * 100 : 0
      const clickRate = successfulSends > 0 ? (clicks / successfulSends) * 100 : 0
      const bounceRate = totalSends > 0 ? (bouncedSends / totalSends) * 100 : 0

      const stats: CampaignStats = {
        campaign_id: campaignId,
        total_sends: totalSends,
        successful_sends: successfulSends,
        failed_sends: failedSends,
        bounced_sends: bouncedSends,
        opens,
        clicks,
        open_rate: Math.round(openRate * 100) / 100,
        click_rate: Math.round(clickRate * 100) / 100,
        bounce_rate: Math.round(bounceRate * 100) / 100
      }

      return {
        data: stats,
        error: null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }

  async getDashboardStats(userId: string): Promise<DatabaseResponse<DashboardStats>> {
    try {
      // Get contact statistics
      const { data: contacts, error: contactError } = await supabase
        .from('contacts')
        .select('status')
        .eq('user_id', userId)

      if (contactError) {
        return { data: null, error: contactError as Error }
      }

      // Get campaign statistics
      const { data: campaigns, error: campaignError } = await supabase
        .from('campaigns')
        .select('status')
        .eq('user_id', userId)

      if (campaignError) {
        return { data: null, error: campaignError as Error }
      }

      // Get send and event statistics
      const { data: sends, error: sendError } = await supabase
        .from('sends')
        .select('id, status, campaign_id')
        .in('campaign_id', campaigns?.map(c => c.id) || [])

      if (sendError) {
        return { data: null, error: sendError as Error }
      }

      const { data: events, error: eventError } = await supabase
        .from('events')
        .select('type')
        .in('send_id', sends?.map(s => s.id) || [])

      if (eventError) {
        return { data: null, error: eventError as Error }
      }

      // Calculate statistics
      const totalContacts = contacts?.length || 0
      const activeContacts = contacts?.filter(c => c.status === 'active').length || 0
      const totalCampaigns = campaigns?.length || 0
      const sentCampaigns = campaigns?.filter(c => c.status === 'sent').length || 0
      const totalSends = sends?.length || 0
      const totalOpens = events?.filter(e => e.type === 'open').length || 0
      const totalClicks = events?.filter(e => e.type === 'click').length || 0
      
      const overallOpenRate = totalSends > 0 ? (totalOpens / totalSends) * 100 : 0
      const overallClickRate = totalSends > 0 ? (totalClicks / totalSends) * 100 : 0

      const stats: DashboardStats = {
        total_contacts: totalContacts,
        active_contacts: activeContacts,
        total_campaigns: totalCampaigns,
        sent_campaigns: sentCampaigns,
        total_sends: totalSends,
        total_opens: totalOpens,
        total_clicks: totalClicks,
        overall_open_rate: Math.round(overallOpenRate * 100) / 100,
        overall_click_rate: Math.round(overallClickRate * 100) / 100
      }

      return {
        data: stats,
        error: null
      }
    } catch (error) {
      return {
        data: null,
        error: error as Error
      }
    }
  }
}

// Export singleton instance
export const db = new DatabaseService()