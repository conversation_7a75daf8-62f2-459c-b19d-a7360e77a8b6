import { z } from 'zod';

/**
 * Input sanitization and XSS protection utilities
 */

// Basic HTML sanitization that works on both server and client
function basicHtmlSanitize(html: string): string {
  return html
    // Remove dangerous tags
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, '')
    .replace(/<input\b[^>]*>/gi, '')
    .replace(/<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi, '')
    .replace(/<select\b[^<]*(?:(?!<\/select>)<[^<]*)*<\/select>/gi, '')
    // Remove dangerous protocols and event handlers
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '')
    .replace(/data:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    // Remove style attributes that could contain expressions
    .replace(/style\s*=\s*["'][^"']*expression\s*\([^"']*["']/gi, '')
    .replace(/style\s*=\s*["'][^"']*javascript\s*:[^"']*["']/gi, '');
}

// HTML sanitization for rich text content
export function sanitizeHtml(html: string): string {
  // For now, use basic sanitization on both server and client
  // This avoids the isomorphic-dompurify server-side issues
  return basicHtmlSanitize(html);
}

// Text sanitization for plain text fields
export function sanitizeText(text: string): string {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Email sanitization
export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
}

// URL sanitization
export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }
    return parsed.toString();
  } catch {
    return '';
  }
}

// SQL injection prevention helpers
export function escapeString(str: string): string {
  return str.replace(/'/g, "''").replace(/\\/g, '\\\\');
}

// Validation schemas with sanitization
export const sanitizedEmailSchema = z.string()
  .transform(sanitizeEmail)
  .refine((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email), {
    message: "Invalid email address"
  });

export const sanitizedTextSchema = z.string()
  .max(1000)
  .transform(sanitizeText);

export const sanitizedHtmlSchema = z.string()
  .max(50000)
  .transform(sanitizeHtml);

export const sanitizedUrlSchema = z.string()
  .transform(sanitizeUrl)
  .refine((url) => url !== '', {
    message: "Invalid URL"
  });

// Content Security Policy headers
export const cspHeader = [
  "default-src 'self'",
  "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
  "style-src 'self' 'unsafe-inline'",
  "img-src 'self' data: https:",
  "font-src 'self' data:",
  "connect-src 'self' https://api.perplexity.ai https://*.supabase.co",
  "frame-ancestors 'none'",
  "base-uri 'self'",
  "form-action 'self'"
].join('; ');

// Security headers configuration
export const securityHeaders = {
  'Content-Security-Policy': cspHeader,
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  'X-XSS-Protection': '1; mode=block'
};