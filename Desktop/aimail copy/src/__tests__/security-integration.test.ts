/**
 * Security Integration Tests
 * Tests security measures across the application
 */

import { NextRequest } from 'next/server'
import { middleware } from '../middleware'

// Mock the security module
jest.mock('../lib/security', () => ({
  securityHeaders: {
    'Content-Security-Policy': "default-src 'self'",
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff'
  }
}))

// Mock Supabase
jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      getSession: jest.fn(() => ({
        data: { session: null },
        error: null
      }))
    }
  }))
}))

describe('Security Integration Tests', () => {
  describe('Middleware Security', () => {
    it('should add security headers to all responses', async () => {
      const request = new NextRequest('http://localhost:3000/dashboard')
      const response = await middleware(request)

      expect(response.headers.get('Content-Security-Policy')).toBe("default-src 'self'")
      expect(response.headers.get('X-Frame-Options')).toBe('DENY')
      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff')
    })

    it('should generate CSRF token for new sessions', async () => {
      const request = new NextRequest('http://localhost:3000/dashboard')
      const response = await middleware(request)

      const csrfCookie = response.cookies.get('csrf-token')
      expect(csrfCookie).toBeDefined()
      expect(csrfCookie?.httpOnly).toBe(true)
      expect(csrfCookie?.sameSite).toBe('strict')
    })

    it('should validate CSRF tokens for POST requests', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await middleware(request)
      expect(response.status).toBe(403)
    })

    it('should allow requests with valid CSRF tokens', async () => {
      const request = new NextRequest('http://localhost:3000/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      const response = await middleware(request)
      expect(response.status).not.toBe(403)
    })

    it('should implement rate limiting', async () => {
      const requests = []
      
      // Make multiple requests to trigger rate limiting
      for (let i = 0; i < 10; i++) {
        const request = new NextRequest('http://localhost:3000/api/auth/login', {
          method: 'POST',
          headers: {
            'X-Forwarded-For': '***********'
          }
        })
        requests.push(middleware(request))
      }

      const responses = await Promise.all(requests)
      
      // Should have at least one rate limited response
      const rateLimited = responses.some(response => response.status === 429)
      expect(rateLimited).toBe(true)
    })
  })

  describe('Input Validation', () => {
    it('should reject malicious HTML in campaign creation', async () => {
      const maliciousHtml = '<script>alert("xss")</script><p>Content</p>'
      
      // This would be tested in the actual API route
      // Here we're testing the sanitization function
      const { sanitizeHtml } = require('../lib/security')
      const sanitized = sanitizeHtml(maliciousHtml)
      
      expect(sanitized).not.toContain('<script>')
      expect(sanitized).toContain('<p>Content</p>')
    })

    it('should sanitize text inputs', async () => {
      const maliciousText = 'Hello <script>alert("xss")</script> world'
      
      const { sanitizeText } = require('../lib/security')
      const sanitized = sanitizeText(maliciousText)
      
      expect(sanitized).not.toContain('<script>')
      expect(sanitized).toBe('Hello scriptalert("xss")/script world')
    })

    it('should validate and sanitize URLs', async () => {
      const maliciousUrl = 'javascript:alert("xss")'
      
      const { sanitizeUrl } = require('../lib/security')
      const sanitized = sanitizeUrl(maliciousUrl)
      
      expect(sanitized).toBe('')
    })
  })

  describe('Authentication Security', () => {
    it('should protect sensitive routes', async () => {
      const protectedRoutes = [
        '/dashboard',
        '/campaigns',
        '/contacts',
        '/settings'
      ]

      for (const route of protectedRoutes) {
        const request = new NextRequest(`http://localhost:3000${route}`)
        const response = await middleware(request)
        
        // Should redirect to login
        expect(response.status).toBe(307)
        expect(response.headers.get('location')).toContain('/login')
      }
    })

    it('should allow access to public routes', async () => {
      const publicRoutes = [
        '/login',
        '/api/health'
      ]

      for (const route of publicRoutes) {
        const request = new NextRequest(`http://localhost:3000${route}`)
        const response = await middleware(request)
        
        // Should not redirect
        expect(response.status).not.toBe(307)
      }
    })
  })

  describe('Data Protection', () => {
    it('should prevent SQL injection in search queries', () => {
      const maliciousQuery = "'; DROP TABLE users; --"
      
      const { escapeString } = require('../lib/security')
      const escaped = escapeString(maliciousQuery)
      
      expect(escaped).toBe("''; DROP TABLE users; --")
    })

    it('should validate email addresses', () => {
      const { sanitizedEmailSchema } = require('../lib/security')
      
      expect(() => sanitizedEmailSchema.parse('<EMAIL>')).not.toThrow()
      expect(() => sanitizedEmailSchema.parse('invalid-email')).toThrow()
      expect(() => sanitizedEmailSchema.parse('<script>@example.com')).toThrow()
    })
  })

  describe('Error Handling Security', () => {
    it('should not expose sensitive information in error messages', () => {
      // Test that error messages don't contain sensitive data
      const sensitiveError = new Error('Database connection failed: password=secret123')
      
      // In a real application, you would have error sanitization
      const sanitizedMessage = 'Database connection failed'
      
      expect(sanitizedMessage).not.toContain('password')
      expect(sanitizedMessage).not.toContain('secret123')
    })
  })
})