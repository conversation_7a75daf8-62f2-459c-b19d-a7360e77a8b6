# Deployment Guide

This guide covers deploying the Email Marketing Platform to various hosting providers.

## Quick Deploy Options

### Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/email-marketing-platform)

### Netlify

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/your-username/email-marketing-platform)

## Manual Deployment

### Prerequisites

Before deploying, ensure you have:

1. **Supabase Project** - Set up and configured
2. **AWS SES** - Configured with verified domain/email
3. **Environment Variables** - All required variables ready
4. **Domain** (optional) - For custom domain setup

### Step 1: Prepare Your Repository

1. **Fork or Clone** the repository
2. **Configure Environment Variables** using `.env.example` as a template
3. **Test Locally** to ensure everything works
4. **Commit Changes** and push to your Git repository

### Step 2: Deploy to Vercel

#### Option A: Vercel CLI

```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Set environment variables
vercel env add NEXT_PUBLIC_SUPABASE_URL
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
vercel env add SUPABASE_SERVICE_ROLE_KEY
vercel env add AWS_ACCESS_KEY_ID
vercel env add AWS_SECRET_ACCESS_KEY
vercel env add AWS_REGION
vercel env add SES_FROM_EMAIL
vercel env add PERPLEXITY_API_KEY
vercel env add NEXTAUTH_SECRET
vercel env add NEXTAUTH_URL

# Redeploy with environment variables
vercel --prod
```

#### Option B: Vercel Dashboard

1. Go to [vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your Git repository
4. Configure environment variables in the dashboard
5. Deploy

### Step 3: Deploy to Netlify

#### Option A: Netlify CLI

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Initialize site
netlify init

# Set environment variables
netlify env:set NEXT_PUBLIC_SUPABASE_URL "your-value"
netlify env:set NEXT_PUBLIC_SUPABASE_ANON_KEY "your-value"
# ... set all other environment variables

# Deploy
netlify deploy --prod
```

#### Option B: Netlify Dashboard

1. Go to [netlify.com](https://netlify.com) and sign in
2. Click "New site from Git"
3. Connect your Git repository
4. Set build settings:
   - Build command: `npm run build`
   - Publish directory: `.next`
5. Add environment variables in Site Settings
6. Deploy

### Step 4: Deploy to Other Platforms

#### Railway

1. Go to [railway.app](https://railway.app)
2. Click "New Project" → "Deploy from GitHub repo"
3. Select your repository
4. Add environment variables
5. Deploy

#### Render

1. Go to [render.com](https://render.com)
2. Click "New" → "Web Service"
3. Connect your repository
4. Configure:
   - Build Command: `npm run build`
   - Start Command: `npm start`
5. Add environment variables
6. Deploy

#### DigitalOcean App Platform

1. Go to DigitalOcean Control Panel
2. Click "Create" → "Apps"
3. Connect your repository
4. Configure build settings
5. Add environment variables
6. Deploy

## Environment Variables Configuration

### Required Variables

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# AWS SES
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_REGION=us-east-1
SES_FROM_EMAIL=<EMAIL>

# Authentication
NEXTAUTH_SECRET=your-random-secret-key
NEXTAUTH_URL=https://yourdomain.com

# Optional
PERPLEXITY_API_KEY=your-perplexity-key
```

### Environment-Specific Variables

#### Production
```bash
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
```

#### Staging
```bash
NODE_ENV=staging
NEXTAUTH_URL=https://staging.yourdomain.com
```

## Post-Deployment Setup

### 1. Verify Deployment

Run the deployment verification script:

```bash
node scripts/deployment-verification.js https://your-deployed-app.com
```

### 2. Test Health Endpoint

Visit `https://your-deployed-app.com/api/health` to check system status.

### 3. Create Demo Data

```bash
# Get your user ID after logging in
node scripts/create-demo-data.js your-user-id
```

### 4. Configure Domain (Optional)

#### Vercel Custom Domain

1. Go to your project dashboard
2. Click "Domains"
3. Add your custom domain
4. Update DNS records as instructed

#### Netlify Custom Domain

1. Go to Site Settings → Domain management
2. Add custom domain
3. Configure DNS records

### 5. Set Up Monitoring

#### Vercel Analytics

1. Enable Vercel Analytics in your project dashboard
2. Monitor performance and usage

#### Custom Monitoring

Set up monitoring for:
- Application uptime
- Database connectivity
- Email sending rates
- Error rates

## SSL/TLS Configuration

Most platforms (Vercel, Netlify) provide automatic SSL certificates. For custom setups:

1. **Let's Encrypt** - Free SSL certificates
2. **Cloudflare** - SSL proxy and CDN
3. **AWS Certificate Manager** - For AWS deployments

## Performance Optimization

### 1. Enable Caching

Configure appropriate cache headers:

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/health',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=60'
          }
        ]
      }
    ]
  }
}
```

### 2. Database Optimization

- Enable connection pooling in Supabase
- Add database indexes for frequently queried columns
- Monitor query performance

### 3. CDN Configuration

- Use Vercel's Edge Network (automatic)
- Or configure Cloudflare for custom domains

## Security Considerations

### 1. Environment Variables

- Never commit secrets to Git
- Use platform-specific secret management
- Rotate keys regularly

### 2. CORS Configuration

Configure CORS for your domain:

```javascript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  
  response.headers.set('Access-Control-Allow-Origin', 'https://yourdomain.com')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  
  return response
}
```

### 3. Rate Limiting

Implement rate limiting for API endpoints:

```javascript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

export const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '10 s'),
})
```

## Troubleshooting

### Common Deployment Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Review build logs for specific errors

2. **Environment Variable Issues**
   - Ensure all required variables are set
   - Check for typos in variable names
   - Verify variable values are correct

3. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check network connectivity
   - Review RLS policies

4. **Email Sending Issues**
   - Verify AWS SES configuration
   - Check SES sending limits
   - Ensure domain is verified

### Debugging Steps

1. **Check Health Endpoint**
   ```bash
   curl https://your-app.com/api/health
   ```

2. **Review Application Logs**
   - Vercel: Check Function Logs in dashboard
   - Netlify: Check Function Logs in dashboard
   - Other platforms: Check platform-specific logs

3. **Test Database Connection**
   ```bash
   # Use Supabase CLI
   supabase db ping
   ```

4. **Verify Environment Variables**
   ```bash
   # Check if variables are set (don't log sensitive values)
   node -e "console.log(Object.keys(process.env).filter(k => k.includes('SUPABASE')))"
   ```

## Rollback Procedures

### Vercel Rollback

```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### Netlify Rollback

1. Go to Site Dashboard
2. Click "Deploys"
3. Find previous successful deploy
4. Click "Publish deploy"

### Manual Rollback

1. Revert Git commits
2. Redeploy from previous commit
3. Restore database if needed

## Monitoring and Maintenance

### 1. Set Up Alerts

Configure alerts for:
- Application downtime
- High error rates
- Database connection issues
- Email sending failures

### 2. Regular Maintenance

- Update dependencies monthly
- Review and rotate API keys quarterly
- Monitor database performance
- Review application logs weekly

### 3. Backup Strategy

- Database: Supabase automatic backups
- Code: Git repository
- Configuration: Document all settings

## Cost Optimization

### 1. Platform Costs

- **Vercel**: Free tier available, pay for usage
- **Netlify**: Free tier available, pay for bandwidth
- **Railway**: Usage-based pricing
- **Render**: Fixed pricing tiers

### 2. Database Costs

- **Supabase**: Free tier up to 500MB, then usage-based
- Monitor database size and optimize queries

### 3. Email Costs

- **Amazon SES**: $0.10 per 1,000 emails
- Monitor sending volume and clean lists regularly

## Support and Resources

- **Platform Documentation**: Check your hosting provider's docs
- **Community Forums**: Platform-specific communities
- **Application Issues**: Check GitHub issues
- **Email Deliverability**: AWS SES documentation