# Amazon SES Setup Guide

This guide covers setting up Amazon SES (Simple Email Service) for reliable email delivery.

## Prerequisites

- AWS account
- Domain name (recommended) or verified email address
- Access to your domain's DNS settings

## Step 1: AWS Account Setup

### 1. Create IAM User

1. Go to AWS IAM console
2. Click "Users" → "Add user"
3. Choose a username (e.g., `email-marketing-ses`)
4. Select "Programmatic access"
5. Click "Next: Permissions"

### 2. Attach SES Permissions

Create a custom policy with these permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ses:SendEmail",
                "ses:SendRawEmail",
                "ses:GetSendQuota",
                "ses:GetSendStatistics",
                "ses:ListVerifiedEmailAddresses",
                "ses:GetIdentityVerificationAttributes",
                "ses:GetIdentityDkimAttributes",
                "ses:GetIdentityNotificationAttributes"
            ],
            "Resource": "*"
        }
    ]
}
```

### 3. Generate Access Keys

1. Complete user creation
2. Save the Access Key ID and Secret Access Key
3. Add these to your environment variables:

```bash
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=us-east-1  # or your preferred region
```

## Step 2: SES Configuration

### 1. Choose Your Region

SES is available in several regions. Choose based on:
- **us-east-1** (N. Virginia) - Most features, lowest latency to US
- **us-west-2** (Oregon) - Good for US West Coast
- **eu-west-1** (Ireland) - Good for Europe

### 2. Verify Your Domain (Recommended)

1. Go to SES console in your chosen region
2. Click "Verified identities" → "Create identity"
3. Choose "Domain"
4. Enter your domain (e.g., `yourdomain.com`)
5. Enable DKIM signing
6. Click "Create identity"

### 3. Add DNS Records

You'll need to add several DNS records:

#### Domain Verification Record
```
Type: TXT
Name: _amazonses.yourdomain.com
Value: [provided by AWS]
```

#### DKIM Records (3 records)
```
Type: CNAME
Name: [random-string]._domainkey.yourdomain.com
Value: [random-string].dkim.amazonses.com
```

#### SPF Record
```
Type: TXT
Name: yourdomain.com
Value: v=spf1 include:amazonses.com ~all
```

#### DMARC Record (Optional but recommended)
```
Type: TXT
Name: _dmarc.yourdomain.com
Value: v=DMARC1; p=quarantine; rua=mailto:<EMAIL>
```

### 4. Verify Email Address (Alternative)

If you don't have a domain, you can verify individual email addresses:

1. Go to "Verified identities" → "Create identity"
2. Choose "Email address"
3. Enter your email address
4. Check your email and click the verification link

## Step 3: Request Production Access

By default, SES starts in "Sandbox mode" with limitations:
- Can only send to verified email addresses
- Limited to 200 emails per day
- Maximum 1 email per second

### Request Production Access

1. Go to SES console → "Account dashboard"
2. Click "Request production access"
3. Fill out the form:
   - **Mail type**: Transactional
   - **Website URL**: Your application URL
   - **Use case description**: Describe your email marketing platform
   - **Additional contact addresses**: Your support email
   - **Preferred contact language**: English
4. Submit the request

**Note**: AWS typically responds within 24-48 hours.

## Step 4: Configure Bounce and Complaint Handling

### 1. Create SNS Topics

```bash
# Create bounce topic
aws sns create-topic --name ses-bounces --region us-east-1

# Create complaint topic  
aws sns create-topic --name ses-complaints --region us-east-1
```

### 2. Set Up SES Notifications

1. Go to SES console → "Verified identities"
2. Select your domain/email
3. Go to "Notifications" tab
4. Configure:
   - **Bounces**: Select your bounce SNS topic
   - **Complaints**: Select your complaint SNS topic
   - **Deliveries**: Optional

### 3. Subscribe to SNS Topics

```bash
# Subscribe webhook endpoint to bounce topic
aws sns subscribe \
  --topic-arn arn:aws:sns:us-east-1:ACCOUNT:ses-bounces \
  --protocol https \
  --notification-endpoint https://yourdomain.com/api/webhooks/ses

# Subscribe webhook endpoint to complaint topic
aws sns subscribe \
  --topic-arn arn:aws:sns:us-east-1:ACCOUNT:ses-complaints \
  --protocol https \
  --notification-endpoint https://yourdomain.com/api/webhooks/ses
```

## Step 5: Environment Configuration

Add these environment variables to your `.env.local`:

```bash
# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=us-east-1
SES_FROM_EMAIL=<EMAIL>

# Optional: Custom configuration
SES_CONFIGURATION_SET=your_configuration_set
SES_REPLY_TO_EMAIL=<EMAIL>
```

## Step 6: Testing Your Setup

### 1. Test Email Sending

Use the AWS CLI to test:

```bash
aws ses send-email \
  --source <EMAIL> \
  --destination ToAddresses=<EMAIL> \
  --message Subject={Data="Test Email",Charset=utf8},Body={Text={Data="This is a test email",Charset=utf8}}
```

### 2. Test in Your Application

1. Start your development server
2. Create a test campaign
3. Send to a verified email address
4. Check delivery and tracking

## Monitoring and Maintenance

### 1. Monitor Sending Statistics

```bash
# Get sending quota
aws ses get-send-quota

# Get sending statistics
aws ses get-send-statistics
```

### 2. Monitor Bounce and Complaint Rates

- Keep bounce rate below 5%
- Keep complaint rate below 0.1%
- Monitor these in SES console

### 3. Reputation Management

- Maintain good sending practices
- Remove bounced emails from your lists
- Honor unsubscribe requests immediately
- Send relevant, expected content

## Troubleshooting

### Common Issues

1. **Emails Going to Spam**
   - Verify SPF, DKIM, and DMARC records
   - Warm up your domain gradually
   - Improve email content and engagement

2. **High Bounce Rate**
   - Validate email addresses before sending
   - Remove invalid addresses from lists
   - Use double opt-in for subscriptions

3. **Account Suspension**
   - Monitor bounce and complaint rates
   - Respond quickly to AWS notifications
   - Follow email best practices

### Getting Help

- AWS SES Documentation: [docs.aws.amazon.com/ses](https://docs.aws.amazon.com/ses/)
- AWS Support (if you have a support plan)
- Email deliverability best practices guides

## Cost Optimization

### Pricing (as of 2024)

- **First 62,000 emails per month**: Free (if sent from EC2)
- **Additional emails**: $0.10 per 1,000 emails
- **Attachments**: $0.12 per GB

### Tips to Reduce Costs

1. Clean your email lists regularly
2. Use text versions to reduce size
3. Optimize image sizes in HTML emails
4. Monitor usage in AWS billing dashboard

## Security Best Practices

1. **Rotate Access Keys Regularly**
   - Set up key rotation schedule
   - Use AWS Secrets Manager for production

2. **Limit IAM Permissions**
   - Use least privilege principle
   - Create separate users for different environments

3. **Monitor Access Logs**
   - Enable CloudTrail for SES API calls
   - Set up alerts for unusual activity

4. **Secure Webhook Endpoints**
   - Validate SNS message signatures
   - Use HTTPS for all webhook URLs
   - Implement rate limiting