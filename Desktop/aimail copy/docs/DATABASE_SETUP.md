# Database Setup Guide

This guide covers setting up the Supabase database for the Email Marketing Platform.

## Prerequisites

- Supabase account
- Supabase CLI installed (`npm install -g supabase`)
- Node.js 18+

## Quick Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Choose a project name and database password
3. Wait for the project to be created (usually takes 2-3 minutes)

### 2. Get Project Credentials

1. Go to Settings > API in your Supabase dashboard
2. Copy the following values:
   - Project URL
   - Anon/Public key
   - Service role key (keep this secret)

### 3. Link Local Project

```bash
# Initialize Supabase in your project (if not already done)
supabase init

# Link to your remote project
supabase link --project-ref YOUR_PROJECT_REF
```

Your project ref can be found in your Supabase project URL: `https://YOUR_PROJECT_REF.supabase.co`

### 4. Run Migrations

```bash
# Push migrations to your remote database
supabase db push

# Or apply migrations manually
supabase db reset
```

### 5. Seed Database (Optional)

```bash
# Run seed script to populate with sample data
supabase db seed
```

## Manual Database Setup

If you prefer to set up the database manually, you can run the SQL scripts directly in the Supabase SQL editor.

### 1. Run Initial Schema

Copy and paste the contents of `supabase/migrations/001_initial_schema.sql` into the Supabase SQL editor and execute.

### 2. Set Up Row Level Security

Copy and paste the contents of `supabase/migrations/002_rls_policies.sql` into the Supabase SQL editor and execute.

### 3. Seed Data (Optional)

Copy and paste the contents of `supabase/seed.sql` into the Supabase SQL editor and execute.

## Database Schema Overview

### Tables

1. **users** - User accounts (handled by Supabase Auth)
2. **contacts** - Email contacts/subscribers
3. **campaigns** - Email campaigns
4. **sends** - Individual email sends
5. **events** - Tracking events (opens, clicks, etc.)
6. **suppression** - Suppressed email addresses

### Relationships

```
users (1) -> (many) contacts
users (1) -> (many) campaigns
campaigns (1) -> (many) sends
contacts (1) -> (many) sends
sends (1) -> (many) events
users (1) -> (many) suppression
```

## Row Level Security (RLS)

All tables have RLS enabled to ensure data isolation between users:

- Users can only access their own data
- All operations are filtered by `user_id`
- Service role key bypasses RLS for admin operations

## Environment Variables

After setting up your database, add these to your `.env.local`:

```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## Backup and Recovery

### Create Backup

```bash
# Create a backup of your database
supabase db dump --file backup.sql
```

### Restore from Backup

```bash
# Restore from backup
supabase db reset --file backup.sql
```

## Monitoring

### View Logs

```bash
# View database logs
supabase logs db
```

### Performance Monitoring

1. Go to your Supabase dashboard
2. Navigate to Reports
3. Monitor query performance and usage

## Troubleshooting

### Common Issues

1. **Migration Fails**
   - Check if you have the correct permissions
   - Ensure you're linked to the correct project
   - Try running `supabase db reset` to start fresh

2. **RLS Blocking Queries**
   - Verify user authentication is working
   - Check if RLS policies are correctly configured
   - Use service role key for admin operations

3. **Connection Issues**
   - Verify your project URL and keys
   - Check if your project is paused (free tier limitation)
   - Ensure network connectivity

### Getting Help

- Check Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
- Join Supabase Discord: [discord.supabase.com](https://discord.supabase.com)
- Review database logs in Supabase dashboard

## Production Considerations

### Performance

- Add indexes for frequently queried columns
- Monitor query performance in Supabase dashboard
- Consider upgrading to Pro plan for better performance

### Security

- Regularly rotate service role keys
- Monitor access logs
- Keep RLS policies up to date
- Use connection pooling for high-traffic applications

### Scaling

- Monitor database usage and upgrade plan as needed
- Consider read replicas for read-heavy workloads
- Implement proper caching strategies