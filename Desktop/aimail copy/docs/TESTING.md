# Testing Documentation

This document provides comprehensive information about the testing strategy, procedures, and maintenance for the Email Marketing Platform.

## Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Test Types](#test-types)
3. [Running Tests](#running-tests)
4. [Test Environment Setup](#test-environment-setup)
5. [Writing Tests](#writing-tests)
6. [CI/CD Integration](#cicd-integration)
7. [Performance Testing](#performance-testing)
8. [Accessibility Testing](#accessibility-testing)
9. [Load Testing](#load-testing)
10. [Maintenance](#maintenance)

## Testing Strategy

Our testing strategy follows a comprehensive approach with multiple layers:

- **Unit Tests**: Test individual components and functions in isolation
- **Integration Tests**: Test interactions between components and services
- **End-to-End Tests**: Test complete user workflows
- **Performance Tests**: Test system performance under various loads
- **Accessibility Tests**: Ensure WCAG 2.1 AA compliance
- **Load Tests**: Test system behavior under high traffic

## Test Types

### Unit Tests

Located in `src/**/__tests__/` directories alongside source code.

**Technologies:**
- Jest for test runner
- React Testing Library for component testing
- Mock Service Worker (MSW) for API mocking

**Coverage Requirements:**
- Minimum 80% code coverage
- 100% coverage for critical business logic
- All edge cases and error conditions

### Integration Tests

Test interactions between different parts of the system.

**Examples:**
- API route testing with database operations
- Component integration with external services
- Authentication flow testing

### End-to-End Tests

Located in `tests/e2e/` directory.

**Technologies:**
- Playwright for browser automation
- Multiple browser testing (Chrome, Firefox, Safari)
- Mobile viewport testing

**Test Scenarios:**
- Complete user authentication flow
- Contact management workflow
- Campaign creation and sending
- Analytics and reporting
- Settings configuration

### Performance Tests

Located in `tests/performance/` directory.

**Focus Areas:**
- Bulk email processing performance
- Database query optimization
- Memory usage monitoring
- Response time benchmarks

### Accessibility Tests

Located in `tests/accessibility/` directory.

**Technologies:**
- Axe-core for automated accessibility testing
- Pa11y for command-line accessibility testing
- Manual testing procedures

**Standards:**
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Color contrast requirements

### Load Tests

Located in `tests/load/` directory.

**Technologies:**
- Artillery for load testing
- Configurable load patterns
- Performance metrics collection

## Running Tests

### All Tests

```bash
# Run all test suites
npm run test:all

# Run tests for CI/CD
npm run test:ci
```

### Individual Test Types

```bash
# Unit tests
npm run test
npm run test:watch
npm run test:coverage

# End-to-end tests
npm run test:e2e
npm run test:e2e:ui

# Performance tests
npm run test:performance

# Accessibility tests
npm run test:accessibility

# Load tests
npm run test:load
```

### Development Testing

```bash
# Watch mode for unit tests
npm run test:watch

# Interactive E2E testing
npm run test:e2e:ui

# Debug mode
npm run test -- --debug
```

## Test Environment Setup

### Prerequisites

1. **Node.js 18+**
2. **PostgreSQL** (for integration tests)
3. **Environment Variables**

### Environment Configuration

Create `.env.test` file:

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/email_marketing_test

# Supabase (Test Instance)
NEXT_PUBLIC_SUPABASE_URL=your_test_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_test_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_test_service_role_key

# Test User Credentials
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=testpassword

# External Services (Test/Mock)
AWS_ACCESS_KEY_ID=test_access_key
AWS_SECRET_ACCESS_KEY=test_secret_key
PERPLEXITY_API_KEY=test_api_key
```

### Database Setup

```bash
# Create test database
createdb email_marketing_test

# Run migrations
npm run db:migrate:test

# Seed test data
npm run db:seed:test
```

### Browser Setup (E2E Tests)

```bash
# Install Playwright browsers
npx playwright install

# Install system dependencies
npx playwright install-deps
```

## Writing Tests

### Unit Test Example

```typescript
// src/lib/__tests__/email.test.ts
import { sendEmail } from '../email';
import { mockSESClient } from '../../__mocks__/aws-sdk';

describe('Email Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should send email successfully', async () => {
    const mockSend = jest.fn().mockResolvedValue({
      MessageId: 'test-message-id'
    });
    mockSESClient.send = mockSend;

    const result = await sendEmail({
      to: '<EMAIL>',
      subject: 'Test Subject',
      html: '<p>Test content</p>'
    });

    expect(result.messageId).toBe('test-message-id');
    expect(mockSend).toHaveBeenCalledTimes(1);
  });
});
```

### E2E Test Example

```typescript
// tests/e2e/campaign-creation.spec.ts
import { test, expect } from '@playwright/test';

test('should create campaign with AI assistance', async ({ page }) => {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  await page.fill('[data-testid="password-input"]', 'testpassword');
  await page.click('[data-testid="login-button"]');

  await page.goto('/campaigns');
  await page.click('[data-testid="create-campaign"]');
  
  await page.fill('[data-testid="campaign-name"]', 'Test Campaign');
  await page.click('[data-testid="ai-generate"]');
  
  await expect(page.locator('[data-testid="ai-suggestions"]')).toBeVisible();
});
```

### Performance Test Example

```typescript
// tests/performance/bulk-operations.test.ts
import { performance } from 'perf_hooks';

test('should process 1000 emails within 5 seconds', async () => {
  const startTime = performance.now();
  
  const result = await processBulkEmails(mockContacts.slice(0, 1000));
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  expect(duration).toBeLessThan(5000);
  expect(result.processed).toBe(1000);
});
```

## CI/CD Integration

### GitHub Actions Workflow

The CI/CD pipeline runs automatically on:
- Push to `main` or `develop` branches
- Pull requests to `main`

### Pipeline Stages

1. **Linting and Code Quality**
2. **Unit Tests with Coverage**
3. **Build Verification**
4. **End-to-End Tests**
5. **Accessibility Tests**
6. **Performance Tests**
7. **Security Scanning**
8. **Deployment (Staging/Production)**
9. **Load Testing (Staging)**

### Required Secrets

Configure these secrets in GitHub repository settings:

```
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY
VERCEL_TOKEN
VERCEL_ORG_ID
VERCEL_PROJECT_ID
SNYK_TOKEN
LHCI_GITHUB_APP_TOKEN
```

## Performance Testing

### Benchmarks

- **Email Processing**: 1000 emails in < 5 seconds
- **Database Queries**: < 500ms for complex queries
- **API Response Time**: < 200ms for standard requests
- **Memory Usage**: < 100MB increase during bulk operations

### Monitoring

```typescript
// Performance monitoring example
const startTime = performance.now();
const result = await bulkOperation();
const endTime = performance.now();

console.log(`Operation took ${endTime - startTime} milliseconds`);
```

### Load Testing Scenarios

1. **Authentication Load**: 50 concurrent users
2. **Contact Management**: 20 concurrent operations
3. **Campaign Sending**: 10 concurrent campaigns
4. **Analytics Queries**: 30 concurrent requests

## Accessibility Testing

### Automated Testing

```bash
# Run Pa11y accessibility tests
npm run test:accessibility

# Run Axe tests in Playwright
npm run test:e2e -- --grep "accessibility"
```

### Manual Testing Checklist

- [ ] Keyboard navigation works throughout the application
- [ ] Screen reader announces all important information
- [ ] Color contrast meets WCAG AA standards
- [ ] Form labels are properly associated
- [ ] Error messages are accessible
- [ ] Focus indicators are visible
- [ ] Images have appropriate alt text

### WCAG 2.1 AA Compliance

Our application must meet WCAG 2.1 AA standards:

- **Perceivable**: Information must be presentable in ways users can perceive
- **Operable**: Interface components must be operable
- **Understandable**: Information and UI operation must be understandable
- **Robust**: Content must be robust enough for various assistive technologies

## Load Testing

### Test Scenarios

1. **Warm-up Phase**: 5 users/second for 1 minute
2. **Ramp-up Phase**: 10 users/second for 2 minutes
3. **Sustained Load**: 20 users/second for 5 minutes
4. **Cool-down Phase**: 5 users/second for 1 minute

### Performance Thresholds

- **Response Time**: 95% of requests < 2 seconds
- **Error Rate**: < 1% of requests fail
- **Throughput**: Handle 20 requests/second sustained

### Monitoring Metrics

- Response time percentiles (p50, p95, p99)
- Error rates by endpoint
- Memory and CPU usage
- Database connection pool utilization

## Maintenance

### Regular Tasks

#### Daily
- Monitor test execution in CI/CD
- Review failed test reports
- Update test data as needed

#### Weekly
- Review test coverage reports
- Update performance benchmarks
- Clean up test artifacts

#### Monthly
- Update testing dependencies
- Review and update test scenarios
- Performance baseline updates
- Accessibility audit

### Test Data Management

```bash
# Reset test database
npm run test:db:reset

# Generate fresh test data
npm run test:data:generate

# Clean up test artifacts
npm run test:cleanup
```

### Debugging Failed Tests

#### Unit Tests
```bash
# Run specific test file
npm test -- email.test.ts

# Run with verbose output
npm test -- --verbose

# Debug mode
npm test -- --debug
```

#### E2E Tests
```bash
# Run in headed mode
npx playwright test --headed

# Debug specific test
npx playwright test --debug campaign-creation.spec.ts

# Generate trace
npx playwright test --trace on
```

### Performance Regression Detection

Monitor these metrics for regressions:

- Test execution time increases > 20%
- Memory usage increases > 50MB
- Response time increases > 100ms
- Coverage decreases > 5%

### Updating Test Dependencies

```bash
# Update testing dependencies
npm update @playwright/test @testing-library/react jest

# Update browser versions
npx playwright install

# Verify compatibility
npm run test:all
```

## Best Practices

### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests independent and isolated

### Test Data
- Use factories for test data generation
- Clean up after each test
- Use realistic but anonymized data
- Avoid hardcoded values

### Assertions
- Use specific assertions
- Test both positive and negative cases
- Include edge cases and error conditions
- Verify side effects

### Maintenance
- Regularly review and update tests
- Remove obsolete tests
- Keep test documentation current
- Monitor test performance

## Troubleshooting

### Common Issues

#### Test Timeouts
- Increase timeout values for slow operations
- Use proper wait conditions
- Check for memory leaks

#### Flaky Tests
- Add proper wait conditions
- Use deterministic test data
- Avoid time-dependent assertions

#### CI/CD Failures
- Check environment variables
- Verify service dependencies
- Review resource limits

### Getting Help

- Check test logs and error messages
- Review documentation and examples
- Consult team members
- Create issues for persistent problems

---

For more information, see:
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Playwright Documentation](https://playwright.dev/)
- [Testing Library Documentation](https://testing-library.com/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)