# Security Implementation

This document outlines the security measures implemented in the email marketing platform.

## Security Features Implemented

### 1. Input Sanitization and XSS Protection

- **HTML Sanitization**: Uses DOMPurify to sanitize HTML content in email campaigns
- **Text Sanitization**: Removes dangerous characters and scripts from text inputs
- **Email Validation**: Validates and sanitizes email addresses
- **URL Validation**: Ensures only safe URLs are processed

**Location**: `src/lib/security.ts`

### 2. CSRF Protection and Security Headers

- **CSRF Tokens**: Generates and validates CSRF tokens for state-changing requests
- **Security Headers**: Implements comprehensive security headers including:
  - Content Security Policy (CSP)
  - X-Frame-Options
  - X-Content-Type-Options
  - Referrer-Policy
  - X-XSS-Protection

**Location**: `src/middleware.ts`

### 3. Rate Limiting

- **API Rate Limiting**: Different limits for different endpoint types:
  - Auth endpoints: 5 requests/minute
  - Send endpoints: 10 requests/minute
  - Tracking endpoints: 1000 requests/minute
  - Default: 100 requests/minute

**Location**: `src/middleware.ts`

### 4. Audit Logging

- **Comprehensive Logging**: Tracks all sensitive operations including:
  - Authentication events
  - Campaign operations
  - Contact management
  - Settings changes
  - Security events

**Location**: `src/lib/audit.ts`

### 5. Data Export and Compliance

- **GDPR Compliance**: Full data export functionality
- **Right to be Forgotten**: Complete data deletion capability
- **Audit Trail**: Maintains logs for compliance purposes

**Location**: `src/lib/data-export.ts`

## Database Security

### Audit Logs Table

```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS)

- Users can only access their own audit logs
- Service role can insert audit logs
- No updates allowed on audit logs for integrity
- Automatic cleanup after 2 years

## API Security

### Protected Routes

All API routes implement:
- Authentication validation
- Input sanitization
- Audit logging
- Error handling that doesn't expose sensitive information

### Example Implementation

```typescript
// Campaign creation with security measures
export async function POST(request: NextRequest) {
  // Authentication check
  const authResult = await authService.getCurrentUser(request)
  if (!authResult.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // Input validation and sanitization
  const body = await request.json()
  const validatedData = createCampaignSchema.parse(body)

  // Business logic
  const result = await campaignService.createCampaign(campaignData)

  // Audit logging
  await logAuditEvent({
    user_id: authResult.user.id,
    action: AuditAction.CAMPAIGN_CREATED,
    resource_type: ResourceType.CAMPAIGN,
    resource_id: result.data.id,
    details: { campaign_name: validatedData.name },
    ...getRequestMetadata(request)
  })

  return NextResponse.json({ campaign: result.data }, { status: 201 })
}
```

## Frontend Security

### Compliance UI Components

- **Data Export Component**: Allows users to export their data
- **Data Deletion Component**: Provides secure account deletion
- **Security Settings**: Manages security preferences

**Location**: `src/components/compliance/`

## Testing

Security tests cover:
- Input sanitization functions
- Validation schemas
- Audit logging (basic functionality)
- Data export/deletion error handling

**Location**: `src/lib/__tests__/security.test.ts`

## Environment Variables

Ensure these environment variables are set securely:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# AWS SES Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=

# Perplexity API
PERPLEXITY_API_KEY=
```

## Security Best Practices

1. **Never expose sensitive data** in error messages
2. **Use environment variables** for all secrets
3. **Validate all inputs** on both client and server
4. **Log security events** for monitoring
5. **Implement proper authentication** on all protected routes
6. **Use HTTPS** in production
7. **Regular security audits** of dependencies
8. **Monitor audit logs** for suspicious activity

## Deployment Security

- Use secure headers in production
- Enable HTTPS/TLS
- Configure proper CORS policies
- Set up monitoring and alerting
- Regular security updates

## Compliance Features

- **GDPR**: Data export and deletion
- **Audit Trail**: Complete activity logging
- **Data Retention**: Configurable retention policies
- **User Consent**: Proper consent management
- **Data Minimization**: Only collect necessary data

This security implementation provides a solid foundation for a production email marketing platform while maintaining compliance with data protection regulations.